/**
 * @file: webpack remote config
 * <AUTHOR>
 * @date 2024-07-15 15:08:30
 */

import {PROXY_PORT} from '../proxyConfig';

// * -------------------------------------------------------------------------------------------------
// * 新增模块时在 RemoteModule 中添加 并加端口号就可以
// * -------------------------------------------------------------------------------------------------

enum FcRemoteModule {
    manageCenter = 'manageCenter',
    commonLibs = 'commonLibs',
    commonApps = 'commonApps',
    dataCenter = 'dataCenter',
    assetsCenter = 'assetsCenter',
    toolsCenter = 'toolsCenter',
}

enum FeedRemoteModule {
    feedCommonLibs = 'feedCommonLibs',
    feedManageCenter = 'feedManageCenter',
    feedDataCenter = 'feedDataCenter',
}

enum JmyRemoteModule {
    remoteMaterialSDK = 'remoteMaterialSDK',
}


/**
 * ! ↓ 这部分是没有用到的模块，但是由于managecenter 里有引用，所以多配置一个用于代理
 */
enum FcUnUsedProxyModule {
    analysisApp = 'analysisApp',
}

enum FeedUnUsedProxyModule {
    feedToolsCenter = 'feedToolsCenter',
}
/**
 * ! ↑ 这部分是没有用到的模块，但是由于managecenter 里有引用，所以多配置一个用于代理
 */

const PORT_CONFIG: Record<FcRemoteModule | FeedRemoteModule | FcUnUsedProxyModule | FeedUnUsedProxyModule, number> = {
    [FcRemoteModule.manageCenter]: 8093,
    [FcRemoteModule.commonLibs]: 8101,
    [FcRemoteModule.commonApps]: 8102,
    [FcRemoteModule.dataCenter]: 8091,

    [FcRemoteModule.assetsCenter]: 8092,
    [FcRemoteModule.toolsCenter]: 8090,
    [FcUnUsedProxyModule.analysisApp]: 8099,

    [FeedRemoteModule.feedCommonLibs]: 8801,
    [FeedRemoteModule.feedManageCenter]: 8806,
    [FeedRemoteModule.feedDataCenter]: 8803,

    [FeedUnUsedProxyModule.feedToolsCenter]: 8805,
};


// * -------------------------------------------------------------------------------------------------

const fengchaoCDN = 'https://tuiguang-s3.bdstatic.com/';
const feedCDN = 'https://feedads.baidu.com/';
const JMY_CDN = 'https://wutong-cdn.baidu.com/';

const fengchaoPreOnline = 'https://fengchao.baidu-int.com/';
const feedPreOnline = 'https://feedads.baidu-int.com/';


const fcPublicPath = {
    TEST: '/',
    PREONLINE: fengchaoPreOnline,
    ONLINE: fengchaoCDN,
} as const;


const feedPublicPath = {
    // 提测临时修改，需要改feed线下 bfe @jiawen
    TEST: '/',
    PREONLINE: feedPreOnline,
    ONLINE: feedCDN,
} as const;

const FENGCHAO_BASE_TEMPLATES = {
    'dev': 'http://dev.fengchao.baidu.com:${port}/',
    'test': `http://fctest.baidu.com:${PROXY_PORT}/`,
    'dev-test': 'http://dev.fctest.baidu.com:${port}/',
    'online': fengchaoCDN,
    'dev-online': 'http://dev.fengchao.baidu.com:${port}/',
    'preonline': fengchaoPreOnline,
    'dev-preonline': 'http://dev.fengchao.baidu-int.com:${port}/',
};

const FEED_BASE_TEMPLATES = {
    'dev': 'http://dev.feedads.baidu.com:${port}/',
    'test': `http://fcfeed.baidu.com:${PROXY_PORT}/`,
    'dev-test': 'http://dev.fcfeed.baidu.com:${port}/',
    'online': feedCDN,
    'dev-online': 'http://dev.feedads.baidu.com:${port}/',
    'preonline': feedPreOnline,
    'dev-preonline': 'http://dev.feedads.baidu-int.com:${port}/',
};

const fcRemoteStage = process.env.FC_REMOTE_STAGE || 'online';
const feedRemoteStage = process.env.FEED_REMOTE_STAGE || 'online';


// 检测remote stage 是否在配置中
if (!(fcRemoteStage in FENGCHAO_BASE_TEMPLATES)) {
    throw new Error(`不合法的 fcRemoteStage: ${fcRemoteStage}， 请到 .env.local 中配置`);
}

if (!(feedRemoteStage in FEED_BASE_TEMPLATES)) {
    throw new Error(`不合法的 feedRemoteStage: ${feedRemoteStage}， 请到 .env.local 中配置`);
}

// 辅助函数：生成配置
const generateConfig = (template: string, module: keyof typeof PORT_CONFIG) => {
    if (template.includes('${port}')) {
        return template.replace('${port}', PORT_CONFIG[module].toString());
    }
    return template;
};

const FENGCHAO_BASE_CONFIG = Object.entries(FENGCHAO_BASE_TEMPLATES).reduce((acc, [stage, template]) => {
    acc[stage] = Object.values(FcRemoteModule).reduce((moduleAcc, module) => {
        moduleAcc[module] = generateConfig(template, module);
        return moduleAcc;
    }, {} as Record<FcRemoteModule, string>);
    return acc;
}, {} as Record<string, Record<FcRemoteModule, string>>);


const FEED_BASE_CONFIG = Object.entries(FEED_BASE_TEMPLATES).reduce((acc, [stage, template]) => {
    acc[stage] = Object.values(FeedRemoteModule).reduce((moduleAcc, module) => {
        moduleAcc[module] = generateConfig(template, module);
        return moduleAcc;
    }, {} as Record<FeedRemoteModule, string>);
    return acc;
}, {} as Record<string, Record<FeedRemoteModule, string>>);


const FC_UN_USED_CONFIG = Object.entries(FENGCHAO_BASE_TEMPLATES).reduce((acc, [stage, template]) => {
    acc[stage] = Object.values(FcUnUsedProxyModule).reduce((moduleAcc, module) => {
        moduleAcc[module] = generateConfig(template, module);
        return moduleAcc;
    }, {} as Record<FcUnUsedProxyModule, string>);
    return acc;
}, {} as Record<string, Record<FcUnUsedProxyModule, string>>);

const FEED_UN_USED_CONFIG = Object.entries(FEED_BASE_TEMPLATES).reduce((acc, [stage, template]) => {
    acc[stage] = Object.values(FeedUnUsedProxyModule).reduce((moduleAcc, module) => {
        moduleAcc[module] = generateConfig(template, module);
        return moduleAcc;
    }, {} as Record<FeedUnUsedProxyModule, string>);
    return acc;
}, {} as Record<string, Record<FeedUnUsedProxyModule, string>>);


const fcBaseUrlConfig = FENGCHAO_BASE_CONFIG[fcRemoteStage];
const feedBaseUrlConfig = FEED_BASE_CONFIG[feedRemoteStage];


const fcUnUsedBaseUrlConfig = FC_UN_USED_CONFIG[fcRemoteStage];
const feedUnUsedBaseUrlConfig = FEED_UN_USED_CONFIG[feedRemoteStage];

const allConfig = {
    ...fcBaseUrlConfig,
    ...feedBaseUrlConfig,
    ...fcUnUsedBaseUrlConfig,
    ...feedUnUsedBaseUrlConfig,
};

/**
 * 开发环境代理路径重写
 * 把所有微前端模块都用 /${key}/ 来访问
 * 示例：
 * /commonLibs/commonLibs.js  =>  http://dev.fengchao.baidu.com:8101/commonLibs/commonLibs.js
 */
export const PROXY_PATH_REWRITE = Object.entries(allConfig).reduce((acc, [key, value]) => {
    acc[`/${key}/`] = `${value}${key}/`;
    return acc;
}, {
    // feed里还引了基木鱼的模块联邦
    '/material/static/': 'https://wutong-cdn.baidu.com/material/static/',
} as Record<string, string>);

function categorizeByValue(config: Record<string, string>) {
    // 首先按value值分组
    const groupedByValue = Object.entries(config).reduce<Record<string, string[]>>((acc, [key, value]) => {
        if (!acc[value]) {
            acc[value] = [];
        }
        acc[value].push('/' + key);
        return acc;
    }, {});

    // 转换成目标格式
    return Object.entries(groupedByValue).map(([target, contexts]) => ({
        context: contexts,
        target: target,
        secure: false,
        changeOrigin: true,
    }));
}
export const proxyPathRewriteForRsapck = categorizeByValue({
    ...allConfig,
    'material/static': 'https://wutong-cdn.baidu.com/',
});

const jmyModuleFileNameConfig = {
    [JmyRemoteModule.remoteMaterialSDK]: `material/static/js/${JmyRemoteModule.remoteMaterialSDK}.js`,
} as Record<string, string>;
const jmyRemotesConfig = Object.entries(JmyRemoteModule).reduce((acc, [key, module]) => {
    acc[key] = `${key}@${JMY_CDN}${jmyModuleFileNameConfig[module]}`;
    return acc;
}, {} as Record<string, string>);

/**
 * 开发环境远程模块配置
 * 示例：
 * commonLibs@/commonLibs/commonLibs.js
 */
export const remotesForDev = {
    ...Object.keys(allConfig).reduce((acc, key) => {
        acc[key] = `${key}@/${key}/${key}.js`;
        return acc;
    }, {} as Record<string, string>),
    ...jmyRemotesConfig,
};


/**
 * 构建环境远程模块配置
 * 示例：
 * commonLibs@https://tuiguang-s3.bdstatic.com/commonLibs/commonLibs.js
 */
export const remotesForBuild = {
    ...Object.keys(fcBaseUrlConfig).reduce((acc, key) => {
        acc[key] = `${key}@${fcPublicPath[process.env.STAGE as keyof typeof fcPublicPath || 'ONLINE']}${key}/${key}.js`;
        return acc;
    }, {} as Record<string, string>),
    ...Object.keys(feedBaseUrlConfig).reduce((acc, key) => {
        // eslint-disable-next-line max-len
        acc[key] = `${key}@${feedPublicPath[process.env.STAGE as keyof typeof feedPublicPath || 'ONLINE']}${key}/${key}.js`;
        return acc;
    }, {} as Record<string, string>),
    ...jmyRemotesConfig,
};
