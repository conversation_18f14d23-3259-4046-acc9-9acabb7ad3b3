/**
* created automatically by script because mock file not exist.
* <AUTHOR>
*/

// eslint-disable-next-line no-unused-vars
module.exports = function mock(params) {
    return {
        'data': [
            {
                'adviceId': 217,
                'adviceKey': 'addCampaignFeed',
                'adviceType': 8,
                'adviceName': '新增方案获取更多转化',
                'adviceOutline': '系统自动理解当前投放业务，并学习同行高质量创意',
                'logicDesc': '为您新建方案并衍生优质素材，提升跑量能力',
                'effect': {
                    'effectMap': {
                        'improveConversionInc': {
                            'key': 'improveConversionInc',
                            'value': 0.1,
                            'unit': 'week',
                            'desc': '预估转化量增幅',
                            'logic': '该方案收益是基于历史数据和竞价情况的预估，不代表实际效果，预估仅供参考',
                        },
                        'improveConversion': {
                            'key': 'improveConversion',
                            'value': 1,
                            'unit': 'week',
                            'desc': '预估新增转化量',
                            'logic': '该方案收益是基于历史数据和竞价情况的预估，不代表实际效果，预估仅供参考',
                        },
                    },
                },
                'haveDetailPage': true,
                'operations': [
                    163,
                    164,
                    165,
                    166,
                    167,
                ],
                'attributes': {
                    'campaignCount': 1,
                },
                'settings': {},
                'productLine': 'feed',
                'aixSortPriority': 86,
            },
            {
                'adviceId': 213,
                'adviceKey': 'removeLowQualityVideoFeed',
                'adviceType': 5,
                'adviceName': '调整视频素材避免展现受限',
                'adviceOutline': '系统发现您的账户中有2个视频素材存在明显的用户体验不佳问题，导致广告展现量受限',
                'logicDesc': '建议您删除这些视频素材，并补充更多质量较好的视频进行投放，让广告获得充分展现，收获更好的投放效果',
                'effect': {
                    'effectMap': {
                        'materialCount': {
                            'key': 'materialCount',
                            'value': 2,
                            'desc': '展现受限的视频素材数',
                            'logic': '',
                        },
                        'relationCreativeCnt': {
                            'key': 'relationCreativeCnt',
                            'value': 5,
                            'desc': '当前关联创意数',
                            'logic': '',
                        },
                    },
                },
                'haveDetailPage': true,
                'operations': [
                    106,
                    107,
                    41,
                ],
                'attributes': {
                    'relationCreativeCnt': 5,
                    'materialCount': 2,
                },
                'settings': {},
                'productLine': 'feed',
                'aixSortPriority': 86,
            },
            {
                'adviceId': 214,
                'adviceKey': 'addFeedCreativePictureConversion',
                'adviceType': 5,
                'adviceName': '新增创意图片获取更多转化',
                'adviceOutline': '您的账户中有2个单元的创意在获得流量能力上低于行业平均水平',
                'logicDesc': '建议您及时更新创意提高转化量，为您推荐了2条创意供选用',
                'effect': {
                    'effectMap': {
                        'improveConversion': {
                            'key': 'improveConversion',
                            'value': 2,
                            'unit': 'week',
                            'desc': '预估转化量',
                            'logic': '',
                        },
                        'improveClick': {
                            'key': 'improveClick',
                            'value': 60,
                            'unit': 'week',
                            'desc': '预估点击量',
                            'logic': '',
                        },
                    },
                },
                'haveDetailPage': true,
                'operations': [
                    110,
                    111,
                    40,
                    176,
                    177,
                ],
                'attributes': {
                    'materialCount': 2,
                },
                'settings': {},
                'productLine': 'feed',
                'aixSortPriority': 86,
            },
            {
                'adviceId': 205,
                'adviceKey': 'modAuditRejectMaterialFeed',
                'adviceType': 0,
                'adviceName': '调整审核不通过物料',
                'adviceOutline': '您有38个创意、81个应用未通过审核',
                'logicDesc': '请及时调整审核不通过的物料，以免影响投放',
                'effect': {
                    'effectMap': {
                        'restoreImpression': {
                            'key': 'restoreImpression',
                            'value': '恢复展现',
                            'desc': '预估效果',
                            'logic': '',
                        },
                    },
                },
                'haveDetailPage': true,
                'operations': [
                    29,
                    6,
                ],
                'attributes': {
                    'materialCount': 119,
                    'creativeCount': 38,
                    'appCount': 81,
                },
                'settings': {},
                'productLine': 'feed',
                'aixSortPriority': 17,
            },
        ],
        'expand': {},
        'status': 0,
        'errors': [],
    };
};

