/**
* created automatically by script because mock file not exist.
* <AUTHOR>
*/

// eslint-disable-next-line no-unused-vars
module.exports = function mock(params) {
    return {
        'data': {
            'adviceId': 215,
            'adviceKey': 'addFeedCreativeVideoConversion',
            'acceptInfo': {
                'items': [
                    {
                        'adgroupFeedId': 4621610039,
                        'adviceCreativeFeedId': 23420,
                        'creativeMd5': 'qingduo_9a965fc2e00d2986d528f61028d47da8',
                    },
                    {
                        'adgroupFeedId': 4631539800,
                        'adviceCreativeFeedId': 23417,
                        'creativeMd5': 'qingduo_9a965fc2e00d2986d528f61028d47da9',
                    },
                ],
            },
        },
        'expand': {},
        'status': 0,
        'errors': [],
    };
};

