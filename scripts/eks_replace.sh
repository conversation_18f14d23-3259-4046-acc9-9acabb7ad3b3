#!/bin/bash

# 代码参考：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/gv0WKdV3ZF/60incnF1Zd/vKYExjFiGcSpLE

# 如果代码出错把下面的注释去掉，可以看到代码的执行过程进行调试，如果是接口报错找 luogang05
# set -x


readonly JOBDIR=$(pwd)


# 从 JOBDIR/.env.local 文件中获取环境变量
function get_env_value() {

    # 如果没有env文件报错并退出
    if [[ ! -f ${JOBDIR}/.env.local ]]; then
        echo "No .env.local file found Please copy from .env.local.example and fill in the correct values"
        exit 1
    fi

    local key=$1
    local value=$(grep "^${key}=" ${JOBDIR}/.env.local | cut -d '=' -f2)
    echo $value
}


# 从 .env.local 文件中获取提交人的用户名和EOS环境ID
realUserName=$(get_env_value "EKS_USER_NAME")
EOS_ID=$(get_env_value "EOS_ID")


# 如果找不到对应的环境变量，就报错后退出
if [[ -z "$realUserName" ]]; then
    echo "EKS_USER_NAME is not set in .env.local"
    exit 1
fi

if [[ -z "$EOS_ID" ]]; then
    echo "EOS_ID is not set in .env.local"
    exit 1
fi




readonly PROJNAME='fe-cangjie'
readonly OUTPUT_DIR=${JOBDIR}/output
readonly file_path=${OUTPUT_DIR}/${PROJNAME}.tar.gz

# 部署组名称，等于EOS_ID 加一个0
deployApp=cpd-fe-cangjie-${EOS_ID}0

filename=$(basename $file_path)



# 下面的步骤为调用jarvis的接口，可以不关注，后续可能会有变动或者下线


# 检测操作系统类型
if [[ "$OSTYPE" == "darwin"* ]]; then
    # MacOS系统
    signature=$(md5 -q "$file_path")
else
    # 假设其他系统为Linux
    signature=$(md5sum "$file_path" | awk '{print $1}')
fi

file_size=$(du -k "$file_path" | awk '{print $1}')

echo "step1：开始上传应用程序包"
if ((file_size >= 102400)); then
  splitNum=$(echo "$file_size / 92160 + 1" | bc)
  fileSuffix="_part_"
  split -b 94371840 -d "$file_path" ${file_path}${fileSuffix}
  if [[ $? -ne 0 ]]; then
    split -b 94371840 "$file_path" ${file_path}${fileSuffix}
    count=0
    for letter in {a..z}; do
      if [[ -f ''${file_path}${fileSuffix}'a'$letter'' ]]; then
        mv ''${file_path}${fileSuffix}'a'$letter'' ''${file_path}${fileSuffix}'00'$count''
        ((count++))
      fi
    done
  fi
  response=$(curl --location --request POST 'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile' \
    -F 'params={"userName": "Comate","realUserName": "'"$realUserName"'","fileName":"'"$filename"'","comateTaskMap":{"deployApp":"'"$deployApp"'"}}' \
    -F 'token=e13393ffa785c021e261117beff5a445' \
    -F 'userid=630152' \
    -F 'optid=630152' \
    -F 'signature='$signature'' \
    -F 'filename='$filename'' \
    -F 'step=InitiateMultipartUpload' \
    --compressed \
    --insecure)
  uploadId=$(echo $response | grep -o '"uploadId":"[^"]*"' | awk -F'"' '{print $4}')
  parts=""
  for i in $(seq 1 $splitNum); do
    result=$((i - 1))
    filexx='@'${file_path}${fileSuffix}'0'$result''
    if [[ -f ''${file_path}${fileSuffix}'00'$result'' ]]; then
      filexx='@'${file_path}${fileSuffix}'00'$result''
    fi
    data=$(curl --location --request POST 'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile' \
      -F 'params={"userName": "Comate","realUserName": "'"$realUserName"'","fileName":"'"$filename"'","comateTaskMap":{"deployApp":"'"$deployApp"'"}}' \
      -F 'token=e13393ffa785c021e261117beff5a445' \
      -F 'userid=630152' \
      -F 'optid=630152' \
      -F 'step=UploadPart' \
      -F 'uploadId='$uploadId'' \
      -F 'file='$filexx'' \
      -F 'partNumber='$i'' \
      --compressed \
      --insecure)
    ETag=$(echo $data | grep -o '"ETag":"[a-zA-Z0-9]*"' | sed 's/"ETag":"//;s/"//')
    parts+='{"partNumber":"'"$i"'","ETag":"'"$ETag"'"},'
  done
  parts=$(echo $parts | sed 's/,$//')
  partsxxx=[$parts]
  uploadResponse=$(curl --location --request POST 'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile' \
    -F 'params={"userName": "Comate","realUserName": "'"$realUserName"'","fileName":"'"$filename"'","comateTaskMap":{"deployApp":"'"$deployApp"'"}}' \
    -F 'token=e13393ffa785c021e261117beff5a445' \
    -F 'userid=630152' \
    -F 'optid=630152' \
    -F 'uploadId='$uploadId'' \
    -F 'filename='$filename'' \
    -F 'signature='$signature'' \
    -F 'step=CompleteMultipartUpload' \
    -F 'parts='$partsxxx'' \
    --compressed \
    --insecure)
  rm -rf ${file_path}${fileSuffix}0*
else
  uploadResponse=$(curl --location --request POST 'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/uploadFile' \
    -F 'params={"userName": "Comate","realUserName": "'"$realUserName"'","fileName":"'"$filename"'","comateTaskMap":{"deployApp":"'"$deployApp"'"}}' \
    -F 'token=e13393ffa785c021e261117beff5a445' \
    -F 'userid=630152' \
    -F 'optid=630152' \
    -F 'signature='$signature'' \
    -F 'file=@'$file_path'' \
    --compressed \
    --insecure)
fi

echo $uploadResponse

uploadStatus=$(echo $uploadResponse | grep -o '"status":[0-9]*' | cut -d':' -f2)

if [ "$uploadStatus" -eq 0 ]; then
  echo "—— 上传成功"
  echo ""

  echo "step2：Jarvis制作镜像准备上线中，监控上传任务状态中，请等待"
  mapId=$(echo $uploadResponse | grep -o '"mapId":[0-9]*' | cut -d':' -f2)
  for ((i = 1; i <= 30; i++)); do
    syncResponse=$(curl -s --location --request POST 'https://api.baidu-int.com/json/sms/service/JarvisMscFileUploadService/getComateTaskDetail' \
      -H "Content-Type: application/json;charset=UTF-8" \
      -d '{"header":{"userid":630152,"optid":630152,"token":"e13393ffa785c021e261117beff5a445"},"body":{"userName": "Comate","realUserName":"'$realUserName'","mapId":'$mapId'}}' \
      --compressed \
      --insecure)
    syncStatus=$(echo $syncResponse | grep -o '"status":[0-9]*' | cut -d':' -f2)
    if [ "$uploadStatus" -eq 0 ]; then
      taskId=$(echo $syncResponse | grep -o '"taskId":[0-9]*' | cut -d':' -f2)
      if [ -n "$taskId" ] && [ "$taskId" -gt 0 ]; then
        echo "—— 制作镜像成功，已发起Jarvis上线任务，上线任务ID："$taskId
        echo ""
        break
      fi
    else
      echo "—— 获取上传任务状态失败"
      break
    fi
    sleep 10
  done
else
  echo "—— 上传失败"
  exit 0
fi

bashOver=0
if [ -n "$taskId" ] && [ "$taskId" -gt 0 ]; then
  echo "step3：监控Jarvis上线任务中，请等待上线任务结束，上线任务ID："$taskId
  for ((i = 1; i <= 30; i++)); do
    taskResponse=$(curl -s --location --request POST 'https://api.baidu-int.com/json/sms/service/JarvisMscTaskService/getWorkflowDeployTaskDetail' \
      -H "Content-Type: application/json;charset=UTF-8" \
      -d '{"header":{"userid":630152,"optid":630152,"token":"e13393ffa785c021e261117beff5a445"},"body":{"paasType":"eks","userName":"Comate","realUserName":"'$realUserName'","taskId":'$taskId'}}' \
      --compressed \
      --insecure)
    resStatus=$(echo $taskResponse | grep -o '"status":[0-9]*' | cut -d':' -f2)
    if [ "$resStatus" -eq 0 ]; then
      taskStatus=$(echo $taskResponse | grep -o '"taskStatus":"[a-zA-Z0-9]*"' | sed 's/"taskStatus":"//;s/"//')
      if [ "$taskStatus" == "SUCCESS" ]; then
        echo "—— Jarvis任务上线成功，上线任务ID："$taskId
        bashOver=1
        break
      fi
    else
      echo "—— 调用上线任务接口失败，上线任务ID："$taskId
      break
    fi
    sleep 10
  done
else
  echo "—— 获取Jarvis上线任务ID超时"
  exit 0
fi

if [ -n "$bashOver" ] && [ "$bashOver" -gt 0 ]; then
  echo ""
else
  echo "—— Jarvis上线任务超时，请进入Jarvis页面查看任务详情，上线任务ID："$taskId
fi
