require('@reskript/config-lint/patch');

module.exports = {
    extends: require.resolve('@reskript/config-lint/config/eslint'),
    rules: {
        '@typescript-eslint/no-unused-vars': 'warn',
        'no-unused-vars': 'warn',
        '@typescript-eslint/no-use-before-define': ['error', {'functions': false}],
        'comma-dangle': ['warn', 'always-multiline'],
        '@reskript/hooks-deps-new-line': 'off',
        'react/jsx-no-bind': 'off',
        'no-underscore-dangle': 'off',
        'no-undef-init': 'warn',
        '@typescript-eslint/init-declarations': 'warn',
        '@reskript/spell-check': 'off',
        '@typescript-eslint/no-empty-interface': 'off',
        'camelcase': 'warn',
    },
};
