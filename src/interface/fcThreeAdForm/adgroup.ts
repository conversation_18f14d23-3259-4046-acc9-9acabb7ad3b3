import {FcAppInfo} from '@/api/appInfo';
import {FcKeywordType} from '@/components/common/NewKeyword/keywordGenerator/config';
import {FC_ADGROUP_STATUS_ENUM} from '@/dicts/fcAdgroup';
import {FC_MARKET_TARGET, FC_SUB_MARKET_TARGET} from '@/dicts/marketingTarget';
import {FcBidTypeEnum, FcOcpcBidTypeEnum} from '@/dicts/ocpc';
import {ShopType} from '@/dicts/fcShopType';
import {FcAdType} from '@/dicts/fcCampaign';
import {AnchorItem} from '@/interface/campaign';
import {AdLinkType} from '@/dicts/adUrlType';
import {RELATED_PRODUCT_TYPE} from '@/dicts/relatedProductsType';


export enum FcAdgroupOrCampaignStatusEnum {
    OPEN = 1,
    CLOSE = 0,
    BY_CAMPAIGN = 2,
}
export enum StructuredCategoryStatusEnum {
    BY_ADGROUP = 0, // 按单元设置
    BY_CAMPAIGN = 2,
    BY_PROJECT = 3,
}

export enum AppShopDirectStatusEnum {
    CLOSED = 0,
    OPEN = 1,
}

export interface FcAdgroupType extends AnchorItem {
    adgroupId: number; // 建完的单元有id
    adgroupName: string;
    adgroupUrlType: AdLinkType; // 广告链接类型 0:落地页 1:商家智能体
    adgroupAgentUrl?: string; // 商家智能体类型
    adgroupAgentParam?: string; // 商家智能体落地页监测后缀
    adgroupAutoTargetingStatus: boolean; // 单元层级自动定向
    segmentRecommendStatus: boolean; // 单元层级自动图片优化
    creativeTextOptimizationStatus: boolean; // 单元层级自动文案优化
    productCategoryType: number; // 绑定的产品库类型 1教育 2汽车 3医美 4旅游 5医疗
    structuredProductIds: number[]; // 销售线索营销目标单元的商品ID
    structuredContentIds: number[]; // 销售线索营销目标单元的内容库ID
    structuredContentIdStrs: string[]; // 对于法律行业id是string类型，所以新增字段
    structuredProductIdStrs: string[]; // 同上
    maxPrice: number;
    newPcFinalUrl: string; // pc落地页
    newMobileFinalUrl: string; // 移动落地页
    catalogId: number;
    appShopDirectStatus: 0 | 1; // 应用商店直达状态
    productSetId?: number;
    productSetName?: string;
    relatedProducts: RELATED_PRODUCT_TYPE;

    /**
     * !用来判断是否是按方案设置 如果要修改请不要用这个字段
     * 而是用 segmentRecommendStatus 传 true 或者 false
     */
    newAdgroupSegmentRecommendStatus: FcAdgroupOrCampaignStatusEnum;
    /**
     * !用来判断是否是按方案设置 如果要修改请不要用这个字段
     * 而是用 creativeTextOptimizationStatus 传 true 或者 false
     */
    newAdgroupCreativeTextOptimizationStatus: FcAdgroupOrCampaignStatusEnum;
    /**
     * !用来判断是否是按方案设置 如果要修改请不要用这个字段
     * 而是用 adgroupAutoTargetingStatus 传 true 或者 false
     */
    newAdgroupAutoTargetingStatus: FcAdgroupOrCampaignStatusEnum;

    pause: boolean;
    status: FC_ADGROUP_STATUS_ENUM;

    campaignBidType: FcBidTypeEnum;
    projectOcpcBidType: FcOcpcBidTypeEnum;
    newAdgroupStructuredCategoryType: StructuredCategoryStatusEnum;
    shopType: ShopType;
    adType: FcAdType;
    marketingTargetId: FC_MARKET_TARGET;
    adgroupAppBinds: {
        androidBindType: CBind;
        iosBindType: CBind;
        hongmengBindType: CBind;
    };
    anchorId: number;
    subMarketingTargetId: FC_SUB_MARKET_TARGET;
    itemsHelper: {
        fromAix: boolean;
        updateType?: number;
    };
    storeName?: string;
}


export interface CBind extends FcAppInfo {
    bidType: number;
    bidRatio: number;
}

export interface FcAdgroup {
    adgroupType: Partial<FcAdgroupType>;
    keywordTypes: FcKeywordType[];
    cbind: CBind;
}
