import {ageConfig} from '@/config/dataInsight';
import {ReportType, PRODUCT_TYPE_CONFIG} from '@/config/report';
import {MaterialList} from './tableList';

type AgeId = keyof typeof ageConfig;
export type ReportResponseItem = Partial<{
    date: string; // 日期
    hour: string; // 小时
    userId: string | number; // 账户ID
    campaignId: number; // 营销ID
    campaignNameStatus: string; // 营销名称
    click: number; // 点击
    cost: number; // 消费
    impression: number; // 展现
    cpc: number; // 平均点击价格
    ctr: number; // 点击率
    cpm: number; // 千次展现消费
    ocpcBid: number; // ocpc出价
    platform: string; // 投放网络
    ocpcTargetTransRatio: number; // 目标转化率
    ocpcTargetTransCPC: number; // 目标转化成本
    ocpcTargetTrans: number; // 目标转化量
    ocpcTransType: string; // 目标转化
    userName: string; // 账户
    ageId: AgeId; // 年龄
    genderId: number; // 性别
    provinceName: string; // 省份
    lastweekClickRate: number;
    lastweekCostRate: number;
    lastweekOcpcTargetTransRate: number;
    lastweekOcpcTargetTransCPCRate: number;
    totalCount: number;
    /**
     * 次日留存转化率
     */
    aixOcpcConversionsDetail28CVR: number;
    /**
     * 7日留存转化率
     */
    aixOcpcConversionsDetail29CVR: number;

}>;


export interface ReportDataResponse<T extends ReportResponseItem = ReportResponseItem> {
    rows: T[];
    rowCount: number;
    totalRowCount: number;
    summary: {
        date: string;
        impression: number;
        click: number;
        cost: number;
        ocpcTargetTrans: number;
        cpc: number;
        ctr: number;
        cpm: number;
        ocpcBid: number;
        ocpcTargetTransRatio: number;
        ocpcTargetTransCPC: number;
        ocpcTransType: string; // 目标转化
        upperRatio: number;
        lowerRatio: number;
        lastweekClickRate: number;
        lastweekCostRate: number;
        lastweekOcpcTargetTransRate: number;
        lastweekOcpcTargetTransCPCRate: number;
    };
}

export interface FilterItem {
    column: string;
    operator: string;
    values: Array<string | number>;
}

export interface sortItem {
    column: string;
    sortRule: string;
}

export interface FetchParams {
    token: string;
    reportType: ReportType;
    userIds?: Array<number | string>;
    startDate: string;
    endDate: string;
    timeUnit: string;
    columns: string[];
    sorts?: sortItem[];
    filters?: FilterItem[];
    startRow?: number;
    rowCount?: number;
    needSum?: boolean;
    addZeroRows?: boolean;
    splitColumn?: unknown;
    compareStartDate?: string;
    compareEndDate?: string;
    needCache?: boolean;
    withColumnMeta?: boolean;
    topCount?: number;
}

export interface FetchConfigParams {
    token: string;
    reportType: number;
    dataAnalysis?: boolean;
    withNewCategory?: boolean;
}

export interface ColumnConfigsProps {
    columnName: string;
    columnText: string;
    sortable?: boolean;
    visible?: boolean;
    columnType?: MaterialList.ColumnType;
    precision?: number;
    isPercentage?: boolean;
    optional?: boolean;
    commentKey?: string;
    filterable?: boolean;
    draggable?: boolean;
    category?: string;
    feConfig?: {
        fixType?: 'left';
        requestTo?: string;
        columnWidth?: number | string;
        columnMinWidth?: number;
        draggable?: boolean;
        composites?: string;
        align?: 'left' | 'right' | 'center';
        filterType?: MaterialList.FilterType;
        filter?: string;
    };
}

export interface ReportConfigResponse {
    appId?: number;
    reportType?: number;
    reportName?: string;
    version?: string;
    comparable?: boolean;
    splitable?: boolean;
    downloadable?: boolean;
    smartFilterable?: boolean;
    columnConfigs: Record<string, ColumnConfigsProps>;
    requiredColumns?: string[];
    visibleColumns?: string[];
    defaultVisibleColumns: string[];
    customColumns: string[];
    splitColumns?: string[];
    smartFilterColumns?: string[];
    timeUnits?: string[];
    maxDateRange?: number;
    supportCustomizeColumn?: boolean;
    newColumnCategories?: ColumnCategory[];
    templateId?: number;
    templateColumns?: string[];
    columnCategories?: ColumnCategory[];
    columnGroups?: ColumnCategory[];
    haveDataColumns?: string[];
}

export interface ColumnCategory {
    name: string;
    columns: string[];
    subCategories?: Array<{
        name: string;
        columns: string[];
    }>;
}

export type SortType = 'ascend' | 'descend' | '';
export interface Sorter {
    sortType: SortType;
    sortField: string;
}

export interface SdkFilterProps {
    campaignIds?: Array<string | number>;
    projectIds?: Array<string | number>;
    dateRange?: {
        startDate: string;
        endDate: string;
    };
    timeUnit?: string;
    productLineList?: PRODUCT_TYPE_CONFIG[];
    videoIdeaType?: number;
    projectType?: string;
    device?: number;
}
