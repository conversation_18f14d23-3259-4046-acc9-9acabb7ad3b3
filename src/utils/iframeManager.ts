/* eslint-disable @typescript-eslint/member-ordering */
import {SoftNavigationEventType, isValidBaiduOrigin} from 'commonLibs/utils/iframe';
import {PLATFORM_ENUM} from '@/dicts';

// iframe管理器状态
interface IframeManagerState {
    isVisible: boolean;
    container: HTMLElement | null;
    iframe: HTMLIFrameElement | null;
    currentTargetContainer: HTMLElement | null; // 当前目标容器
    isInitialized: boolean; // 是否已初始化
    scrollListener?: () => void; // 滚动监听器
    messageListener?: (event: MessageEvent) => void; // 消息监听器
    resizeObserver?: ResizeObserver; // 尺寸变化监听器
    pendingMessage?: { routePath: string, options?: Record<string, any> }; // 待发送的消息
    isIframeLoaded: boolean; // iframe是否已加载完成
    platform: PLATFORM_ENUM;
}
// 软导航消息接口
export interface SoftNavigationMessage {
    type: SoftNavigationEventType;
    url?: string;
    routePath?: string;
    options?: {
        extraSearch?: Record<string, any>;
    };
}

export const defaultIframeStyle = `
    width: 100%;
    height: 100%;
    border: none;
    margin: 0;
    padding: 0;
`;

class IframeManager {
    private readonly state: IframeManagerState = {
        isVisible: false,
        container: null, // 容器
        iframe: null, // iframe
        currentTargetContainer: null,
        isInitialized: false, // 是否已初始化
        scrollListener: undefined,
        resizeObserver: undefined,
        pendingMessage: undefined,
        isIframeLoaded: false,
        messageListener: undefined,
        platform: PLATFORM_ENUM.FC,
    };

    constructor(platform: PLATFORM_ENUM) {
        this.state.platform = platform;
    }
    /**
     * 初始化iframe并加载指定URL
     * 使用 CSS 定位避免重新加载
     */
    initAndLoad(iframeUrl: string, options?: {
        container?: HTMLElement;
        containerStyle?: string;
        iframeStyle?: string;
        enableDynamicPositioning?: boolean;
    }): void {
        if (this.state.isInitialized) {
            return;
        }

        // 创建全局容器（始终固定在 body 上）
        this.state.container = document.createElement('div');
        this.state.container.id = 'singleton-iframe-container';
        this.state.container.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: transparent;
            z-index: 10;
            display: none;
        `;

        // 创建 iframe
        this.state.iframe = document.createElement('iframe');
        this.state.iframe.style.cssText = `
            width: 100%;
            height: 100%;
            border: none;
            margin: 0;
            padding: 0;
        `;
        this.state.iframe.frameBorder = '0';
        this.state.iframe.src = iframeUrl;
        this.state.messageListener = (event: MessageEvent) => {
            this.state.isIframeLoaded = true;
            if (this.state.pendingMessage
                && event.data.type === 'IFRAME_READY'
                && event.data.platform === this.state.platform
                && isValidBaiduOrigin(event.origin)) {
                this.sendMessageToIframe(this.state.pendingMessage.routePath, this.state.pendingMessage.options);
                this.state.pendingMessage = undefined;
            }
        };

        window.addEventListener('message', this.state.messageListener);

        // 将 iframe 添加到容器
        this.state.container.appendChild(this.state.iframe);
        document.body.appendChild(this.state.container);

        // 标记为已初始化
        this.state.isInitialized = true;

        // 如果指定了容器，移动到该容器
        if (options?.container) {
            this.moveToContainer(options.container, {
                containerStyle: options.containerStyle,
                iframeStyle: options.iframeStyle,
                enableDynamicPositioning: options.enableDynamicPositioning,
            });
        }
    }

    /**
     * 向iframe发送消息的私有方法
     */
    private sendMessageToIframe(routePath: string, options?: Record<string, any>): void {
        if (this.state.iframe?.contentWindow) {
            // 发送软导航消息
            this.state.iframe.contentWindow.postMessage({
                type: SoftNavigationEventType.ROUTE_CHANGE,
                routePath,
                options,
            }, '*');
        }
    }

    /**
     * 软导航 - 通知iframe内部进行路由切换
     * 主要使用方式，避免重新加载iframe
     */
    softNavigate(routePath: string, options?: {
        container?: HTMLElement;
        extraSearch?: Record<string, any>;
        containerStyle?: string;
        iframeStyle?: string;
        enableDynamicPositioning?: boolean;
        iframeClassName?: string;
    }): void {
        // 如果iframe已加载完成，直接发送消息
        if (this.state.isIframeLoaded && this.state.iframe?.contentWindow) {
            this.sendMessageToIframe(routePath, options?.extraSearch);
        } else {
            // 如果iframe还没加载完成，保存待发送的消息
            this.state.pendingMessage = {
                routePath,
                options: options?.extraSearch,
            };
        }

        if (options?.container) {
            this.moveToContainer(options.container, {
                containerStyle: options.containerStyle,
                iframeStyle: options.iframeStyle,
                enableDynamicPositioning: options.enableDynamicPositioning,
                iframeClassName: options.iframeClassName,
            });
        }
    }

    /**
     * 显示iframe
     */
    show(): void {
        if (this.state.container) {
            this.state.container.style.display = 'block';
            this.state.isVisible = true;
        }
    }

    /**
     * 隐藏iframe
     */
    hide(): void {
        if (this.state.container) {
            this.state.container.style.display = 'none';
            this.state.isVisible = false;
        }
        // 清理监听器
        this.cleanupListeners();
    }

    /**
     * 将iframe移动到指定容器
     * 使用 CSS 定位避免重新加载，并添加滚动和尺寸变化监听
     */
    moveToContainer(container: HTMLElement, options?: {
        containerStyle?: string;
        iframeStyle?: string;
        enableDynamicPositioning?: boolean; // 是否启用动态定位（默认true）
        iframeClassName?: string;
    }): void {
        if (!this.state.container || !this.state.iframe) {
            return;
        }

        // 检查是否已经在目标容器中
        if (this.state.currentTargetContainer === container) {
            return; // 已经在目标容器中，不需要移动
        }

        // 清理之前的监听器
        this.cleanupListeners();

        // 记录当前目标容器
        this.state.currentTargetContainer = container;

        // 更新 iframe 位置
        this.updateIframePosition();
        this.show();

        // 设置 iframe 样式
        const defaultIframeStyle = `
            width: 100%;
            height: 100%;
            border: none;
            margin: 0;
            padding: 0;
        `;
        this.state.iframe.style.cssText = options?.iframeStyle || defaultIframeStyle;
        this.state.iframe.className = options?.iframeClassName || '';

        // 如果启用动态定位，添加监听器
        if (options?.enableDynamicPositioning !== false) {
            // 添加滚动监听器
            this.state.scrollListener = () => {
                this.updateIframePosition();
            };
            window.addEventListener('scroll', this.state.scrollListener, true);

            // 添加尺寸变化监听器
            this.state.resizeObserver = new ResizeObserver(() => {
                this.updateIframePosition();
            });
            this.state.resizeObserver.observe(container);
        }
    }

    /**
     * 手动更新 iframe 位置
     * 在特殊情况下可以调用此方法来强制更新位置
     */
    updatePosition(): void {
        this.updateIframePosition();
    }

    /**
     * 销毁管理器
     */
    destroy(): void {
        // 清理监听器
        this.cleanupListeners();
        if (this.state.messageListener) {
            window.removeEventListener('message', this.state.messageListener);
        }

        if (this.state.container && this.state.container.parentElement) {
            this.state.container.parentElement.removeChild(this.state.container);
        }
    }

    /**
     * 更新 iframe 位置和尺寸
     */
    private updateIframePosition(): void {
        if (!this.state.container || !this.state.iframe || !this.state.currentTargetContainer) {
            return;
        }

        const containerRect = this.state.currentTargetContainer.getBoundingClientRect();

        // 检查容器是否仍然可见
        if (containerRect.width === 0 || containerRect.height === 0) {
            return;
        }

        // 更新容器样式
        this.state.container.style.cssText = `
            position: fixed;
            top: ${containerRect.top}px;
            left: ${containerRect.left}px;
            width: ${containerRect.width}px;
            height: ${containerRect.height}px;
            background: transparent;
            z-index: 10;
            display: block;
            border: none;
            margin: 0;
            padding: 0;
            overflow: hidden;
        `;
    }

    /**
     * 清理监听器
     */
    private cleanupListeners(): void {
        // 清理滚动监听器
        if (this.state.scrollListener) {
            window.removeEventListener('scroll', this.state.scrollListener, true);
            this.state.scrollListener = undefined;
        }

        // 清理尺寸变化监听器
        if (this.state.resizeObserver) {
            this.state.resizeObserver.disconnect();
            this.state.resizeObserver = undefined;
        }
    }
}


// 创建单例实例
const FcIframeManager = new IframeManager(PLATFORM_ENUM.FC);

// 导出便捷方法
export const initIframe = (iframeUrl: string, options?: Parameters<typeof FcIframeManager.initAndLoad>[1]) => {
    FcIframeManager.initAndLoad(iframeUrl, options);
};

export const showIframe = () => {
    FcIframeManager.show();
};

export const hideIframe = () => {
    FcIframeManager.hide();
};

export const softNavigateIframe = (routePath: string, options?: Parameters<typeof FcIframeManager.softNavigate>[1]) => {
    FcIframeManager.softNavigate(routePath, options);
};

export const navigateIframe = (iframePath: string, options?: Parameters<typeof FcIframeManager.initAndLoad>[1]) => {
    FcIframeManager.initAndLoad(iframePath, options);
};

export const updateIframePosition = () => {
    FcIframeManager.updatePosition();
};
