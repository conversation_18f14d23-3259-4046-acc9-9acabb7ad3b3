/* eslint-disable no-console */
/**
 * @file weirwood 异常/自定义性能上报
 * <AUTHOR>
 */

import {Client} from '@baidu/weirwood-sdk';
import {isProductionOnline} from '../index';

interface APIException {
    url: string;
    params: Record<string, any>;
    response: any;
    method?: string;
    status?: number;
}

declare const window: Window & typeof globalThis & {
    __Weirwood: Client;
    currentSpaNav: string;
};

const shouldCapture = isProductionOnline || location.origin === 'https://qingge.baidu.com';

export function captureAPIError({url, params, response, method = 'post', status}: APIException) {
    if (shouldCapture) {
        window.__Weirwood?.error?.captureAPIException({
            url, params, response, method, responseHeader: '',
            status: typeof status === 'number' && status < 0 ? status : response?.data?.status,
        });
    }
    else {
        console.log(url, params, response, response?.data?.status);
    }
}

export const ensureError = (e: unknown): Error => {
    if (e instanceof Error) {
        return e;
    }
    if (typeof e === 'object' && e !== null) {
        return new Error(`Non-Error exception captured: ${JSON.stringify(e)}`);
    }
    return new Error(`Non-Error thrown: ${String(e)}`);
};

export function captureError(e: unknown) {
    if (shouldCapture) {
        const safeError = ensureError(e);
        window?.__Weirwood?.error?.setContext({errorPage: true}, {persist: false});
        window?.__Weirwood?.error?.captureException(safeError);
    }
    else {
        console.log(e);
    }
}

export function markCustomPerf(mark: string) {
    window.performance?.clearMarks(mark);
    window.performance?.mark(mark);
}

export function measureCustomPerf(mark: string, name?: string) {
    const startMark = `^_${mark}`;
    if (!name) {
        window.performance?.clearMarks(startMark);
        window.performance?.mark(startMark);
        return;
    }

    const endMark = `$_${mark}`;
    const measureName = `x_qingge_${name}`; // 在大池子里，所以必须加项目前缀
    try {
        window.performance?.mark(endMark);
        window.performance?.measure(measureName, startMark, endMark);
        // window.performance?.clearMeasures(measureName);
        window.performance?.clearMarks(startMark);
        window.performance?.clearMarks(endMark);
    }
    catch (e) {
        // 之前的 mark 被清理，说明非智能体触发，API 性能自然会统计，无需端到端
    }
}

// weirwood 自定义指标直接上报
export function addCustomUserPerf(name: string, duration: number) {
    if (window.__Weirwood?.perf && shouldCapture) {
        try {
            window.__Weirwood.perf.addUserTiming({
                [name]: duration,
            });
        } catch (e) {
            // 处理可能的 measure 失败
        }
    }
    else {
        console.log('weirwood addUserTiming', name, duration);
    }
}


// 按导航类型分别统计性能指标，兼容软硬导航，并生成两种指标，spa和hard
export function addPerf(name: string) {
    if (!window.__Weirwood?.perf || !shouldCapture) {
        return;
    }

    try {
        const navigationType = window.currentSpaNav === 'spa_hard' ? 'hard' : 'spa';
        // 上报指标，自动添加 x_qingge_ 前缀,和软硬导航后缀
        window.__Weirwood.perf.addUserTiming({
            [`x_qingge_${name}_${navigationType}`]: Date.now() - (window as any).__Weirwood.lastRouteTime,
        });
    } catch (e) {
        // 处理可能的失败
    }
}

interface IResourceTimingLoggerConfig {
    resourceName?: string; // PerformanceResourceTiming的name，精确匹配
    isSendResourceTiming?: (entry: PerformanceResourceTiming) => boolean; // 需要上报的资源，自定义匹配方式
    isMultiple?: boolean; // 是否匹配多个资源，作为同一个指标上报。如果传false，则只取第一个匹配的资源
    customPerfName: string; // 自定义指标名称，需要在weirwood上进行配置
}
/**
 * 收集PerformanceResourceTiming数据并作为自定义指标上报。
 * @param config - 配置数组，每个配置项包含资源名称、自定义匹配方式、是否匹配多个资源以及自定义指标名称。
 */
export function addCustomResTimingLogger(config: IResourceTimingLoggerConfig[]) {
    // eslint-disable-next-line max-len
    const entries: PerformanceResourceTiming[] = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
    config.forEach(
        ({resourceName: name = '', isSendResourceTiming, isMultiple, customPerfName}) => {
            if (
                !customPerfName
                || (!name && !isSendResourceTiming)
            ) {
                return;
            }
            let matchedEntries = entries.filter(
                i => i.name === name || isSendResourceTiming?.(i),
            );
            if (!isMultiple) {
                matchedEntries = matchedEntries.slice(0, 1);
            }
            // 如果是匹配了多个资源，将多个资源视作一个资源，取fetchStart和responseEnd的区间
            const [fetchStart, responseEnd] = matchedEntries.reduce(
                (acc, cur) => {
                    if (cur.fetchStart < acc[0]) {
                        acc[0] = cur.fetchStart;
                    }
                    if (cur.responseEnd > acc[1]) {
                        acc[1] = cur.responseEnd;
                    }
                    return acc;
                },
                [matchedEntries[0]?.fetchStart, matchedEntries[0]?.responseEnd]
            );
            if (responseEnd - fetchStart > 0) {
                addCustomUserPerf(customPerfName, responseEnd - fetchStart);
            }
        }
    );
}
