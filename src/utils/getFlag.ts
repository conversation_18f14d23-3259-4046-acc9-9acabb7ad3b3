import globalData from '@/utils/globalData';

export function getFlag(key: string) {
    const flag = globalData.get('bizHags')?.[key];
    return flag == null || !!flag;
}

export function getOptHagsFlag(key: string) {
    const flag = globalData.get('optHags')?.[key];
    return flag == null || !!flag;
}

export function getOptOrUserFlag(key: string) {
    return getFlag(key) || getOptHagsFlag(key);
}

export const isMockScript = () => !!localStorage.getItem('is_internal_user');
export const isCampaignDraftUser = () => getFlag('qingge_campaign_draft_user');
export const isCampaignUnLimitUser = () => getFlag('aix_ngtpacket_unlimited_user');

export const isAccountRegionUser = () => getFlag('aix_accout_region_target_2024');

export const isAccountRegionPacketUser = () => getFlag('aix_accout_region_target_2024');

export const isRegionExcludeUser = () => getFlag('aix_fc_region_exclude_user');

export const isAixFeedNotransidUser = () => getFlag('aix_feed_notransid_user');

export const isNegativeLimitUser = () => getFlag('negative_limit_user');

export const isFeedSupportThirdRegionUser = () => getFlag('aix_feed_exclude_region_user');

// 项目-精细化出价-人群质量
export const isProjectMainflowCrowdUser = () => getFlag('fuhe-fc-mainflow-bypopulation-user');
// 项目-精细化出价-时段、地域、年龄
export const isProjectMainflowScheduleUser = () => getFlag('fc-mainflow-time-region-age-user');
// 项目-精细化出价
export const isProjectMainflowUser = () => isProjectMainflowCrowdUser() || isProjectMainflowScheduleUser();

export const isSoftLinkFeedProjectUser = () => getFlag('aix_feed_project_migrate');

export const isEducationUser = () => getFlag('fc_qingge_education');
export const isCplClueUser = () => getFlag('qinggecpl_user');

export const isMedicalUser = () => getFlag('yiliao_structured_user');

export const isFeedCreativeListUser = () => getFlag('aix_creative_list_users_feed');

export const isFeedCreativeTextListUser = () => getFlag('feed_creative_text_list_users');

export const isVideoBlockUser = () => getFlag('aix_white_video_shield');

// feed精细化出价-人群质量-默认
export const isFeedProjectMainflowDefaultCrowdUser = () => getFlag('fuhe-feed-mainflow-bypopulation-user');
// feed精细化出价-人群质量-联合识别
export const isFeedProjectMainflowCombineCrowdUser = () => getFlag('feed-mainflow-bypopulation-combion-user');
// feed精细化出价-时段、地域、年龄
export const isFeedProjectMainflowScheduleUser = () => getFlag('feed-mainflow-time-region-age-user');
// feed精细化出价入口
export const isFeedProjectMainflowUser = () => (
    isFeedProjectMainflowDefaultCrowdUser()
    || isFeedProjectMainflowCombineCrowdUser()
    || isFeedProjectMainflowScheduleUser()
);

// 全局录屏白名单
export const isGlobalRecordUser = () => getFlag('dom_record_user');

export const isFcOcpcBaiduAppTransTypeUser = () => getFlag('fc_ocpc_baiduapp_user');

export const isFeedAiMaxUser = () => isFeedProjectMainflowUser() || isSoftLinkFeedProjectUser();

export const isFeedMinBudgetUser = () => getFlag('feed-budget-limit');

export const isFeedShareBudgetUser = () => getFlag('feed-share-budget');

export const isFeedLGUIProjectUser = () => getFlag('aix_feed_project_user');

export const isIosItunesIdSameUser = () => getFlag('feed_itunesid_same_user');

// 服务直达-业务点小流量名单
export const isFcRfqClueInfoUser = () => getFlag('fc-cpl-user');
// 服务直达-业务词小流量名单
export const isFcRfqWordUser = () => getFlag('fc_rfq_word_user');
// 服务直达名单
export const isFcCplUser = () => isFcRfqClueInfoUser() || isFcRfqWordUser();

export const isFeedCplUser = () => getFlag('feed_cpl_plan_userids');

export const isRfqUser = () => getFlag('clue_enhance_rfq');

export const isFeedDpaRtaUser = () => getFlag('feed-dpa-rta-user');

// AI智选人群（idmp人群）名单
export const isAiIdmpCrowdUser = () => getFlag('fc-idmp-1-user');
export const isFcIdmpUser2 = () => getFlag('fc-idmp-2-user');
export const isFeedIdmpUser3 = () => getFlag('feed-idmp-3');

export const isCompassAccountMoveGroup = () => getOptHagsFlag('compass_account_move_group');
export const isCompassWanhuatongIndependent = () => getOptHagsFlag('compass_wanhuatong_independent');
export const isAixWanhuatongUser = () => getOptHagsFlag('chaoguanwanhuatong');

export const isAixAIHelper = () => getFlag('aix_ai_helper_user');

// 支持添加8000个否词的名单
export const isNegativeWord8kNumUser = () => getFlag('fc_negative_word_expand_8QIAN_user');

export const isNegativeMergeUser = () => getFlag('negative_merge_fix_user');
export const isClueRfqUser = () => getFlag('clue_enhance_rfq_new');

export const isGeographicLocation = () => getFlag('fc_geographic_location');

export const isNegativeWordUpgradeUser = () => getFlag('fc_negative_word_upgrate_user');
// AIMAX 信息流智能起量
export const isProjectAiLiftUser = () => getFlag('feed-project-liftbudget');

// AIMAX 信息流智能起量-多段时间预约
export const isFeedAiLiftMoreStartTimeUser = () => getFlag('feed-liftbudget-more-starttime');

export const isFeedProjectBudgetUpdradeUser = () => getFlag('feed-share-budget-editable');

// 应用推广营销目标支持pc
export const isAppPcUser = () => getFlag('fc_app_support_pc_user');

export const isIntelligentInvestmentUser = () => getFlag('aix_intelligent_investment_user');

export const isFeedThreeLevelUser = () => getFlag('feed-three-level-user');

// 信息流三级计划自定义创意
export const isFeedCustomCreativeUser = () => getFlag('feed-custom-creative-user');

// 排除人群新增过滤时间【12个月】
export const isCrowdRecentDayYearUser = () => getFlag('conversion_recentdays_twelve_month_user');

// 新增排除人群支持36个月的名单
export const isMaxThreeYearExclusionUser = () => getFlag('fc_max_36months_exclusion_user');

export const isNegativeListUser = () => getFlag('new_aix_negative_list_user');

export const isNegativeMatchTypeUser = () => false; // getFlag('aix_negative_match_type_user');

// 老否词清理名单
export const isOldNegativeWordClearUser = () => getFlag('fc-negative-bod-user');

export const isNegativeShieldTypeUser = () => getFlag('negative_word_break_through_small_flow');
// 转化资产 搜索名单
export const isFcAssetUser = () => getFlag('fc-conversion-tracking-remode-list');
// 转化追踪和转化资产兼容名单（已推全）
export const isFcTransAndAssetFuheUser = () => getFlag('fuhe-both-fc-conversion-tracking-remode-list');

// 根据组件强推需要圈选的目标转化类型(强投)-新建项目
export const isMultipleTransSpecifiedUser = () => getFlag('fc_multi_transtype_bid_force_user');
// 根据组件强推需要圈选的目标转化类型(强投)-编辑项目
export const isStockProjectMultipleTransSpecifiedUser = () => getFlag('fc_qiangtou_cunliang');
export const isFeedStdVideoList = () => getFlag('feed_standard_video_list_white');
export const isFeedStdVideoOpList = () => getFlag('feed_standard_video_operation_white');
// 搜索接入线索直播场景投放
export const isFcLiveUser = () => getFlag('fc_zhibo_user');

export const isRoiUser = () => getFlag('fc_ocpc_roi_user');
// 落地页使用智能体
export const isFcAgentUrlUser = () => getFlag('fc_unit_agent_user');
// 支持智能体轮值
export const isFcAgentUser = () => getFlag('fc_agent_user');
export const isFcAgentRotatingShieldUser = () => getFlag('fc_agent_rotating_shield_user');

export const isMtDbProviderUser = () => getFlag('swr_db_cache_provider_user');
export const isFcCreativeVideoUser = () => getFlag('fc_video_restructure_smallflow');

// 应用推广接入鸿蒙
export const isFcHarmonyOSUser = () => getFlag('fc-harmonyos-user');

// AIMAX大健康行业版
export const isFeedAimaxMedical = () => getFlag('aimax_health_version');
export const isAiMaxSmartControlUser = () => getFlag('aimax_smart_control_small_flow');

// AIMAX行业版-商服版
export const isFeedAimaxBusiness = () => getFlag('feed_aimax_business_small_flow');

export const isAiBuildAdvancedStyleUser = () => true;
export const isFcDistrictsUser = () => getFlag('fc_plan_districts_trade');
// tabs标签页小流量名单
export const isFcMaterialTabsUser = () => getFlag('fc_material_tabs_user');

// 信息流主流程小流量
export const isFeedPromotionMainUser = () => getFlag('feed_promotion_main_compose_user');
// 信息流服务直达小流量
export const isFeedPromotionCplCampaignUser = () => getFlag('feed_promotion_cpl_campaign_user');

// q4大转盘活动小流量名单
export const isPrizeActivityUser = () => getFlag('prize_activity_2024');
export const isPrizeActivityWinnerUser = () => getFlag('prize_activity_winner_2024');

export function isAiBuildUser() {
    return !!globalData.get('canUseSmartAdBuild');
}


export const isAIBuildFastBrandBuildUser = () => {
    return true;
    // 先注释 明天再看
    // const unitInfo = globalData.get('unitInfo') as {
    //     firstUnitId: number;
    //     firstUnitName: string;
    //     secondUnitId: number;
    //     secondUnitName: string;
    // } | undefined;

    // return unitInfo?.firstUnitId === 698;
};

// 应用推广直播
export const isAppLiveroom = () => getFlag('app_liveroom');
// 应用直投小流量名单
export const isAppInvestmentDefaultOpenUser = () => getFlag('app_direct_investment_default_open_user');
// 轻舸信息流百家号创编流程
export const isFeedBJHNew = () => getFlag('feed-bjh-new');
// 百家号新增项目层级深转
export const isFeedBJHProjectRoiType = () => isFeedBJHNew() && getFlag('feed-bjh-project-roiType');
// 百家号原生推广
export const isFeedBJHNative = () => isFeedBJHNew() && getFlag('feed-bjh-native');
// 百家号付费阅读
export const isFeedBjhPayToReadUser = () => getFlag('feed-bjh-pay-to-read');
// 创建爱采购加油推方案-有可投放的店铺和商品
export const isIcgLocEnoughUser = () => getFlag('acg_loc_enough');

// 创建爱采购加油推方案-有可投放的店铺，无商品
export const isIcgLocLackUser = () => getFlag('acg_loc_lack');

export const isDirectStatusBatchEditUser = () => getFlag('fc-list-batch-edit-app-user');
// 信息流应用推广支持鸿蒙系统
export const isFeedAppHarmonyOSUser = () => getFlag('feed-subject-harmony-app-download');
// 律效通产品类型
export const isLXTUser = () => getFlag('feed_lxt_user');
// 快速新建百青藤项目
export const isFeedBqtFastProject = () => getFlag('feed-bqt-fast-project');
// 法律标准类目
export const isLawIndustryUser = () => getFlag('fc_law_user');

// 轻舸首页行动建议优化交互展示(已推全)
export const isNewOptimizeAdviceUser = () => true;
// 系统自动刷为默认的uid
export const isAutoQinggeMoveUser = () => getFlag('aix_user_already_move_default');
// 返回旧版问卷users 同 小流量名单0102晚
export const isGoBacQuUser = () => getFlag('go_back_user');
// 一站式预先请求优化中心卡片名单，挑选了轻舸的ITTI较大的用户
export const isOnewebPrefetchOptApiUser = () => getFlag('oneweb_prefetch_queryoutline_user');
// 首页优化建议卡片分批请求小流量名单
export const isSplitOptApiUser = () => true;

// 项目-深度优化方式-优化转化价值-roi系数
export const isFcOcpcDeepRoiUser = () => getFlag('fuhe_fc_ocpc_roixishu_user');
// 出价管控名单
export const isNewOcpcLimitUser = () => getFlag('feed-budget-bid-new-rule');
// 短剧行业aimax
export const isFeedShortPlayIndustryAimaxUser = () => getFlag('feed-short-play-industry-aimax-user');

export const isFcTaskCenterUser = () => getFlag('fc_taskcenter_user');
// 搜索推广原生互动营销目标支持笔记营销场景
export const isFcNativeBJUser = () => getFlag('fc_native_bj_user');

// 诊断-超成本
export const isDiagnosisOverCostUser = () => getFlag('fc_diagnosis_over_cost_user');
// 诊断-超成本-是否支持选择今日
export const isDiagnosisOverCostTodayUser = () => getFlag('fc_diagnosis_over_cost_today_user');

// 短剧行业
export const isShortPlayIndustryUser = () => getFlag('UC_TRADE_SECOND-GROUP-2008-200812');
// 智能基建
export const isFeedSmartControlUser = () => getFlag('feed_template_smart_control_small_flow_fe');
export const isFcNativeDqaUser = () => getFlag('native_dqa_user');
export const isKxtProjectUser = () => getFlag('kxt_project_user');
/**
 * 极简投
 */
export const isLiteProjectUser = () => getFlag('fc_minimal_project_user');
export const isAIMaxBasedScene = () => getFlag('aimax_based_scene');
export const isAIMaxOvercostScene = () => getFlag('aimax_overcost_scene');
export const isAIMaxIntelligentPricing = () => getFlag('aimax_intelligent_pricing');

// 非商家智能体url类型落地页也允许填写商家智能体格式落地页
export const isAllowAgentUrlRegUser = () => getFlag('fc_agent_url_white_user');
// 调整推广设备卡片文案升级
export const isModCampaignEquiUser = () => getFlag('mod_campaign_equipment_text_inner');

// 百家号视频质量小流量名单
export const isBaiKanVideoUser = () => getFlag('baikan_video_user');
// 百青藤运营甄别适合wap变现的账户加入小流量名单。
export const isBaiqingtengWapAdUser = () => getFlag('baiqingteng_wap_ad');
// 新版GUI新建创意流程小流量名单
export const isNewCreativeGuiUser = () => getFlag('new_creative_gui_user');
// 「一键开启AIMax」跳转逻辑支持信息流&优化
export const isOneClickAimaxUser = () => getFlag('one_click_aimax_user');
// 大健康行业版自动盯盘调价功能
export const isAimaxHealthAutoruleUser = () => getFlag('aimax_health_new_auto_rule_small_flow');
// 创意url清理白名单(不在复合名单内的可继续使用url功能)
// idea_url_stage2 = idea_url_edit_user&(!idea_url_edit_white)
export const isIdeaUrlCleanWhite = () => !getFlag('idea_url_stage2');
// 旅效通自动生成产品名单
export const isAutoProductFlagUser = () => getFlag('lxt_auto_product_user');

// flashlink 名单
export const isFlashLinkUser = () => getFlag('aix_flash_link_user');

export const isDoublePriceIILevelUser = () => getFlag('fc_deeptcpa_chose');
// 智能搭建批量翻新名单
export const isBatchRefreshUser = () => true;
