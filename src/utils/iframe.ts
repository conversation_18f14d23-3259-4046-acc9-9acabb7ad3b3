import queryString from 'query-string';
import {PRODUCT} from '@/dicts/campaign';
import {appendQuery} from './route';
import {formatString} from './string';
import {getFcUrl, getFeedUrl} from './format/url';
import {getEnvPort, getUserId} from '.';

export const isInFeedIframe = (
    location.search.includes('in=iframe') && location.search.includes('host_app=feed')
);

export const getIframeUrl = (iframePath: string, options?: {
    product?: PRODUCT;
    extraSearch?: Record<string, any>;
    getUrl?: (url: string) => string;
}) => {
    const {
        product = PRODUCT.FC,
        extraSearch = {},
        getUrl,
    } = options || {};

    if (getUrl) {
        return getUrl(iframePath);
    }

    const iframeUrl = product === PRODUCT.FEED ? getFeedUrl(iframePath) : getFcUrl(iframePath);
    const url = formatString(iframeUrl, {userId: getUserId(), port: getEnvPort()});
    const {search, pathname, protocol, host} = new URL(url);

    return appendQuery(
        protocol + '//' + host + pathname,
        {
            ...queryString.parse(search),
            in: 'iframe',
            'host_app': 'qingge',
            from: 'qingge',
            ...extraSearch,
        }
    );
};
