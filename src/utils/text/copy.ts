import {Toast} from '@baidu/one-ui';

type CopyTextOptions =
    | boolean
    | {
          showToast?: boolean;
          successMessage?: string;
          errorMessage?: string;
      };

/**
 * 复制文本到剪贴板
 * @param text 需要复制的文本
 * @throws 如果复制失败会抛出错误
 */

export async function copyText(
    text: string,
    options?: CopyTextOptions
): Promise<boolean> {
    const {showToast, successMessage, errorMessage} = normalizeOptions(options);

    try {
        await tryClipboardCopy(text);
        if (showToast) {
            Toast.success({content: successMessage, showCloseIcon: false});
        }
        return true;
    } catch {
        if (showToast) {
            Toast.error({content: errorMessage, showCloseIcon: false});
        }
        return false;
    }
}

// ✅ 将选项标准化
function normalizeOptions(options?: CopyTextOptions) {
    if (typeof options === 'boolean') {
        return {
            showToast: options,
            successMessage: '已复制到剪贴板',
            errorMessage: '复制失败',
        };
    }
    return {
        showToast: options?.showToast ?? true,
        successMessage: options?.successMessage ?? '已复制到剪贴板',
        errorMessage: options?.errorMessage ?? '复制失败',
    };
}

// ✅ 尝试复制逻辑提取
async function tryClipboardCopy(text: string): Promise<void> {
    if (navigator.clipboard?.writeText) {
        await navigator.clipboard.writeText(text);
    } else {
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text: string) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
}
