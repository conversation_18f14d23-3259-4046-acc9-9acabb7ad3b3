/**
 * @file 异步请求封装
 * <AUTHOR>
 */

import axios, {AxiosRequestConfig, AxiosResponse} from 'axios';
import {identity} from 'lodash-es';
import qs from 'query-string';
import {Toast} from '@baidu/one-ui';
import {v4 as uuidv4} from 'uuid';
import {getToken, getRedirectUrl, getUserId} from '@/utils';
import {delay} from '../delay';
import {captureAPIError} from '../logger';
import SSE from './sse';
import {STATUS, canOpt} from './auth';
import {AxiosCanceller} from './axiosCanceller';

// 海若接口
const HAIRUO_AJAX_URL = '/hairuo/request.ajax';
// 设置 Content-Type
const contentType = 'application/x-www-form-urlencoded';
axios.defaults.headers['Content-Type'] = contentType;
const axiosCanceller = new AxiosCanceller();

type RequestStatus = {
    [K in keyof typeof STATUS]: typeof STATUS[K];
};
interface RequestError extends Error {
    status: RequestStatus[keyof typeof STATUS];
}

interface CustomAxiosRequestConfig {
    weirwoodIgnored?: boolean;
    isHairuo?: boolean;
}

const notHairuo = (path?: string) => path && ['ChatService/chat', 'ChatService/indexProxy'].includes(path);
function normalize(config: AxiosRequestConfig & CustomAxiosRequestConfig) {
    // 每次发送请求之前判断 state 中是否存在 token
    // 如果存在，则统一在 http 请求的header都加上token，这样后台根据token判断你的登录情况
    // 即使本地存在token，也有可能token是过期的，所以在响应拦截器中要对返回状态进行判断
    const reqid = uuidv4();
    const path = config.url;
    const data = qs.stringify({
        userid: getUserId(),
        token: getToken(),
        reqid,
        path,
        eventId: uuidv4(),
        params: JSON.stringify(config.data || {}),
        source: 'aix',
    });

    return {
        url: (config.isHairuo === false || notHairuo(path))
            ? path
            : `${HAIRUO_AJAX_URL}?path=${path}&reqid=${reqid}${config.weirwoodIgnored ? '&weirwoodIgnored=true' : ''}`,
        data,
    };
}

// 请求拦截器
axios.interceptors.request.use(
    config => {
        const canOptFlag = canOpt(config.url || '');
        if (canOptFlag != null && !canOptFlag) {
            const error = new Error('很抱歉，您暂无内测权限，无法执行此操作') as RequestError;
            error.status = STATUS.NOAUTH;
            throw error;
        }
        return {
            ...config,
            ...normalize(config),
        };
    },
    error => Promise.reject(error)
);

// 响应拦截器
axios.interceptors.response.use(
    response => {
        // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
        // 否则的话抛出错误
        if (response.status === 200 && response.data.status === 0) {
            return Promise.resolve(response);
        }
        if (shouldRetry<AxiosResponse<HaiRuoResponse<unknown>>>(
            response, {errorRetryCount: +response.config.headers['X-Retry']}
        )) {
            return Promise.reject({uShouldRetry: true, errorRetryCount: response.config.headers['X-Retry'] - 1});
        }
        return Promise.reject(response);
    },
    error => {
        if (shouldRetry<AxiosResponse<HaiRuoResponse<unknown>>>(
            error, {errorRetryCount: +error.config.headers['X-Retry']}
        )) {
            return Promise.reject({uShouldRetry: true, errorRetryCount: error.config.headers['X-Retry'] - 1});
        }
        return Promise.reject(error.response);
    }
);

function defaultOnSuccess<T>(res: HaiRuoResponse<T>, {
    apiPath, formData,
}: {apiPath: string, formData: Record<string, any>}) {
    if ('redirect' in res) {

        const url = res.redirecturl || getRedirectUrl();
        // 控制台打印 防止重定向后看不到
        if (process.env.STAGE === 'TEST') {
            // eslint-disable-next-line no-console
            console.log('%c%s', 'color: red;', `${apiPath} 请求失败，重定向到 ${url}`);
            // eslint-disable-next-line no-console
            console.log(JSON.stringify({apiPath, formData, res}, null, 2));
            // eslint-disable-next-line no-console
            console.log('请到 https://uc-off.baidu-int.com/auth/app/4009/advance/resource/manage 添加资源');
        }
        else {
            // eslint-disable-next-line no-console
            console.log(`${apiPath} 请求失败，重定向到 ${url}`);
        }

        window.location.href = url;
        axiosCanceller.removeAllPending();
        throw res;
    }
    return res.data;
}

interface ErrorItem {
    code: number;
    message: string;
}

export type HaiRuoResponse<T> = {
    status: number;
    data: T;
    errors: ErrorItem[];
} | {
    redirect: boolean;
    redirecturl: string;
};

const shouldErrorCodeToBeRetry = ({code}: {code: number}) => [
    88806, 99902, 99959, 99936, 99937, 99938, 99942, 99906, 99907, 99908, 99920, 99921, 99923, 99999, 2002,
].includes(code); // 海若错误码
const invalidUrlCode = [8710]; // The request url is invalid

export function getFailureMsg(data: any, {defaultErrorMsg = '系统开小差，请稍后再试'} = {}) {
    const errors = (data?.errors || []).map((item: ErrorItem) => {
        return shouldErrorCodeToBeRetry(item) || invalidUrlCode.includes(+item.code)
            ? '' : item.message; // 海若的码/不合法url的码，则不显示 msg，如果都是则走兜底文案
    });
    return errors.filter(identity).length ? errors.join('；') : defaultErrorMsg;
}

function defaultOnFailure(error: AxiosResponse & Error): never {
    if (error?.status === STATUS.NOAUTH) {
        Toast.error({content: error.message, duration: 3});
    }
    throw 'redirect' in (error ?? {}) ? error : error?.data;
}

export interface RequestOptions extends CustomAxiosRequestConfig {
    onSuccess?: typeof defaultOnSuccess;
    onFailure?: (error: AxiosResponse & Error) => never;
    errorRetryCount?: number;
    errorRetryInterval?: number;
    toastOnFailure?: boolean;
    defaultToastOnFailure?: string;
    timeout?: number;
    isHairuo?: boolean;
}
const DEFAULT_RETRY_COUNT = 0;

/**
 * request 方法，发送海若请求
 *
 * @param {string} apiPath 请求路径
 * @param {Object} formData 请求时携带的数据
 * @param {RequestOptions} options 配置
 * @return {Promise}
 */
export const request = <T>(
    apiPath: string,
    formData: Record<string, any> = {},
    options: RequestOptions = {}
): Promise<T> => {
    const {
        onSuccess = defaultOnSuccess,
        onFailure = defaultOnFailure,
        errorRetryCount = DEFAULT_RETRY_COUNT,
        errorRetryInterval = 500,
        toastOnFailure = true,
        timeout,
        weirwoodIgnored,
        isHairuo = true,
    } = options;
    const signal = axiosCanceller.addPending({url: apiPath, data: formData});
    const requestConfig: AxiosRequestConfig & CustomAxiosRequestConfig = {
        signal,
        headers: {'X-Retry': errorRetryCount},
    };
    if (timeout) {
        requestConfig.timeout = timeout;
    }
    if (weirwoodIgnored) {
        requestConfig.weirwoodIgnored = weirwoodIgnored;
    }
    if (!isHairuo) {
        requestConfig.isHairuo = isHairuo;
    }

    return axios.post<HaiRuoResponse<T>>(apiPath, formData, requestConfig)
        .then(res => onSuccess<T>(res.data, {apiPath, formData}))
        .catch(error => {
            // 上报
            if (!error?.redirect) {
                captureAPIError({url: apiPath, params: formData, response: error});
            }
            // 重试
            if (error?.uShouldRetry) {
                options.errorRetryCount = error.errorRetryCount;
                return delay(errorRetryInterval).then(() => request(apiPath, formData, options));
            }
            // 是否要默认的toast提示
            if (toastOnFailure && error?.data) {
                const errorMessages = getFailureMsg(error.data, {
                    defaultErrorMsg: options.defaultToastOnFailure || '系统开小差，请稍后再试',
                });
                Toast.error({content: errorMessages, duration: 3});
            }
            // 透传error
            return onFailure(error);
        });
};

type Callback = (obj: any) => void;
export interface StreamOptions {
    onFulfill: Callback;
    onError?: Callback;
    errorRetryCount?: number;
}

/**
 * 流式发送海若请求
 *
 * @param {string} apiPath 请求路径
 * @param {Object} formData 请求时携带的数据
 * @param {StreamOptions} options 回调/重试配置等
 */
export const stream = (apiPath: string, formData: Record<string, any>, options: StreamOptions) => {
    const {url, data} = normalize({
        url: apiPath,
        data: formData,
    });
    const source = new SSE(url, {
        headers: {'Content-Type': contentType, 'X-Hairuo-Sse': 'true'},
        method: 'POST',
        payload: data,
    });
    debugConsole([['start'], ['stream参数:', 'blue', formData]]);

    const {onFulfill, onError, errorRetryCount = DEFAULT_RETRY_COUNT} = options;
    function closeConnection() {
        source.removeEventListener('error', errorHandler);
        source.close();
    }

    function errorHandler(e: any) {
        debugConsole([['stream返回:', 'red', e], ['end']]);
        if (!e.redirect) {
            captureAPIError({url: apiPath, params: formData, response: e});
        }
        closeConnection();
        if (!shouldRetry({data: e} as AxiosResponse, {errorRetryCount})) {
            return onError?.(e); // 这里返回的不是 hairuo 格式，只是一个 event 对象。onError 里处理了差异
        }
        stream(apiPath, formData, {onFulfill, onError, errorRetryCount: errorRetryCount - 1});
    }

    source.addEventListener('message', (e: any) => {
        const response = JSON.parse(e.data || '{}');
        // 由于浏览器的控制台只能会显示原生 EventSource 对象的日志，这里只能在这打印返回值来 debug
        try {
            if (response.status === 0 || response.status === 200) { // 和海若的 status===0 一致
                const data = defaultOnSuccess<{done: boolean, tag?: number}>(response, {apiPath, formData});
                const {done, tag} = data;
                if (done) { // 规范，见 https://ku.baidu-int.com/d/Ga_JALVFbNUGpe
                    if (tag && tag < 0) { // 该字段为智能体自定义的非稳定返回
                        captureAPIError({url: apiPath, params: formData, response, status: tag});
                    }
                    closeConnection();
                }
                debugConsole([['stream返回:', 'green', response], ['end']]);
                return onFulfill(data);
            }
            errorHandler(response);
        }
        catch (error) {
            errorHandler(error);
        }
    });

    source.addEventListener('error', errorHandler);
    source.stream();
    return source;
};

/* eslint-disable  no-console */
function debugConsole(consoles: Array<[string] | [string, string, any]>) {
    if (process.env.NODE_ENV !== 'production' || process.env.STAGE !== 'ONLINE') {
        const dashedLine = '-'.repeat(20);
        consoles.forEach(([text, color = 'gray', extra]) => {
            if (text === 'start') {
                console.log(`%c${dashedLine}开始${dashedLine}`, `color: ${color}; font-weight: bold;`);
            }
            else if (text === 'end') {
                console.log(`%c${dashedLine}结束${dashedLine}`, `color: ${color}; font-weight: bold;`);
            }
            else {
                console.log(`%c${text}`, `color: ${color}; font-weight: bold;`, extra);
            }
        });
    }
}

interface shouldRetryOptions<T> {
    errorRetryCount: number;
    errorRetryIgnores?: Array<(response: T) => boolean>;
    conditions?: Array<(response: T) => boolean>;
}

const defaultRetryConditions: Array<(response: AxiosResponse<HaiRuoResponse<unknown>>) => boolean> = [
    response => {
        if (response.data && ('status' in response.data)) {
            return !!response.data.status;
        }
        return false;
    },
    response => {
        if (response.data && ('errors' in response.data)) {
            return (response.data?.errors || []).some(shouldErrorCodeToBeRetry);
        }
        return false;
    },
];
function shouldRetry<T extends AxiosResponse>(
    response: T,
    {errorRetryCount, conditions = defaultRetryConditions, errorRetryIgnores = []}: shouldRetryOptions<T>
): boolean {
    if (errorRetryIgnores.some(f => f(response))) {
        return false;
    }
    if (conditions.some(f => f(response))) {
        return !!(errorRetryCount && errorRetryCount > 0);
    }
    return false;
}
