@import (reference) '@baidu/one-ui/src/index.less';

.common-icon() {
    cursor: pointer;
    padding-left: 4px;
    color: #848b99;
    font-size: 14px;
    flex-shrink: 0;
}

.multiple-line-txt-cut(@line) {
    display: -webkit-box;
    overflow: hidden;
    -webkit-line-clamp: @line;
    -webkit-box-orient: vertical;
}

.common-filter-list() {
    .filter-list {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin-bottom: 16px;

        &-operations {
            margin-left: 16px;

            button + button {
                margin-left: 8px;
            }
        }

        .one-ui-pro-filter-dropdown-tag-trigger-node {
            margin: 0 4px;
            background: rgba(109, 159, 247, .07);
            border: none;
            border-radius: 6px;

            .one-ui-pro-filter-dropdown-label-and-operator {
                color: #3a5bfd;
            }

            .one-ui-pro-filter-dropdown-value {
                color: #3a5bfd;
                max-width: 150px;
                font-weight: 600;
                margin: 0 4px 0 8px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .one-ui-pro-filter-dropdown-count {
                color: #3a5bfd;
            }

            .one-ai-tag-close-icon {
                color: #3a5bfd;
            }
        }
    }
}

.common-table-columns() {
    // inline-operation-icon是图标，需使用visibility: hidden，不能使用diplay: none，会导致toolTips无法正常定位。
    // inline-operation是需要hover显示的其他组件，比如select下拉框
    // inline-title是需要hover隐藏的名字，比如hover显示下拉框就会隐藏名字
    // operation-margin是操作列的间距
    .inline-operation-icon-disabled {
        color: #a8b0bf !important;
        .common-icon();
    }

    .inline-operation-icon {
        visibility: hidden;
        .common-icon();

        &.disabled {
            color: #a8b0bf;
            cursor: not-allowed;
        }
    }

    .inline-operation {
        cursor: pointer;
        display: none;
    }

    .inline-title {
        display: inline-block;
    }

    .operation-margin {
        margin-right: 8px;
    }

    tr:hover {
        .inline-operation-icon {
            display: inline-block;
            visibility: visible;
        }

        .inline-operation {
            display: inline-block;
        }

        .inline-title {
            display: none;
        }
    }

    // 左侧不超过两行
    .multiple-cut {
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .material-name {
        color: #0054e6;

        a {
            text-decoration: none;
            background-color: transparent;

            &:focus {
                color: #0054e6;
            }
        }
    }

    .column-cell-flex {
        display: inline-flex;
        align-items: center;
        width: 100%;
        overflow: hidden;
    }

    .column-cell-disabled {
        cursor: not-allowed;
        color: @dls-color-gray-6;
    }

    .column-cell-disabled-color {
        color: @dls-color-gray-6;
    }

    .column-cell-flex.right {
        justify-content: flex-end;
    }

    .column-cell-inline-flex {
        display: inline-flex;
        align-items: center;
    }

    .with-multiple-cut-3 {
        .multiple-line-txt-cut(3);
    }
}

.common-manage-center-gridmode(@right: 0) {
    .aix-manage-center-gridmode {
        position: absolute;
        right: @right;
    }
}

.common-manage-center-searchbox(@left) {
    position: absolute;
    top: 0;
    left: @left;
    z-index: 2;
}

.common-manage-center-table-list-layout() {
    display: flex;
    flex-direction: column;
    flex: 1;

    .table-list-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 16px;
        position: sticky;
        top: 0;

        .table-pagination {
            margin-bottom: 20px;
        }
    }

    .one-ai-table-scroll-container {
        position: sticky;
        margin-top: -17px;
    }
}

.common-manage-center-table-list-operation-bar(@right: 120px, @top: 70px) {
    .operation-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: absolute;
        top: @top;
        right: @right;
        z-index: 2;

        .right {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }
}

.common-manage-center-table-list-columns() {
    .operations-column {
        display: flex;
        gap: 4px;
        flex-wrap: wrap;
        align-items: center;
    }
}

.common-manage-center-batch-operation-bar(@top: -52px, @bottom: 16px) {
    .manage-center-operators.manage-center-operators-top {
        position: sticky;
        top: 0;
        margin-top: @top;
        margin-bottom: @bottom;
    }
}

.ellipsis-n-line(@line-num) {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: @line-num;
    overflow: hidden;
    text-overflow: ellipsis;
}
