<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14 19C14.5523 19 15 19.4477 15 20C15 20.5523 14.5523 21 14 21H8C7.44772 21 7 20.5523 7 20C7 19.4477 7.44772 19 8 19H14ZM17 3.5C19.2091 3.5 21 5.29086 21 7.5V13.5C21 15.7091 19.2091 17.5 17 17.5H6C3.79086 17.5 2 15.7091 2 13.5V7.5C2 5.29086 3.79086 3.5 6 3.5H17Z" fill="url(#paint0_radial_648_4251)"/>
<foreignObject x="7.80371" y="8.30396" width="18.3926" height="18.3921"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_648_4251_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_ii_648_4251)" data-figma-bg-blur-radius="4">
<path d="M21.5 14.9019C22.4282 16.5096 22.4282 18.4904 21.5 20.0981V20.0981C20.5718 21.7058 18.8564 22.6962 17 22.6962V22.6962C15.1436 22.6962 13.4282 21.7058 12.5 20.0981V20.0981C11.5718 18.4904 11.5718 16.5096 12.5 14.9019V14.9019C13.4282 13.2942 15.1436 12.3038 17 12.3038V12.3038C18.8564 12.3038 20.5718 13.2942 21.5 14.9019V14.9019Z" fill="#9EC4FF" fill-opacity="0.6"/>
</g>
<g filter="url(#filter1_d_648_4251)">
<path d="M15 17V19.5" stroke="white"/>
</g>
<g filter="url(#filter2_d_648_4251)">
<path d="M17 15.5V19.5" stroke="white"/>
</g>
<g filter="url(#filter3_d_648_4251)">
<path d="M19 16.5L19 19.5" stroke="white"/>
</g>
<path opacity="0.9" d="M7 11.5L9.64706 8.64286L12.25 11L16 7.5" stroke="white" stroke-width="2"/>
<defs>
<filter id="filter0_ii_648_4251" x="7.80371" y="8.30396" width="18.3926" height="18.3921" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_648_4251"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.4" dy="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.7 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_648_4251" result="effect2_innerShadow_648_4251"/>
</filter>
<clipPath id="bgblur_0_648_4251_clip_path" transform="translate(-7.80371 -8.30396)"><path d="M21.5 14.9019C22.4282 16.5096 22.4282 18.4904 21.5 20.0981V20.0981C20.5718 21.7058 18.8564 22.6962 17 22.6962V22.6962C15.1436 22.6962 13.4282 21.7058 12.5 20.0981V20.0981C11.5718 18.4904 11.5718 16.5096 12.5 14.9019V14.9019C13.4282 13.2942 15.1436 12.3038 17 12.3038V12.3038C18.8564 12.3038 20.5718 13.2942 21.5 14.9019V14.9019Z"/>
</clipPath><filter id="filter1_d_648_4251" x="11.5" y="15" width="7" height="8.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.4 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_648_4251"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_648_4251" result="shape"/>
</filter>
<filter id="filter2_d_648_4251" x="13.5" y="13.5" width="7" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.4 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_648_4251"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_648_4251" result="shape"/>
</filter>
<filter id="filter3_d_648_4251" x="15.5" y="14.5" width="7" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.4 0 0 0 0 1 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_648_4251"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_648_4251" result="shape"/>
</filter>
<radialGradient id="paint0_radial_648_4251" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(2.63333) rotate(52.562) scale(30.2132 22.8641)">
<stop stop-color="#BCD7FF"/>
<stop offset="1" stop-color="#0066FF"/>
</radialGradient>
</defs>
</svg>
