@import '@baidu/react-formulator/dist/style.css';
@import '@baidu/one-ui/src/index.less';
@import '@baidu/one-ui/lib/index.ai.css';
@import '@baidu/one-ui/lib/typography.css';
@import '@baidu/one-ui-pro/es/index.css';
@import '@baidu/one-ui-pro/es/index.ai.css';
@import 'slick-carousel/slick/slick.css';
@import '@baidu/ai-materials/dist/style.css';
@import '@baidu/one-charts/es/index.css';
@import '@baidu/one-charts/es/index.ai.css';
@import "@baidu/feed-preview/lib/index.css";

@font-face {
    font-family: 'FZPinShangHeiS-B-GB';
    src: url('https://fc-feed.bj.bcebos.com/aix%2FFZPSZHJW.TTF') format('truetype');
    font-weight: normal;
    font-style: normal;
}

*::-webkit-scrollbar-thumb {
    background-color: @dls-color-translucent-4 !important;
}

*:hover::-webkit-scrollbar-thumb {
    background-color: rgba(83, 101, 138, 0.5) !important;
}

* {
    box-sizing: border-box;
    padding: 0;
    margin: 0;
}

html,
body {
    margin: 0;
}

body {
    background-repeat: no-repeat;
    background-attachment: fixed;
    background-size: cover;
    background-image: url(https://fc-feed.bj.bcebos.com/aix/bg_202505.png);
}

a,
a:link,
a:visited,
a:hover,
a:active {
    text-decoration: none;
}

#root {
    height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;

    .one-ui-pro-page-tips-container {
        width: 100%;
    }
}

// hack 导航hover样式
.one-nav-item:hover {
    background-color: transparent;
}

// 这个是挂在body下的，暂时先这么加吧
.one-dropdown-menu {
    box-shadow: 0 1px 8px rgba(0, 0, 0, .06), 0 7px 14px 2px rgba(0, 0, 0, .05), 0 8px 16px 4px rgba(0, 0, 0, .04) !important;
    border-radius: 6px !important;
}

.one-popover-inner {
    border-radius: 6px !important;
    box-shadow: 0 4px 28px 1px rgba(0, 71, 194, .04), 0 5px 30px 1px rgba(0, 71, 194, .05), 0 6px 32px 2px rgba(0, 71, 194, .06) !important;
}

.one-textarea-wrapper-medium .one-textarea-container .one-textarea {
    border-radius: 6px !important;

    &:focus {
        border-color: #3a5bfd;
    }
}

// 这里加一个全局的吧，默认link都是baseline
.one-button.one-button-text-strong {
    vertical-align: baseline;
}

// shadow color
@dls-shadow-color: #0047c2;

// border radius
@dls-border-radius-0: @dls-height-unit;
@dls-border-radius-1: @dls-height-unit * 1.5;
@dls-border-radius-2: @dls-height-unit * 2.5;
@dls-border-radius-3: @dls-height-unit * 4;
@dls-border-radius-4: @dls-height-unit * 6.5;
@dls-checkbox-border-radius: @dls-border-radius-0;

// brand color
@dls-color-brand: #4d79ff;
@dls-color-brand-0: #fff;
@dls-color-brand-1: #f2f7ff;
@dls-color-brand-2: #e3edff;
@dls-color-brand-3: #c4daff;
@dls-color-brand-4: #99beff;
@dls-color-brand-5: #729cfe;
@dls-color-brand-6: #4d79ff;
@dls-color-brand-7: #3a5bfd;
@dls-color-brand-8: #333fe6;
@dls-color-brand-9: #0f2dbd;
@dls-color-brand-10: #1c244a;
@dls-color-brand-11: #000;

// component height
@dls-height-xs: @dls-height-unit * 6;
@dls-height-s: @dls-height-unit * 7;
@dls-height-m: @dls-height-unit * 9;
@dls-height-l: @dls-height-unit * 10;
@dls-height-xl: @dls-height-unit * 12;
@dls-height-xxl: @dls-height-unit * 15;

.ellipsis {
    white-space: nowrap;      /* 确保文本在一行显示 */
    overflow: hidden;         /* 隐藏溢出的文本 */
    text-overflow: ellipsis;  /* 使用省略号表示被裁切的文本 */
}

.inline-popover-editor {
    .footer {
        button + button {
            margin-left: 12px;
        }
    }
}

.light-ai-tooltip {
    z-index: 1053 !important; // 组件库z-index 有问题，以免被 radio 等覆盖
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex {
    display: flex;
    align-items: center;
}

.warning-color {
    color: #FEA800;
}

.strong-color {
    color: @dls-color-brand-7;
}

.grey-color {
    color: #999;
}

.ml-1 {
    margin-left: 4px;
}

.mt-1 {
    margin-top: 4px;
}

.mt-4 {
    margin-top: 16px;
}

.flex-none {
    flex: none;
}

.h100 {
    height: 100%;
}

.w100 {
    width: 100%;
}

.overflow-hidden-nowrap-ellipsis {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
