import {useEffect, useCallback} from 'react';
import {hideIframe, initIframe, softNavigateIframe} from '@/utils/iframeManager';
import {getIframeUrl} from '@/utils/iframe';
import {PRODUCT} from '@/dicts/campaign';

interface UseSingletonIframeOptions {
    iframePath: string;
    autoShow?: boolean;
    extraSearch?: Record<string, any>;
    getContainer?: () => HTMLDivElement | undefined;
    iframeStyle?: string;
    containerStyle?: string;
    iframeClassName?: string;
}

export function useSingletonIframe(options: UseSingletonIframeOptions) {
    const {
        iframePath,
        extraSearch,
        getContainer = () => document.body,
        autoShow = true,
        iframeStyle,
        containerStyle,
        iframeClassName,
    } = options;

    // 软导航 - 通知iframe内部进行路由切换
    const softNavigate = useCallback(() => {
        softNavigateIframe(iframePath, {
            extraSearch,
            container: getContainer?.() || undefined,
            iframeStyle,
            containerStyle,
            iframeClassName,
        });
    }, [extraSearch, iframePath, getContainer, iframeStyle, containerStyle, iframeClassName]);

    // 自动显示 - 确保在 DOM 元素渲染后再调用
    useEffect(() => {
        if (autoShow && getContainer?.()) {
            softNavigate();
        }
    }, [autoShow, softNavigate, getContainer]);

    // 组件卸载时隐藏iframe
    useEffect(() => hideIframe, []);

    return {softNavigate};
}

initIframe(getIframeUrl('/fc/managecenter/dashboard/overview/user/${userId}', {product: PRODUCT.FC}));
