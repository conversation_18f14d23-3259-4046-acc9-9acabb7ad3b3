/**
 * ! 这个hook存在的意义在于添加全局快捷键
 * ! 由于平台内存在iframe 页面， iframe 页面的keydown事件无法被监听，所以需要通过post message 传出来
 */
import {useEffect, useRef} from 'react';

interface ShortcutKeyConfig {
    key: string;
    ctrl?: boolean;
    shift?: boolean;
    alt?: boolean;
    preventDefault?: boolean; // 是否阻止默认行为
}

// !这里通过限制传入的 name 来限制可以用的快捷键， 这些快捷键需要在 fe-fengchao、 fuyao 这些嵌入的iframe 做post message 才可以在iframe页面生效
export enum GlobalShortcutName {
    FlashLinkEsc = 'FlashLinkEsc',
    FlashLinkTrigger = 'FlashLinkTrigger',
}

const globalShortcuts: {
    [key in GlobalShortcutName]: ShortcutKeyConfig;
} = {
    [GlobalShortcutName.FlashLinkEsc]: {
        key: 'Escape',
        preventDefault: false, // ESC 可能需要保留默认行为
    },
    [GlobalShortcutName.FlashLinkTrigger]: {
        key: 'P',
        ctrl: true,
        shift: true,
        preventDefault: true, // 阻止打印对话框
    },
};
// !这里通过限制传入的 name 来限制可以用的快捷键， 这些快捷键需要在 fe-fengchao、 fuyao 这些嵌入的iframe 做post message 才可以在iframe页面生效

/**
 * KeyboardEvent 透传消息格式
 */
interface KeyboardEventMessage {
    type: 'KEYBOARD_EVENT';
    event: {
        key: string;
        code: string;
        ctrlKey: boolean;
        shiftKey: boolean;
        altKey: boolean;
        metaKey: boolean;
    };
}

/**
 * 键盘事件接口（统一 KeyboardEvent 和透传的事件）
 */
interface KeyboardEventLike {
    key: string;
    ctrlKey: boolean;
    shiftKey: boolean;
    altKey: boolean;
    metaKey: boolean;
}

/**
 * 检查快捷键是否匹配
 */
const checkShortcutMatch = (
    shortcut: ShortcutKeyConfig,
    event: KeyboardEventLike
): boolean => {
    const keyMatch = event.key.toUpperCase() === shortcut.key.toUpperCase();
    const ctrl = event.ctrlKey || event.metaKey;
    const ctrlMatch = shortcut.ctrl ? ctrl : !ctrl;
    const shiftMatch = shortcut.shift ? event.shiftKey : !event.shiftKey;
    const altMatch = shortcut.alt ? event.altKey : !event.altKey;

    return keyMatch && ctrlMatch && shiftMatch && altMatch;
};

export const useGlobalShortcut = (globalShortcutName: GlobalShortcutName, callback: () => void) => {
    const callbackRef = useRef(callback);

    // 总是保持 ref 指向最新的 callback
    useEffect(() => {
        callbackRef.current = callback;
    });

    useEffect(() => {
        const shortcutConfig = globalShortcuts[globalShortcutName];

        // 处理直接的键盘事件
        const handleKeyDown = (e: KeyboardEvent) => {
            if (checkShortcutMatch(shortcutConfig, e)) {
                if (shortcutConfig.preventDefault) {
                    e.preventDefault();
                }
                callbackRef.current();
            }
        };

        // 处理来自 iframe 透传的键盘事件
        const handlePostMessage = (event: MessageEvent<KeyboardEventMessage>) => {
            if (event.data?.type !== 'KEYBOARD_EVENT') {
                return;
            }

            const keyEvent = event.data.event;
            if (checkShortcutMatch(shortcutConfig, keyEvent)) {
                callbackRef.current();
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        window.addEventListener('message', handlePostMessage);

        return () => {
            document.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('message', handlePostMessage);
        };
    }, [globalShortcutName]); // 只依赖 globalShortcutName
};

/**
 * iframe 内部使用的快捷键透传 Hook
 * 监听键盘事件并通过 postMessage 透传给父窗口
 */
export const useShortcutTransmitter = (globalShortcutName: GlobalShortcutName, callback?: () => void) => {
    const callbackRef = useRef(callback);

    // 总是保持 ref 指向最新的 callback
    useEffect(() => {
        callbackRef.current = callback;
    });

    useEffect(() => {
        const shortcutConfig = globalShortcuts[globalShortcutName];

        const handleKeyDown = (e: KeyboardEvent) => {
            if (checkShortcutMatch(shortcutConfig, e)) {
                // 根据配置决定是否阻止默认行为
                if (shortcutConfig.preventDefault) {
                    e.preventDefault();
                }

                // 执行本地回调
                callbackRef.current?.();

                // 透传事件到父窗口
                window.parent.postMessage({
                    type: 'KEYBOARD_EVENT',
                    event: {
                        key: e.key,
                        code: e.code,
                        ctrlKey: e.ctrlKey,
                        shiftKey: e.shiftKey,
                        altKey: e.altKey,
                        metaKey: e.metaKey,
                    },
                }, '*');
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [globalShortcutName]); // 只依赖 globalShortcutName
};
