import {RangePickerProps} from '@baidu/one-ui';

export const dateFormat = 'YYYY-MM-DD';
export const timeFormat = 'YYYY-MM-DD HH:mm:ss';

export const DateShortcuts = [
    {label: '今天', to: 0, from: 0},
    {label: '昨天', to: -1, from: -1},
    {label: '最近7天', to: -1, from: -7},
    {label: '最近14天', to: -1, from: -14},
    {label: '最近30天', to: -1, from: -30},
    {label: '上个月', from: {startOf: 'month', months: -1}, to: {startOf: 'month', days: -1}},
    {label: '本月', from: {startOf: 'month'}, to: 0},
] as RangePickerProps['shortcuts'];
