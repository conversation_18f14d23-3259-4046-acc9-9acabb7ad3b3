import globalData from '@/utils/globalData';
import {TradeEnum, Trade2ndEnum} from '@/dicts/tradeInfo';

/* 在以下一级行业和二级行业生效：
    * 一级行业：医疗器械（2003）、保健品/药品（2004）
    * 二级行业：特殊用途化妆品（201204）、特殊医学用途配方食品（201310）、消毒产品（202904）
*/
// 四品一械行业
export const spyxTrade = [
    200302,
    200303,
    200301,
    200404,
    200402,
    200403,
    200405,
    200401,
    201204,
    201310,
    202904,
];

// 信息流项目层级选中AI Max后默开智能基建 - 生效范围：
// * 教育培训：一级行业ID—— 2019
// * 生活服务：一级行业ID——2007
// * 游戏：一级行业ID——2018
// * 短剧：二级行业ID——200812
// * 金融：一级行业ID——2015
export const getAutoOpenTradeList = () => {
    const {tradeId1st, tradeId2nd} = globalData.get('feedBasicData') || {};
    return [
        TradeEnum.COURSE,
        TradeEnum.SHFW,
        TradeEnum.GAME,
        TradeEnum.JRFW,
    ].includes(+tradeId1st)
    || [Trade2ndEnum.Duanju].includes(+tradeId2nd);
};
