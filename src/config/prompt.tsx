import {
    IconUserStar,
    IconBullseyeHit,
    IconReportBookmark,
    IconReport,
    IconFileAdd,
    IconImageStar,
    IconAiBubble,
} from 'dls-icons-react';
import {SugAction} from '@/utils/aiChat';
import AdRoutes from '@/modules/Ad/routes';
import history from '@/entries/main/history';
import {appendQuery} from '@/utils/route';
import {
    isAiBuildUser,
    isFcCplUser,
    isFeedPromotionMainUser,
    isFeedPromotionCplCampaignUser,
} from '@/utils/getFlag';
import {getUserId} from '@/utils';
import {PageType} from '@/dicts/pageType';
import {PLATFORM_ENUM} from '@/dicts';
import {PRODUCT} from '@/dicts/campaign';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {makeLogger, ENTER_GUI_CREATE} from '@/modules/Ad/ManageCenter/common/utils';
import {FcCampaignTypeEnum} from '@/interface/campaign';
import {checkIsSupportCplPlan} from '@/utils/checkIsSupportCplPlan';
import {getFeedBjhTongtouUser} from '@/utils/getFeedFlag';

const sendLog = makeLogger({source: 'homepage_sug', 'extra_params': `${PRODUCT.FC}|0`});
const sendFeedLog = makeLogger({source: 'homepage_sug', 'extra_params': `${PRODUCT.FEED}|0`});

export const CONTENT_CONFIG = {
    AI_BUILD_ENTRY: '智能搭建',
    AI_BUILD: '搭建账户',
    AI_BUILD_REFRESH: '智能翻新',
    CREATE_CAMPAIGN_FROM_AI: '在对话中创建方案',
    CREATE_UNIT_FROM_AI: '在对话中创建单元',
    CREATE_CREATIVE_FROM_AI: '在对话中添加创意',
    CREATE_PROJECT_FROM_AI: '在对话中创建项目',
};

export const PROMPT_CONFIG = {
    CREATE_CAMPAIGN: '创建方案',
    CREATE_CAMPAIGN_FROM_AI: '创建方案',
    CREATE_CAMPAIGN_FROM_GUI: '创建关键词方案',
    CREATE_CPL_CAMPAIGN_FROM_GUI: '创建搜索服务直达方案',
    VIEW_MY_CAMPAIGNS: '查看我的营销方案',
    VIEW_MY_CAMPAIGNS_1: '查看全部营销方案',
    VIEW_REPORT: '数据报告智能解读', // 账户和营销方案
    DIAGNOSE_ACCOUNT: '为我诊断我的账户',
    DIAGNOSE_ACCOUNT_1: '诊断账户',
    DIAGNOSE_ACCOUNT_2: '查看账户的风险点和潜力点',
    DIAGNOSE_FLUCTUATIONS: '消费波动智能诊断',
    VIEW_HISTORY: '查看我的历史操作记录',
    VIEW_HISTORY_1: '查看历史操作记录',
    INDUSTRY_INSIGHT: '解读我所在行业的流量与竞争情况', // 账户和营销方案
    MANAGE_CAMPAIGNS: '管理营销方案',
    NEGATIVE_WORD: '否词',
    AI_BUILD: '帮我搭建账户',
    AI_BUILD_REFRESH: '帮我翻新账户',
    CREATE: '帮我创建',
    CREATE_PROJECT: '创建项目',
    CREATE_PROJECT_FROM_AI: '创建项目',
    CREATE_PROJECT_FROM_GUI: '创建搜索推广项目',
    CREATE_FEED_PROJECT_FROM_GUI: '创建信息流推广项目',
    CREATE_FEED_CAMPAIGN_FROM_GUI: '创建信息流方案',
    CREATE_FEED_CPL_CAMPAIGN_FROM_GUI: '创建信息流服务直达方案',
    CREATE_UNIT: '创建单元',
    CREATE_UNIT_FROM_GUI: '创建关键词方案的单元',
    CREATE_FEED_UNIT_FROM_GUI: '创建信息流方案的单元',
    CREATE_UNIT_FROM_AI: '创建单元',
    ADD_KEYWORD: '添加关键词',
    CREATE_CREATIVE: '添加创意',
    CREATE_CREATIVE_FROM_AI: '添加创意',
    CREATE_CREATIVE_FROM_GUI: '添加关键词方案的创意',
    CREATE_FEED_CREATIVE_FROM_GUI: '添加信息流方案的创意',
};

export const CREATE_ICON_CONFIG = {
    AI_BUILD: <IconUserStar />,
    CREATE_PROJECT: <IconBullseyeHit />,
    CREATE_CAMPAIGN: <IconReportBookmark />,
    CREATE_UNIT: <IconReport />,
    ADD_KEYWORD: <IconFileAdd />,
    CREATE_CREATIVE: <IconImageStar />,
};

enum HomePageCreateSug {
    AI_BUILD = 'AI_BUILD',
    CREATE_PROJECT = 'CREATE_PROJECT',
    CREATE_CAMPAIGN = 'CREATE_CAMPAIGN',
    CREATE_UNIT = 'CREATE_UNIT',
    ADD_KEYWORD = 'ADD_KEYWORD',
    CREATE_CREATIVE = 'CREATE_CREATIVE',
}

// eslint-disable-next-line complexity
const getSugConfig = () => ({
    [HomePageCreateSug.AI_BUILD]: {
        content: CONTENT_CONFIG.AI_BUILD_ENTRY,
        prompt: PROMPT_CONFIG.AI_BUILD,
        icon: CREATE_ICON_CONFIG.AI_BUILD,
        trigger: 'homepage',
        visible: isAiBuildUser,
        children: [
            {
                content: CONTENT_CONFIG.AI_BUILD_REFRESH,
                prompt: PROMPT_CONFIG.AI_BUILD_REFRESH,
                trigger: 'homepage',
            },
            {
                content: CONTENT_CONFIG.AI_BUILD,
                prompt: PROMPT_CONFIG.AI_BUILD,
                trigger: 'homepage',
            },
        ],
    },
    [HomePageCreateSug.CREATE_PROJECT]: {
        content: PROMPT_CONFIG.CREATE_PROJECT,
        prompt: PROMPT_CONFIG.CREATE_PROJECT,
        icon: CREATE_ICON_CONFIG.CREATE_PROJECT,
        trigger: 'homepage',
        children: [
            {
                content: PROMPT_CONFIG.CREATE_PROJECT_FROM_GUI,
                prompt: PROMPT_CONFIG.CREATE_PROJECT_FROM_GUI,
                trigger: 'homepage',
                type: SugAction.CALLBACK,
                callback: () => {
                    sendLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageProjectList});
                    const toUrl = AdRoutes.getPathByName?.(PageType.CreateProject);
                    history.push(appendQuery(toUrl, {
                        userId: getUserId(),
                        globalProduct: PLATFORM_ENUM.FC,
                    }));
                },
                once: false,
            },
            ...(
                isFeedPromotionMainUser() ? [
                    {
                        content: PROMPT_CONFIG.CREATE_FEED_PROJECT_FROM_GUI,
                        prompt: PROMPT_CONFIG.CREATE_FEED_PROJECT_FROM_GUI,
                        trigger: 'homepage',
                        type: SugAction.CALLBACK,
                        callback: () => {
                            sendFeedLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageProjectList});
                            const toUrl = AdRoutes.getPathByName?.(PageType.CreateFeedProject);
                            history.push(appendQuery(toUrl, {
                                userId: getUserId(),
                                globalProduct: PLATFORM_ENUM.FEED,
                            }));
                        },
                        once: false,
                    },
                ] : []
            ),
            ...(
                getFeedBjhTongtouUser()
                    ? []
                    : [
                        {
                            content: CONTENT_CONFIG.CREATE_PROJECT_FROM_AI,
                            prompt: PROMPT_CONFIG.CREATE_PROJECT_FROM_AI,
                            trigger: 'homepage',
                            afterIcon: (<IconAiBubble style={{color: '#3a5bfd'}} />),
                        },
                    ]
            ),
        ],
    },
    [HomePageCreateSug.CREATE_CAMPAIGN]: {
        content: PROMPT_CONFIG.CREATE_CAMPAIGN,
        prompt: PROMPT_CONFIG.CREATE_CAMPAIGN,
        icon: CREATE_ICON_CONFIG.CREATE_CAMPAIGN,
        trigger: 'homepage',
        children: [
            {
                content: PROMPT_CONFIG.CREATE_CAMPAIGN_FROM_GUI,
                prompt: PROMPT_CONFIG.CREATE_CAMPAIGN_FROM_GUI,
                trigger: 'homepage',
                type: SugAction.CALLBACK,
                callback: () => {
                    sendLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageCampaignList});
                    const toUrl = AdRoutes.getPathByName?.(PageType.NewCampaign);
                    history.push(appendQuery(toUrl, {
                        userId: getUserId(),
                        globalProduct: PLATFORM_ENUM.FC,
                        productLine: JSON.stringify([PLATFORM_ENUM.FC, PRODUCT.FC, FcCampaignTypeEnum.KEYWORD]),
                    }));
                },
                once: false,
            },
            ...(
                isFcCplUser()
                    ? [
                        {
                            content: PROMPT_CONFIG.CREATE_CPL_CAMPAIGN_FROM_GUI,
                            prompt: PROMPT_CONFIG.CREATE_CPL_CAMPAIGN_FROM_GUI,
                            trigger: 'homepage',
                            type: SugAction.CALLBACK,
                            callback: () => {
                                sendLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageCampaignList});
                                const toUrl = AdRoutes.getPathByName?.(PageType.CreateCPLCampaign);
                                if (toUrl) {
                                    history.push(appendQuery(toUrl, {
                                        userId: getUserId(),
                                        globalProduct: PLATFORM_ENUM.FC,
                                    }));
                                }
                            },
                            once: false,
                        },
                    ]
                    : []
            ),
            ...(
                isFeedPromotionMainUser() ? [
                    {
                        content: PROMPT_CONFIG.CREATE_FEED_CAMPAIGN_FROM_GUI,
                        prompt: PROMPT_CONFIG.CREATE_FEED_CAMPAIGN_FROM_GUI,
                        trigger: 'homepage',
                        type: SugAction.CALLBACK,
                        callback: () => {
                            sendFeedLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageCampaignList});
                            const toUrl = AdRoutes.getPathByName?.(PageType.CreateFeedCampaign);
                            if (toUrl) {
                                history.push(appendQuery(toUrl, {
                                    userId: getUserId(),
                                    globalProduct: PLATFORM_ENUM.FEED,
                                }));
                            }
                        },
                        once: false,
                    },
                ] : []
            ),
            ...(
                isFeedPromotionCplCampaignUser() && checkIsSupportCplPlan() ? [
                    {
                        content: PROMPT_CONFIG.CREATE_FEED_CPL_CAMPAIGN_FROM_GUI,
                        prompt: PROMPT_CONFIG.CREATE_FEED_CPL_CAMPAIGN_FROM_GUI,
                        trigger: 'homepage',
                        type: SugAction.CALLBACK,
                        callback: () => {
                            sendFeedLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageCampaignList});
                            const toUrl = AdRoutes.getPathByName?.(PageType.CreateFeedCPLCampaign);
                            if (toUrl) {
                                history.push(appendQuery(toUrl, {
                                    userId: getUserId(),
                                    globalProduct: PLATFORM_ENUM.FEED,
                                }));
                            }
                        },
                        once: false,
                    },
                ] : []
            ),
            ...(
                getFeedBjhTongtouUser()
                    ? []
                    : [
                        {
                            content: CONTENT_CONFIG.CREATE_CAMPAIGN_FROM_AI,
                            prompt: PROMPT_CONFIG.CREATE_CAMPAIGN_FROM_AI,
                            trigger: 'homepage',
                            afterIcon: (<IconAiBubble style={{color: '#0052cc'}} />),
                        },
                    ]
            ),
        ],
    },
    [HomePageCreateSug.CREATE_UNIT]: {
        content: PROMPT_CONFIG.CREATE_UNIT,
        prompt: PROMPT_CONFIG.CREATE_UNIT,
        icon: CREATE_ICON_CONFIG.CREATE_UNIT,
        trigger: 'homepage',
        children: [
            {
                content: PROMPT_CONFIG.CREATE_UNIT_FROM_GUI,
                prompt: PROMPT_CONFIG.CREATE_UNIT_FROM_GUI,
                trigger: 'homepage',
                type: SugAction.CALLBACK,
                callback: () => {
                    sendLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageAdgroupList});
                    const toUrl = AdRoutes.getPathByName?.(PageType.AdgroupList);
                    history.push(appendQuery(toUrl, {
                        userId: getUserId(),
                        openDrawer: true,
                        productLine: JSON.stringify([PLATFORM_ENUM.FC, PRODUCT.FC, FcCampaignTypeEnum.KEYWORD]),
                        globalProduct: PLATFORM_ENUM.FC,
                    }));
                },
                once: false,
            },
            ...(
                isFeedPromotionMainUser() ? [
                    {
                        content: PROMPT_CONFIG.CREATE_FEED_UNIT_FROM_GUI,
                        prompt: PROMPT_CONFIG.CREATE_FEED_UNIT_FROM_GUI,
                        trigger: 'homepage',
                        type: SugAction.CALLBACK,
                        callback: () => {
                            sendFeedLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageAdgroupList});
                            const toUrl = AdRoutes.getPathByName?.(PageType.AdgroupList);
                            history.push(appendQuery(toUrl, {
                                userId: getUserId(),
                                openDrawer: true,
                                productLine: JSON.stringify([PLATFORM_ENUM.FEED, PRODUCT.FEED]),
                                globalProduct: PLATFORM_ENUM.FEED,
                            }));
                        },
                        once: false,
                    },
                ] : []
            ),
            {
                content: CONTENT_CONFIG.CREATE_UNIT_FROM_AI,
                prompt: PROMPT_CONFIG.CREATE_UNIT_FROM_AI,
                trigger: 'homepage',
                afterIcon: (<IconAiBubble style={{color: '#0052cc'}} />),
            },
        ],
    },
    [HomePageCreateSug.ADD_KEYWORD]: {
        content: PROMPT_CONFIG.ADD_KEYWORD,
        prompt: PROMPT_CONFIG.ADD_KEYWORD,
        icon: CREATE_ICON_CONFIG.ADD_KEYWORD,
        trigger: 'homepage',
        type: SugAction.CALLBACK,
        callback: () => {
            sendLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageKeywordList});
            const toUrl = AdRoutes.getPathByName?.(PageType.KeywordList);
            history.push(appendQuery(toUrl, {
                userId: getUserId(),
                openDrawer: true,
                globalProduct: PLATFORM_ENUM.FC,
            }));
        },
        once: false,
    },
    [HomePageCreateSug.CREATE_CREATIVE]: {
        content: PROMPT_CONFIG.CREATE_CREATIVE,
        prompt: PROMPT_CONFIG.CREATE_CREATIVE,
        icon: CREATE_ICON_CONFIG.CREATE_CREATIVE,
        trigger: 'homepage',
        children: [
            {
                content: PROMPT_CONFIG.CREATE_CREATIVE_FROM_GUI,
                prompt: PROMPT_CONFIG.CREATE_CREATIVE_FROM_GUI,
                trigger: 'homepage',
                type: SugAction.CALLBACK,
                callback: () => {
                    sendLog({event: ENTER_GUI_CREATE, level: levelTypeMapForLogOrSwr.manageCreativeTextList});
                    const toUrl = AdRoutes.getPathByName?.(PageType.CreativeTextList);
                    history.push(appendQuery(toUrl, {
                        userId: getUserId(),
                        productLine: JSON.stringify([PLATFORM_ENUM.FC, PRODUCT.FC, FcCampaignTypeEnum.KEYWORD]),
                        openDrawer: true,
                        globalProduct: PLATFORM_ENUM.FC,
                    }));
                },
                once: false,
            },
            ...(
                isFeedPromotionMainUser() ? [
                    {
                        content: PROMPT_CONFIG.CREATE_FEED_CREATIVE_FROM_GUI,
                        prompt: PROMPT_CONFIG.CREATE_FEED_CREATIVE_FROM_GUI,
                        trigger: 'homepage',
                        type: SugAction.CALLBACK,
                        callback: () => {
                            sendFeedLog({
                                event: ENTER_GUI_CREATE,
                                level: levelTypeMapForLogOrSwr.manageCreativeTextList,
                            });
                            const toUrl = AdRoutes.getPathByName?.(PageType.CreativeTextList);
                            history.push(appendQuery(toUrl, {
                                userId: getUserId(),
                                productLine: JSON.stringify([PLATFORM_ENUM.FEED, PRODUCT.FEED]),
                                openDrawer: true,
                                globalProduct: PLATFORM_ENUM.FEED,
                            }));
                        },
                        once: false,
                    },
                ] : []
            ),
            {
                content: CONTENT_CONFIG.CREATE_CREATIVE_FROM_AI,
                prompt: PROMPT_CONFIG.CREATE_CREATIVE_FROM_AI,
                trigger: 'homepage',
                afterIcon: (<IconAiBubble style={{color: '#0052cc'}} />),
            },
        ],
    },
});


export const getHomepageCreateSugs = () => {
    const HomePageCreateSugConfig = getSugConfig();
    return Object.keys(HomePageCreateSugConfig)
        .filter(key => {
            const item = HomePageCreateSugConfig[key as HomePageCreateSug];
            return 'visible' in item ? item.visible() : true;
        })
        .map(key => {
            return HomePageCreateSugConfig[key as HomePageCreateSug];
        });
};
