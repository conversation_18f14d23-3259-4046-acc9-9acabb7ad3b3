export enum TradeEnum {
    /**
     * 医疗服务
     */
    MEDICAL = 2001,
    /**
     * 医疗器械
     */
    YLQX = 2003,
    /**
     * 保健品/药品
     */
    BAOJIAN = 2004,
    /**
     * 机械设备
     */
    JXSHB = 2005,
    /**
     * 商务服务
     */
    SWFW = 2006,
    /**
     * 交通出行
     */
    CAR = 2010,
    /**
     * 整形美容 医美
     */
    YIMEI = 2002,
    /**
     * 游戏
     */
    GAME = 2018,
    /**
     * 教育培训
     */
    COURSE = 2019,
    /**
     * 生活服务
     */
    SHFW = 2007,
    /**
     * 文娱传媒
     */
    WYCM = 2008,
    /**
     * 金融服务
     */
    JRFW = 2015,
    /**
     * 旅游服务
     */
    LYFW = 2020,
    /**
     * 房产家居
     */
    FCJJ = 2023,
    /**
     * 电子电工
     */
    DZDG = 2024,
    /**
     * 化工能源
     */
    HGNY = 2029,
    /**
     * 农林牧渔
     */
    NLMY = 2027,
    /**
     * 招商加盟
     */
    ZSJM = 2030,
    // 法律
    LAW = 2006,
}

export enum Trade2ndEnum {
    Law = 200604,
    Video = 200805,
    Duanju = 200812,
    TravelAgency = 202004,
}

export const ProductSelectTipMap = {
    [Trade2ndEnum.TravelAgency]: '推荐您关联旅游路线相关产品，以便系统根据产品信息优化定向和创意，提升投放效果',
};

export const supportedImageGeneratorTrade = [
    TradeEnum.FCJJ,
    TradeEnum.JRFW,
    TradeEnum.LYFW,
    TradeEnum.SHFW,
    TradeEnum.WYCM,
];

export const B2BTrade = [
    TradeEnum.JXSHB,
    TradeEnum.DZDG,
    TradeEnum.NLMY,
    TradeEnum.HGNY,
];

export function isYimeiTrade(tradeId1st: number) {
    return tradeId1st === TradeEnum.YIMEI;
}

export function isEducationTrade(tradeId1st: number) {
    return tradeId1st === TradeEnum.COURSE;
}

export function isDuanjuVideoTrade(tradeId1st: number, tradeId2nd: number) {
    return tradeId1st === TradeEnum.WYCM && [Trade2ndEnum.Video, Trade2ndEnum.Duanju].includes(tradeId2nd);
}
