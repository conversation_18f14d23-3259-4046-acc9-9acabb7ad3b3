import moment from 'moment';
import {getUserId} from '@/utils';

export const VarPlaceholder = '%X%';

export enum OptimizeAdviceSource {
    /**
     * 首页概览
     */
    OVERVIEW = 249,
    /**
     * 优化中心
     */
    OPT_CENTER = 248,
    /**
     * 诊断概览
     */
    DIAGNOSIS = 250,
}

/**
 * 优化建议类型枚举
 *
 * @const
 * @enum
 */
export enum AdviceTypeEnum {
    ALL = -1,
    OVERALL = 6,
    ORIENT = 1,
    BID = 2,
    BUDGET = 3,
    IDEA = 5,
    REPAIR = 0,
    BASE = 8,
}

export enum OperationEnums {
    ADVICE_DETAIL = 999,
    ACCEPT_ALL_ADD_CREATIVE_IMAGE = 111,
    ACCEPT_ALL_ADD_CREATIVE_VIDEO = 113,
    ACCEPT_ALL_ADD_CREATIVE = 39,
    DELETE_ALL_REPEATE_IDEA = 94,
    ACCEPT_ALL_ADD_PLUGIN = 74,
}

export const OperationMap = {
    [OperationEnums.ADVICE_DETAIL]: 'detail',
    [OperationEnums.ACCEPT_ALL_ADD_CREATIVE_IMAGE]: 'feedCreativePictureInfo',
    [OperationEnums.ACCEPT_ALL_ADD_CREATIVE_VIDEO]: 'feedCreativeVideoInfo',
    [OperationEnums.ACCEPT_ALL_ADD_CREATIVE]: 'feedCreativeInfo',
    [OperationEnums.DELETE_ALL_REPEATE_IDEA]: 'removeDuplicateCreativeFeed',
    [OperationEnums.ACCEPT_ALL_ADD_PLUGIN]: 'feedPluginInfo',
};

export enum AdviceOperateType {
    NAVIGATE_DETAIL = 0,
    ACCEPT_ONCE = 1,
    OTHERS = 2,
    ACCEPT_DELETE = 3,
    DIALOG_EDITOR = 4
}

const detailOperationsConfig = {
    type: OperationEnums.ADVICE_DETAIL,
    mold: AdviceOperateType.NAVIGATE_DETAIL,
    text: '查看详情',
};


export enum AdviceEnums {
    modExpiredMaterialFeed = 223,
    addFeedCreativePictureConversion = 214,
    addFeedCreativeConversion = 206,
    addFeedCreativeVideoConversion = 215,
    removeDuplicateCreativeFeed = 211,
    addPluginConversionFeed = 209,
}

/**
 * 优化建议卡片操作按钮配置
 * text - 按钮展示文案
 * type - 操作按钮类型
 * remindMsg - 一键操作类按钮弹窗文案
 * successMsg - 一键操作类按钮操作成功提示文案
 * overridePath - 一键操作按钮，特殊的采纳接口path
 *
 */
export const AdviceOperationsConfig = {
    [AdviceEnums.modExpiredMaterialFeed]: [
        {
            ...detailOperationsConfig,
            text: '去调整',
        },
    ],
    [AdviceEnums.addFeedCreativePictureConversion]: [
        {
            ...detailOperationsConfig,
            text: '去添加',
        },
        {
            type: OperationEnums.ACCEPT_ALL_ADD_CREATIVE_IMAGE,
            mold: AdviceOperateType.ACCEPT_ONCE,
            text: '一键添加',
            overridePath: 'raining/MOD/FeedCreativePictureAsyncService/acceptFeedCreativePicture',
            remindMsg: [
                '点击一键添加，自动保存到图片库并填充至指定单元。请确保您已充分查阅优化建议详情页的方案，添加操作不可恢复。',
            ],
            successMsg: `成功添加${1}个创意`,
            modifyParams(params) {
                const {
                    operationParam: {feedCreativePictureInfo},
                    operationType, source, ...rest
                } = params;

                return {
                    items: {
                        acceptFeedCreativePicture: feedCreativePictureInfo,
                    },
                    excludeIds: [],
                    mtlIds: [],
                    checkAllCondition: {
                        startTime: moment().format('YYYY-MM-DD'),
                        endTime: moment().format('YYYY-MM-DD'),
                        idType: 5000,
                        ids: [getUserId()],
                    },
                    checkAll: true,
                    adviceOptType: operationType,
                    adviceSource: source,
                    adviceId: AdviceEnums.addFeedCreativePictureConversion,
                    ...rest,
                };
            },
        },
    ],
    [AdviceEnums.addFeedCreativeConversion]: [
        {
            ...detailOperationsConfig,
            text: '去添加',
        },
        {
            type: OperationEnums.ACCEPT_ALL_ADD_CREATIVE,
            mold: AdviceOperateType.ACCEPT_ONCE,
            text: '一键添加',
            remindMsg: [
                '点击一键添加，自动填充至指定单元。请确保您已充分查阅优化建议详情页的方案，添加操作不可恢复。',
            ],
            successMsg: `成功添加${VarPlaceholder}个创意`,
        },
    ],
    [AdviceEnums.addFeedCreativeVideoConversion]: [
        {
            ...detailOperationsConfig,
            text: '去添加',
        },
        {
            type: OperationEnums.ACCEPT_ALL_ADD_CREATIVE_VIDEO,
            mold: AdviceOperateType.ACCEPT_ONCE,
            text: '一键添加',
            remindMsg: [
                '点击一键添加，将启动离线生成数字人视频，生成后自动保存到视频库并填充至指定单元。请确保您已充分查阅优化建议详情页的方案，添加操作不可恢复。',
            ],
            successMsg: `成功添加${VarPlaceholder}个创意`,
        },
    ],
    [AdviceEnums.removeDuplicateCreativeFeed]: [
        {
            ...detailOperationsConfig,
            text: '去删除',
        },
        {
            type: OperationEnums.DELETE_ALL_REPEATE_IDEA,
            mold: AdviceOperateType.ACCEPT_ONCE,
            overridePath: 'raining/DEL/CreativeFeedAsyncService/deleteDuplicateCreativeFeed',
            modifyParams(params) {
                const {source, overridePath} = params;
                const today = moment().format('YYYY-MM-DD');
                return {
                    adviceId: AdviceEnums.removeDuplicateCreativeFeed, // 固定值
                    adviceOptType: OperationEnums.DELETE_ALL_REPEATE_IDEA, // 固定值
                    adviceSource: source,
                    checkAll: true,
                    overridePath,
                    checkAllCondition: {
                        startTime: today, // 当前时间
                        endTime: today, // 当前时间
                        idType: 5000, // id层级，5000：用户层级
                        ids: [getUserId()] // userid
                    }
                };
            },
            text: '一键删除',
            remindMsg: [
                '请确认是否删除近期无消费的重复创意',
            ],
            successMsg: `成功删除${VarPlaceholder}个无用的重复雷同创意`,
        },
    ],
    [AdviceEnums.addPluginConversionFeed]: [
        {
            ...detailOperationsConfig,
            text: '去添加',
        },
        {
            type: OperationEnums.ACCEPT_ALL_ADD_PLUGIN,
            mold: AdviceOperateType.ACCEPT_ONCE,
            text: '一键采纳',
            remindMsg: [
                '点击确定，系统将自动为您推荐单元创建高级创意表单组件，表单标题和按钮文案与本单元已绑定的落地页的表单组件保持一致。',
                '通过高级创意表单组件收集的线索可以在营销通后台查看。请确保您已充分了解优化建议详情页的内容以及本次操作对于账户修改的结果，是否确定一键添加？',
            ],
            successMsg: `成功为${VarPlaceholder}个单元添加创意组件`,
        },
    ],
};
