/**
 * @file 登陆页
 * <AUTHOR>
 */

import {lazy, Suspense, useEffect} from 'react';
import {BrowserRouter as Router, Route, Switch} from 'react-router-dom';
import {getToday} from '@/utils/date';
import '../../styles/app.global.less';
import MobileLogin from './MobileLogin';
import Summer2024 from './Summer2024';

import './index.global.less';

const TaskDetail = lazy(() => import('./EscortIncentiveTask/TaskDetail'));
const MobileTaskHistory = lazy(() => import('./EscortIncentiveTask/TaskHistory'));
const MobileTaskRule = lazy(() => import('./EscortIncentiveTask/TaskRule'));
const MobileTaskInvite = lazy(() => import('./EscortIncentiveTask/TaskInvite'));

const activityElement = [
    {
        startDate: '2024-05-22',
        endDate: '2024-06-20',
        Component: <Summer2024 />,
    },
].find(({startDate, endDate}) => startDate <= getToday() && getToday() <= endDate)?.Component;

const checkIsMobile = () => {
    const mobilePattern = /Mobi|Android|iPhone/i;
    const isMobile = mobilePattern.test(navigator.userAgent) || document.body.clientWidth <= 500;
    const taskInvitePage = window.location.pathname === '/mobile/taskInvite';
    if (!isMobile && !taskInvitePage) {
        window.location.href = '/login';
    }
};

export default function Mobile() {
    useEffect(
        () => {
            checkIsMobile();
            window.addEventListener('resize', checkIsMobile);
            return () => {
                window.removeEventListener('resize', checkIsMobile);
            };
        },
        []
    );

    return (
        <Suspense fallback={null}>
            <Router>
                <Switch>
                    <Route path="/mobile/taskDetail" component={TaskDetail} />
                    <Route path="/mobile/taskHistory" component={MobileTaskHistory} />
                    <Route path="/mobile/taskRule" component={MobileTaskRule} />
                    <Route path="/mobile/taskInvite" component={MobileTaskInvite} />
                    <Route path="/mobile" render={() => <MobileLogin activityElement={activityElement} />} />
                </Switch>
            </Router>
        </Suspense>
    );
}
