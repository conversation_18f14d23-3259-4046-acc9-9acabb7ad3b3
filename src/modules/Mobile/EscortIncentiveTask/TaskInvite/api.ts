import axios from 'axios';

const AxiosInstance = axios.create();

// 设置 Content-Type
const contentType = 'application/json; charset=utf-8';
AxiosInstance.defaults.headers['Content-Type'] = contentType;
const successStatus = 200;

const TASK_ID = 13;

export async function addInviteCustData(params: {
    city: string;
    inviteCompany: string;
    inviteOfficial: string;
    inviteOfficialPhone: number;
    userId: number;
}) {
    const addInvitePath = '/OptCenter/InspireTaskHttpService/addInviteCustData';
    const reqParams = {...params, taskId: TASK_ID};
    try {
        const response = await AxiosInstance.post(addInvitePath, reqParams);
        if (response.status === successStatus && response.data.status === successStatus) {
            return response.data;
        }
        return Promise.reject(response.data);
    }
    catch (error) {
        throw error;
    }
}
