.task-invite {
    background: linear-gradient(180deg, #FD3753 25%, #<PERSON>FA<PERSON> 100%);

    &-top {
        width: 100%;
    }

    &-content-wrap {
        height: 504px;
        box-sizing: border-box;
        background-color: #fff;
        border-radius: 10px;
        margin: 12px 22px 32px 22px;
        padding: 16px;
        display: flex;
        justify-content: center;

        .task-invite-content {
            width: 400px;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;

            &-tip {
                font-size: 12px;
                color: #A8B0BF;
                margin-top: 24px;
            }

            &-submit {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 311px;
                height: 36px;
                border-radius: 6px;
                background: linear-gradient(90deg, #FF875C 0%, #FF6537 100%);
                font-size: 14px;
                font-weight: 500;
                color: #fff;
                margin-top: 20px;
            }

            &-form {
                .one-input-all-container-has-focused .one-input {
                    box-shadow: none;
                }

                .one-input-all-container.one-invalid .one-input,
                .one-input-all-container-error .one-input {
                    border: none;
                    border-bottom: 1px solid #d9150b;
                    box-shadow: none;
                }
            }
        }
    }

    &-bottom {
        height: 36px;
    }
}

.invalid-link {
    text-align: center;
}
