import React, {useEffect, useRef, useState} from 'react';
import {isEmpty} from 'lodash-es';
import {Form, Input, Select, Toast, Dialog} from '@baidu/one-ui';
import {useURLQuery} from '@/hooks/tableList/query';
import taskInviteSvg from '@/styles/assets/taskInvite.png';
import {addInviteCustData} from './api';
import './style.global.less';

const Field = Form.Field;
const Option = Select.Option;

const cityList = ['福建省', '重庆市', '武汉市', '济南市'];
const cityOptions = cityList.map(city => ({value: city, label: city}));

const rulesConfig = {
    city: [{required: true, message: '请选择城市'}],
    inviteCompany: [{required: true, message: '请输入公司名称'}],
    inviteOfficial: [{required: true, message: '请输入公司负责人姓名'}],
    inviteOfficialPhone: [
        {required: true, message: '请输入公司负责人手机号'},
        {pattern: new RegExp(/^[1]([3-9])[0-9]{9}$/), message: '手机号码格式不正确'},
    ],
};

export default function MobileTaskInvite() {
    const [query] = useURLQuery<{userId: string}>();
    const formRef = useRef<any>({});
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        document.title = '百度营销-邀请新客';
    }, []);

    const onSubmit = async () => {
        const values = await formRef.current?.validateFieldsAndScroll();
        if (values && !loading) {
            const params = {...values, userId: atob(query.userId)};
            setLoading(true);
            addInviteCustData(params).then(res => {
                if (!isEmpty(res.data)) {
                    Dialog.confirm({
                        content: '信息提交成功，后续服务有专人和您联系',
                        footer: [],
                        size: 'small',
                        width: '300px',
                        maskClosable: true,
                    });
                    formRef.current?.resetFields();
                }
            }).catch(err => {
                const errMsg = err?.errors?.[0]?.message || '提交失败';
                Toast.error({content: errMsg, duration: 5});
            }).finally(() => {
                setLoading(false);
            });
        }
    };

    if (!query.userId) {
        return <h1 className="invalid-link">无效链接</h1>;
    }

    return (
        <div className="task-invite">
            <img className="task-invite-top" src={taskInviteSvg} />
            <div className="task-invite-content-wrap">
                <div className="task-invite-content">
                    <Form className="task-invite-content-form" labelPosition="top" ref={formRef}>
                        <Field label="所在城市" name="city" rules={rulesConfig.city}>
                            <Select>
                                {cityOptions.map(
                                    item => <Option key={item.value} value={item.value}>{item.label}</Option>
                                )}
                            </Select>
                        </Field>
                        <Field label="公司名称" name="inviteCompany" rules={rulesConfig.inviteCompany}>
                            <Input placeholder="请输入公司名称" type="inline" />
                        </Field>
                        <Field label="公司负责人姓名" name="inviteOfficial" rules={rulesConfig.inviteOfficial}>
                            <Input placeholder="请输入" type="inline" />
                        </Field>
                        <Field label="公司负责人手机号" name="inviteOfficialPhone" rules={rulesConfig.inviteOfficialPhone}>
                            <Input placeholder="请输入" type="inline" />
                        </Field>
                    </Form>
                    <div className="task-invite-content-tip">本活动最终解释权归百度营销服务联络中心所有</div>
                    <div className="task-invite-content-submit" onClick={onSubmit}>提交</div>
                </div>
            </div>
            <div className="task-invite-bottom"></div>
        </div>
    );
}
