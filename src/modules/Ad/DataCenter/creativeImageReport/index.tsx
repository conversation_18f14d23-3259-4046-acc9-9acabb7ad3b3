import {useLocation} from 'react-router-dom';
import queryString from 'query-string';
import {getUserId} from '@/utils';
import {formatString} from '@/utils/string';
import {appendQuery} from '@/utils/route';
import {getUrlWithQinggeQuery} from '@/utils/link';
import {PLATFORM_ENUM, PLATFORM_KEY_MAP} from '@/dicts';
import {useGlobalProductContext} from '@/hooks/productLine';
import {SingletonIframeInDataCenter} from '../fcCommon/SingletonIframeInDataCenter';

const stage = process.env.STAGE || 'ONLINE';
const customReportConfig = [
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FC],
        name: '凤巢',
        url: ({
            ONLINE: 'https://fengchao.baidu.com/fc/datacenter/qingge/image/user/${userId}',
            PREONLINE: 'https://fengchao.baidu-int.com/fc/datacenter/qingge/image/user/${userId}',
            TEST: 'http://fctest.baidu.com:${port}/fc/datacenter/qingge/image/user/${userId}',
        })[stage],
    },
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FEED],
        name: '信息流',
        url: ({
            ONLINE: 'https://feedads.baidu.com/fd/datacenter/idea/pictureReport?userId=${userId}',
            // ONLINE: 'http://dev.feedads.baidu.com:8800/fd/datacenter/idea/pictureReport?userId=${userId}',
            PREONLINE: 'https://feedads.baidu-int.com/fd/datacenter/idea/pictureReport?userId=${userId}',
            TEST: 'http://fcfeed.baidu.com:${port}/fd/datacenter/idea/pictureReport?userId=${userId}',
        })[stage],
    },
];

function CreativeImageReport() {
    const {search} = useLocation();
    const {platform: queryProduct, ...restQueryParams} = queryString.parse(search);
    const {product} = useGlobalProductContext();
    const tabKey = PLATFORM_KEY_MAP[product];

    const url = formatString(
        customReportConfig.find(i => i.key === tabKey)?.url || '',
        {userId: getUserId(), port: location.port}
    );
    const urlWirhQuery = appendQuery(
        getUrlWithQinggeQuery(url),
        {
            in: 'iframe',
            'host_app': 'qingge',
            ...restQueryParams,
        });

    const groupProps = {
        value: tabKey,
        onChange: (e: any) => {
            onChangeTabKey(e.target.value);
        },
        className: 'aix-report-radio-group',
        theme: 'light-ai',
    } as const;
    return (
        <div className="aix-report-container">
            <div className="report-header">
                <div className="report-title">创意图片报告</div>
            </div>
            {
                tabKey === PLATFORM_KEY_MAP[PLATFORM_ENUM.FC]
                    ? (
                        <SingletonIframeInDataCenter
                            urlWirhQuery={urlWirhQuery}
                        />
                    )
                    : (
                        <iframe
                            src={urlWirhQuery}
                            frameBorder="0"
                            width="100%"
                        />
                    )
            }
        </div>
    );
}

export default CreativeImageReport;
