import {useLocation} from 'react-router-dom';
import queryString from 'query-string';
import {getUserId} from '@/utils';
import {formatString} from '@/utils/string';
import {appendQuery} from '@/utils/route';
import {getUrlWithQinggeQuery} from '@/utils/link';
import {PLATFORM_ENUM, PLATFORM_KEY_MAP} from '@/dicts';
import {useProductTabSelector} from '../hooks';
import {SingletonIframeInDataCenter} from '../fcCommon/SingletonIframeInDataCenter';

const stage = process.env.STAGE || 'ONLINE';
const customReportConfig = [
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FC],
        name: '凤巢',
        url: ({
            ONLINE: 'https://fengchao.baidu.com/fc/datacenter/qingge/idmp/user/${userId}',
            PREONLINE: 'https://fengchao.baidu-int.com/fc/datacenter/qingge/idmp/user/${userId}',
            TEST: 'http://fctest.baidu.com:${port}/fc/datacenter/qingge/idmp/user/${userId}',
        })[stage],
    },
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FEED],
        name: '信息流',
        url: ({
            ONLINE: 'https://feedads.baidu.com/fd/datacenter/special/aftereffectMeasureReport?userId=${userId}',
            PREONLINE: 'https://feedads.baidu-int.com/fd/datacenter/special/aftereffectMeasureReport?userId=${userId}',
            TEST: 'http://fcfeed.baidu.com:${port}/fd/datacenter/special/aftereffectMeasureReport?userId=${userId}',
        })[stage],
    },
];

function AftereffectMeasureReport() {
    const {search} = useLocation();
    const {platform: queryProduct = '', ...restQueryParams} = queryString.parse(search);
    const [tabKey, onChangeTabKey] = useProductTabSelector({queryProduct});

    const url = formatString(
        customReportConfig.find(i => i.key === tabKey)?.url || '',
        {userId: getUserId(), port: location.port}
    );
    const urlWirhQuery = appendQuery(
        getUrlWithQinggeQuery(url),
        {
            in: 'iframe',
            'host_app': 'qingge',
            ...restQueryParams,
        });

    return (
        <div className="aix-report-container">
            <div className="report-header">
                <div className="report-title">转化效果度量报告</div>
            </div>
            {
                tabKey === PLATFORM_KEY_MAP[PLATFORM_ENUM.FC]
                    ? (
                        <SingletonIframeInDataCenter
                            urlWirhQuery={urlWirhQuery}
                        />
                    )
                    : (
                        <iframe
                            src={urlWirhQuery}
                            frameBorder="0"
                            width="100%"
                        />
                    )
            }

        </div>
    );
}

export default AftereffectMeasureReport;
