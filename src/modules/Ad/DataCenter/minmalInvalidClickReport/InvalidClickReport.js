// @ts-check
import {PureComponent} from 'react';
import ReportSdkEntry from '@baidu/cube-sdk';
import dayjs from 'dayjs';
import {noop} from 'lodash-es';
import {
    SelectFilter,
    getQueryScore,
    InvalidClickCustomDash,
    InvalidClickBatchEditBar,
    InvalidClickInlineOpt,
    DateTimeFilter,
    getDateTimeFilterParams,
} from 'dataCenter/components';
import {reportTypeMap, oCommentAppId, fcTimeUnit} from '../fcCommon/config';

const {minmalInvalidClickIpClick, minmalInvalidClickIpRealtime} = reportTypeMap;

const content = ['由于实时ip涉及数据量过大，仅支持48小时内分钟级别ip查询'];
const tabType = {
    ipQueryTab: 'ipQueryTab',
    realTimeIpQueryTab: 'realTimeIpQueryTab',
};
// 特殊的查询范围时间
const specialDateMinValue = dayjs().subtract(6, 'month').format('YYYY/MM/DD');

export default class InvalidClickReport extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            queryScope: {
                planList: [],
                unitList: [],
                unitListMap: {},
            },
            businessPoint: {
                businessPointList: [],
                selectedIds: [],
            },
        };
    }
    setCacheValue = (key, value) => {
        this.setState(oldState => {
            return {
                [key]: {
                    ...oldState[key],
                    ...value,
                },
            };
        });
    };
    componentDidMount() {
        const {
            commonConfig, handleSmartQuery = noop, customColumnfilters, sortRule,
            cardType, rowCount, topCount,
            globalDefaultDate, handleGlobalDateChange,
        } = this.props;
        const me = this;
        const {userId, token} = commonConfig.hairuo;
        const queryScoreSelectorData = getQueryScore(me, {isUniformName: true, setCacheValue: this.setCacheValue});
        this.config = {
            ...commonConfig,
            config: {
                isNeedCompare: false,
                pageTitle: '无效点击报告',
                withNewCategory: true,
                filterAreaConfig: {
                    filterList: ['device'], // 头部筛选
                    datePickerMinValue: specialDateMinValue,
                    filterData: {
                        timeUnit: fcTimeUnit,
                        queryScopeSelector: queryScoreSelectorData,
                    },
                    filterNodeMap: {
                        queryScopeSelector: <SelectFilter {...queryScoreSelectorData} />,
                    },
                    // 是否展示全局时间的switch开关
                    isShowGlobalDateSwitch: true,
                    // 是否打开全局时间初始值，非受控，当关闭全局时间时，globalDefaultDate 是undefined
                    isGlobalDateChecked: true,
                    // 隐藏时间切换开关
                    hiddenGlobalDateSwitchBtn: true,
                    // 基准时间初始值，如果报告除了全局时间外，有初始基准时间，可以这么写
                    // datePickerDefaultValue: globalDefaultDate || DEFAULT_DATE
                    datePickerDefaultValue: globalDefaultDate,
                    isMccSearchAble: true,
                    navs: [{
                        label: '计费IP查询',
                        value: tabType.ipQueryTab,
                    }, {
                        label: '计费实时IP查询',
                        value: tabType.realTimeIpQueryTab,
                    }],
                    onNavChange: () => {
                        this.sdkInit(this.realTimeConfig, tabType.realTimeIpQueryTab);
                    },
                },
                // 表头筛选配置
                filter: {
                    stringFilterMaxLength: 100,
                    useNewRegionDatasource: true,
                    defaultFilterOperatorMap: {
                        STRING: 'IN',
                    },
                },
                cellRenderConfig: {
                    contentFormats: { // 和renders有啥区别？
                        //
                    },
                },
                // ...(
                //     (roles.isSmartQueryArea() && location.pathname.includes('datacenter'))
                //         ? {
                //             // 智能数据查询区
                //             smartQueryAreaConfig: {
                //                 useInputQuery: roles.isUseInputQuery(),
                //                 smartReportType: reportTypeMap.invalidClickNormal,
                //                 source: 101,
                //                 customColumnfilters,
                //                 sortRule,
                //                 cardType, rowCount, topCount,
                //             }
                //         } : {}
                // ),
                // 图表区配置，数组项对应一个tab
                reportArea: {
                    // 图表上方自定义数据展示看板
                    InvalidClickCustomDashRender: () => {
                        const InvalidClickCustomDashProps = {
                            token,
                            userId,
                            getEntry: () => {
                                return {
                                    sdkEntry: this.sdkEntry,
                                };
                            },
                            reportType: 2300012,
                        };
                        this.invalidClickCustomDash = <InvalidClickCustomDash {...InvalidClickCustomDashProps} />;
                        return this.invalidClickCustomDash;
                    },
                    tabs: [
                        {
                            tabLabel: '',
                            chartAreaList: [
                                {
                                    chartType: 'line',
                                    titleArea: {
                                        title: '近期数据趋势',
                                    },
                                    // 支持指标对比
                                    isCompareIndicator: true,
                                    // chartHeight: '200px',
                                    // legend是否独自一行
                                    isLegendTitleOccupyOneLine: true,
                                    chartReportType: 2300012,
                                    // 是否响应时间窗
                                    isResponseTimeWindow: true,
                                    // 是否响应时间对比
                                    isResponseCompare: false,
                                    // 是否显示下拉指标相关
                                    isShowIndicator: true,
                                    indicatorUiType: 'dropdown',
                                    /* eslint-disable */
                                        // 是否响指标button改变 ?
                                        // isResponseIndicatorButtonChange: true,
                                        indicatorList: [
                                            {label: '展现', value: 'impression', unit: '次', isIndicator: true, dataType: {precision: 0}},
                                            {label: '滤前总点击', value: 'sumClicks', unit: '次', isIndicator: true, dataType: {precision: 0}},
                                            {label: '滤前总点击金额', value: 'sumCost', unit: '元', isIndicator: true, dataType: {precision: 2}},
                                            {label: '无效点击', value: 'freeClicks', unit: '次', isIndicator: true, dataType: {precision: 0}},
                                            {label: '无效点击金额', value: 'freeCost', unit: '元', isIndicator: true, dataType: {precision: 2}},
                                            {label: '计费点击', value: 'validClicks', unit: '次', isIndicator: true, dataType: {precision: 0}},
                                            {label: '计费点击金额', value: 'validCost', unit: '元', isIndicator: true, dataType: {precision: 2}},
                                            {label: '不计费点击占比', value: 'unchargedClicksRatio', unit: '%', isIndicator: true, dataType: {isPercent: true, precision: 2}},
                                            {label: '点击率', value: 'ctr', unit: '%', isIndicator: true, dataType: {isPercent: true, precision: 2}},
                                            {label: '平均点击价格', value: 'cpc', unit: '元', isIndicator: true, dataType: {precision: 2}}
                                        ],
                                        // 是否简约button ?
                                        isSingleIndicatorButton: true,
                                        // x轴的字段
                                        xAxisField: 'date',
                                        xAxisFieldFormatter: value => value,
                                        // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                                        defaultIndicator: 'impression',
                                        isLegendCutOff: true,
                                        legendItemWidth: 100
                                    }
                                ],
                                tableAreaConfig: {
                                    withNewSortableSelector: true,
                                    showPartFields: true,
                                    isOComment: true,
                                    oCommentAppId: oCommentAppId,
                                    indicatorUiType: 'dropdown',
                                    isUseCustomerField: true,
                                    // 页面初始化时的表格reportType
                                    tableReportType: minmalInvalidClickIpClick,
                                    additionFieldCommonFilter: {
                                        campaignId: ['campaignNameStatus'],
                                        adgroupId: ['campaignNameStatus', 'adGroupNameStatus']
                                    },
                                    rowSelectionOption: {
                                        [minmalInvalidClickIpClick]: {
                                            needRowSelection: true,
                                            // 多账户是否需要多选框，默认false
                                            mccRowSelection: false,
                                            rowKey: row => {
                                                const targetField = 'queryId';
                                                const targetFieldValue = Array.isArray(row[targetField])
                                                    ? row[targetField] && row[targetField][0]
                                                    : row[targetField];
                                                return `${targetFieldValue}_${row.rowIndex}` || 'sumId';
                                            },
                                            BatchBarRender: () => {
                                                const batchEditProps = {
                                                    me,
                                                    userId,
                                                    getEntry: () => {
                                                        return {
                                                            sdkEntry: this.sdkEntry
                                                        };
                                                    }
                                                };
                                                this.invalidClickBatchEditBar = <InvalidClickBatchEditBar {...batchEditProps} />;
                                                return this.invalidClickBatchEditBar;
                                            }
                                        }
                                    },
                                    tableIndicatorList: [
                                        {
                                            label: '计费ip查询',
                                            // value对应reportType，因为指标切换是对reportType的切换
                                            value: minmalInvalidClickIpClick,
                                            customerFields: [{
                                                columnText: '操作',
                                                cellRender: (text, column) => {
                                                    const opytProps = {
                                                        me,
                                                        text,
                                                        column,
                                                        userId
                                                    }
                                                    return <InvalidClickInlineOpt {...opytProps}/>
                                                }
                                            }]
                                        }
                                    ]
                                }
                            }
                        ]
                    }
                }
            };
        this.realTimeConfig = {
            ...commonConfig,
            config: {
                // isNeedMcc: false,
                isNeedCompare: false,
                filterRenderConfig: {
                    dateTimeSec: props => {
                        const {initData} = props;
                        const dateTimeProps = {
                            initData,
                            getEntry: () => {
                                return {
                                    sdkEntry: this.sdkEntry
                                };
                            },
                            isNeedSecond: true,
                            columnName: 'dateTimeSec'
                        };
                        this.dateTimeSec = <DateTimeFilter {...dateTimeProps} />;
                        return this.dateTimeSec;
                    }
                },
                pageTitle: '无效点击报告',
                filterAreaConfig: {
                    isMccSearchAble: true,
                    hideDatePicker: true, // 隐藏时间组件
                    navs: [{
                        label: '计费IP查询',
                        value: tabType.ipQueryTab
                    }, {
                        label: '计费实时IP查询',
                        value: tabType.realTimeIpQueryTab
                    }],
                    onNavChange: key => {
                        this.sdkInit(this.config, tabType.ipQueryTab);
                    }
                },
                // 表头筛选配置
                filter: {
                    stringFilterMaxLength: 100,
                    useNewRegionDatasource: true,
                    defaultFilterOperatorMap: {
                        STRING: 'IN'
                    },
                    innerCustomFilterMap: {
                        // 时间列可查询得最长天数
                        dateTimeSec: {
                            defaultValue: [0, 2880],
                            filterLabelFormat: params => {
                                const {time: endTime} = getDateTimeFilterParams((params && params[0]) || 0, true);
                                const {time: startTime} = getDateTimeFilterParams((params && params[1]) || 2880, true);
                                return `时间范围：${startTime}至${endTime}`;
                            },
                            getFilters: params => {
                                const {date: endDate, time: endTime}
                                = getDateTimeFilterParams((params && params[0]) || 0, true);
                                const {date: startDate, time: startTime}
                                = getDateTimeFilterParams((params && params[1]) || 2880, true);
                                return {
                                    outerFilters: {
                                        startDate,
                                        endDate
                                    },
                                    innerFilters: [
                                        {
                                            column: 'dateTimeSec',
                                            operator: 'GT',
                                            values: [startTime]
                                        },
                                        {
                                            column: 'dateTimeSec',
                                            operator: 'LT',
                                            values: [endTime]
                                        }
                                    ]
                                };
                            }
                        }
                    }
                },
                cellRenderConfig: {
                    contentFormats: { // 和renders有啥区别？
                        //
                    }
                },
                // ...(
                //     (roles.isSmartQueryArea() && location.pathname.includes('datacenter'))
                //         ? {
                //             // 智能数据查询区
                //             smartQueryAreaConfig: {
                //                 useInputQuery: roles.isUseInputQuery(),
                //                 smartReportType: invalidClickRealTime,
                //                 source: 101,
                //                 customColumnfilters,
                //                 sortRule,
                //                 cardType, rowCount, topCount,
                //             }
                //         } : {}
                // ),
                // 图表区配置，数组项对应一个tab
                reportArea: {
                    tabs: [
                        {
                            tabLabel: '',
                            tableAreaConfig: {
                                indicatorUiType: 'dropdown',
                                isOComment: true,
                                oCommentAppId: oCommentAppId,
                                isUseCustomerField: false,
                                // 页面初始化时的表格reportType
                                tableReportType: minmalInvalidClickIpRealtime,
                                showPartFields: true,
                                rowSelectionOption: {
                                    [minmalInvalidClickIpRealtime]: {
                                        needRowSelection: true,
                                        // 多账户是否需要多选框，默认false
                                        mccRowSelection: false,
                                        rowKey: row => {
                                            const targetField = 'queryId';
                                            const targetFieldValue = Array.isArray(row[targetField])
                                                ? row[targetField] && row[targetField][0]
                                                : row[targetField];
                                            return `${targetFieldValue}_${row.rowIndex}` || 'sumId';
                                        },
                                        BatchBarRender: () => {
                                            const batchEditProps = {
                                                me,
                                                userId,
                                                getEntry: () => {
                                                    return {
                                                        sdkEntry: this.sdkEntry
                                                    };
                                                }
                                            };
                                            this.invalidClickBatchEditBar = <InvalidClickBatchEditBar {...batchEditProps} />;
                                            return this.invalidClickBatchEditBar;
                                        }
                                    }
                                },
                                tableIndicatorList: [
                                    {
                                        value: minmalInvalidClickIpRealtime,
                                        customerFields: [{
                                            columnText: '操作',
                                            cellRender: (text, column) => {
                                                const opytProps = {
                                                    me,
                                                    text,
                                                    column,
                                                    userId
                                                }
                                                return <InvalidClickInlineOpt {...opytProps}/>
                                            }
                                        }]
                                    }
                                ]
                            }
                        }
                    ]
                }
            }
        };
        this.sdkInit(this.config);
        this.sdkEntry.on('sdk.event.globalDate.change', handleGlobalDateChange);
        this.sdkEntry.on('sdk.event.smartQuery.search', handleSmartQuery);
    }
    componentWillUnmount() {
        this.sdkEntry.destroy();
    }
    sdkInit = (config, tabType) => {
        config.config.filterAreaConfig.defaultActiveNav = tabType;
        //@ts-ignore
        this.sdkEntry = new ReportSdkEntry(config);
        this.sdkEntry.init('cube-sdk-invalid-click-report');
    }
    render() {
        return (
            <div id="cube-sdk-invalid-click-report" style={{width: '100%'}} />
        );
    }
}
