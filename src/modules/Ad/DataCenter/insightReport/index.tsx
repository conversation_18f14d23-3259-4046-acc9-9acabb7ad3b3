import {getUserId} from '@/utils';
import {formatString} from '@/utils/string';
import {appendQuery} from '@/utils/route';
import {getUrlWithQinggeQuery} from '@/utils/link';
import {PLATFORM_ENUM, PLATFORM_KEY_MAP} from '@/dicts';
import '../index.global.less';
import {SingletonIframeInDataCenter} from '../fcCommon/SingletonIframeInDataCenter';

const stage = process.env.STAGE || 'ONLINE';
const customReportConfig = [
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FC],
        name: '凤巢',
        url: ({
            ONLINE: 'https://fengchao.baidu.com/fc/datacenter/qingge/insight/user/${userId}',
            PREONLINE: 'https://fengchao.baidu-int.com/fc/datacenter/qingge/insight/user/${userId}',
            TEST: 'http://fctest.baidu.com:${port}/fc/datacenter/qingge/insight/user/${userId}',
        })[stage],
    },
];

function InsightReport() {
    const url = formatString(
        customReportConfig.find(i => i.key === PLATFORM_KEY_MAP[PLATFORM_ENUM.FC])?.url || '',
        {userId: getUserId(), port: location.port}
    );
    const urlWirhQuery = appendQuery(getUrlWithQinggeQuery(url), {in: 'iframe', 'host_app': 'qingge'});
    return (
        <div className="aix-report-container">
            <div className="report-header">
                <div className="report-title">数据简报</div>
            </div>
            <SingletonIframeInDataCenter
                urlWirhQuery={urlWirhQuery}
            />
        </div>
    );
}

export default InsightReport;
