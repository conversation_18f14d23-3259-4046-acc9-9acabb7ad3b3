import {getUserId} from '@/utils';
import {formatString} from '@/utils/string';
import {appendQuery} from '@/utils/route';
import {getUrlWithQinggeQuery} from '@/utils/link';
import {PLATFORM_ENUM, PLATFORM_KEY_MAP} from '@/dicts';
import {useGlobalProductContext} from '@/hooks/productLine';
import '../index.global.less';
import {SingletonIframeInDataCenter} from '../fcCommon/SingletonIframeInDataCenter';

const stage = process.env.STAGE || 'ONLINE';
const customReportConfig = [
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FC],
        name: '凤巢',
        url: ({
            ONLINE: 'https://fengchao.baidu.com/fc/datacenter/qingge/program/user/${userId}',
            PREONLINE: 'https://fengchao.baidu-int.com/fc/datacenter/qingge/program/user/${userId}',
            TEST: 'http://fctest.baidu.com:${port}/fc/datacenter/qingge/program/user/${userId}',
        })[stage],
    },
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FEED],
        name: '信息流',
        url: ({
            ONLINE: 'https://feedads.baidu.com/fd/datacenter/special/programReport?userId=${userId}',
            PREONLINE: 'https://feedads.baidu-int.com/fd/datacenter/special/programReport?userId=${userId}',
            TEST: 'http://fcfeed.baidu.com:${port}/fd/datacenter/special/programReport?userId=${userId}',
        })[stage],
    },
];

function ProgramReport() {
    const {product} = useGlobalProductContext();

    const url = formatString(
        customReportConfig.find(i => i.key === PLATFORM_KEY_MAP[product])?.url || '',
        {userId: getUserId(), port: location.port}
    );

    const urlWirhQuery = appendQuery(getUrlWithQinggeQuery(url), {in: 'iframe', 'host_app': 'qingge'});
    return (
        <div className="aix-report-container">
            <div className="report-header">
                <div className="report-title">小程序报告</div>
            </div>
            {
                product === PLATFORM_ENUM.FC
                    ? (
                        <SingletonIframeInDataCenter
                            urlWirhQuery={urlWirhQuery}
                        />
                    )
                    : (
                        <iframe
                            src={urlWirhQuery}
                            frameBorder="0"
                            width="100%"
                        />
                    )
            }
        </div>
    );
}

export default ProgramReport;
