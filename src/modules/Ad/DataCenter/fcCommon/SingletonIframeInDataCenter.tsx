import {useMemo} from 'react';
import SingletonIframe from '@/components/common/iframe/SingletonIframe';

export function SingletonIframeInDataCenter({urlWirhQuery}: {
    urlWirhQuery: string;
}) {
    const iframePath = useMemo(() => {
        const {search, pathname} = new URL(urlWirhQuery);
        return pathname + search;
    }, [urlWirhQuery]);
    return (
        <SingletonIframe
            iframePath={iframePath}
            style={{
                width: '100%',
            }}
        />
    );
}
