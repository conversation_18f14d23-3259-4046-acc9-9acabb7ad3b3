/**
 @file 全部账户报告看板
 <AUTHOR>
 @date 2023-08-01
*/
/* eslint-disable */
import {useEffect, useState} from 'react';
import ReportSdkEntry from '@baidu/cube-sdk';
import BaiduMarketTip from 'commonLibs/BaiduMarketTip';
import classNames from 'classnames';
import {noop} from 'lodash-es';
import {PRODUCT} from '@/dicts/campaign';
import {reportTypeMap, oCommentAppId, fcTimeUnitWithHour} from '../fcCommon/config';
import {getSpecialColumnsRender} from '../fcCommon/format';
import {useCubeSdkCommonConfig} from '../fcCommon/report';

const reportType = reportTypeMap.icgQuery;

function QueryReport(props) {
    const [sdkEntry, setSdkEntry] = useState();
    const {
        commonConfig, globalDefaultDate, handleGlobalDateChange, handleSmartQuery = noop,
        handleTimeStatusChange, newReportInteract, dateMinValue, handleDateChange, dateData
    } = props;
    const reportCls = classNames({
        'new-report-interact': newReportInteract && !dateData?.data.checked,
        'new-report-interact-compare': newReportInteract && dateData?.data.checked

    });
    useEffect(() => {
        const config = {
            ...commonConfig,
            tableReportType: reportType,
            config: {
                withNewCategory: true,
                pageTitle: '搜索词报告',
                isShowMultipleAccountCharts: true,
                // 是否需要比较
                isNeedCompare: true,
                filterAreaConfig: {
                    filterList: ['timeUnit'],
                    titleTip: <BaiduMarketTip />,
                    filterData: {
                        timeUnit: fcTimeUnitWithHour
                    },
                    datePickerMinValue: dateMinValue || '2018/11/01',
                    // 是否打开全局时间初始值，非受控，当关闭全局时间时，globalDefaultDate 是undefined
                    isGlobalDateChecked: true,
                    // 隐藏时间切换开关
                    hiddenGlobalDateSwitchBtn: true,
                    // 基准时间初始值，如果报告除了全局时间外，有初始基准时间，可以这么写
                    // datePickerDefaultValue: globalDefaultDate || DEFAULT_DATE
                    datePickerDefaultValue: globalDefaultDate
                },
                // 表头筛选配置
                filter: {
                    stringFilterMaxLength: 100,
                    useNewRegionDatasource: true
                },
                cellRenderConfig: {
                    contentFormats: {
                        regionFormat: ({column, columnName}) => {
                            const field = column[columnName];
                            return getSpecialColumnsRender(field);
                        }
                    }
                },
                // 图表区配置，数组项对应一个tab
                reportArea: {
                    // 是否指标对比
                    // isIndicatorCompare: true,
                    tabs: [{
                        // 一期的表格配置,和tableTabList的value对应
                        tableAreaConfig: {
                            // 页面初始化时的表格reportType
                            tableReportType: reportType,
                            withNewSortableSelector: true,
                            showPartFields: true,
                            isOComment: true,
                            oCommentAppId: oCommentAppId,
                            extraStaticFilters: [{
                                column: 'productLine',
                                operator: 'IN',
                                values: [PRODUCT.B2B_PROMOTION]
                            }]
                        }
                    }]
                }
            }
        };
        const sdk = new ReportSdkEntry(config);
        sdk.init('cube-sdk-all-account-report');
        sdk.on('sdk.event.globalDate.change', handleGlobalDateChange);
        sdk.on('sdk.event.date.change', handleDateChange);
        sdk.on('sdk.event.page.filter.changed', handleTimeStatusChange);
        sdk.on('sdk.event.smartQuery.search', handleSmartQuery);
        setSdkEntry(sdk);
        return () => {
            sdkEntry?.destroy();
        };
    }, []);
    return (
        <div id="cube-sdk-all-account-report" className={reportCls}>

        </div>
    );
}

function QueryReportContainer() {
    const props = useCubeSdkCommonConfig('cube_sdk_query');
    return <QueryReport {...props} />;
}

export default QueryReportContainer;
