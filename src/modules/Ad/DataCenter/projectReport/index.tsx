import {useLocation} from 'react-router-dom';
import queryString from 'query-string';
import {getUserId} from '@/utils';
import {useGlobalProductContext} from '@/hooks/productLine';
import {formatString} from '@/utils/string';
import {appendQuery} from '@/utils/route';
import {getUrlWithQinggeQuery} from '@/utils/link';
import {PLATFORM_ENUM, PLATFORM_KEY_MAP} from '@/dicts';
import {SingletonIframeInDataCenter} from '../fcCommon/SingletonIframeInDataCenter';

const stage = process.env.STAGE || 'ONLINE';
const customReportConfig = [
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FC],
        name: '凤巢',
        url: ({
            ONLINE: 'https://fengchao.baidu.com/fc/datacenter/qingge/projectReport/user/${userId}',
            PREONLINE: 'https://fengchao.baidu-int.com/fc/datacenter/qingge/projectReport/user/${userId}',
            TEST: 'http://fctest.baidu.com:${port}/fc/datacenter/qingge/projectReport/user/${userId}',
        })[stage],
    },
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FEED],
        name: '信息流',
        url: ({
            ONLINE: 'https://feedads.baidu.com/fd/datacenter/special/projectReport?userId=${userId}',
            // ONLINE: 'http://dev.feedads.baidu.com:8800/fd/datacenter/special/projectReport?userId=${userId}',
            PREONLINE: 'https://feedads.baidu-int.com/fd/datacenter/special/projectReport?userId=${userId}',
            TEST: 'http://fcfeed.baidu.com:${port}/fd/datacenter/special/projectReport?userId=${userId}',
        })[stage],
    },
];

function ProjectReport() {
    const {search} = useLocation();
    const {platform: queryProduct, ...restQueryParams} = queryString.parse(search);

    const {product} = useGlobalProductContext();
    const tabKey = PLATFORM_KEY_MAP[product];

    const url = formatString(
        customReportConfig.find(i => i.key === tabKey)?.url || '',
        {userId: getUserId(), port: location.port}
    );
    const urlWirhQuery = appendQuery(
        getUrlWithQinggeQuery(url),
        {
            in: 'iframe',
            'host_app': 'qingge',
            ...restQueryParams,
        });

    return (
        <div className="aix-report-container">
            <div className="report-header">
                <div className="report-title">项目报告</div>
            </div>
            {
                tabKey === PLATFORM_KEY_MAP[PLATFORM_ENUM.FC]
                    ? (
                        <SingletonIframeInDataCenter
                            urlWirhQuery={urlWirhQuery}
                        />
                    )
                    : (
                        <iframe
                            src={urlWirhQuery}
                            frameBorder="0"
                            width="100%"
                        />
                    )
            }
        </div>
    );
}

export default ProjectReport;
