/**
 @file 全部账户报告看板
 <AUTHOR>
 @date 2023-08-01
*/
/* eslint-disable */
import {useEffect, useState} from 'react';
import ReportSdkEntry from '@baidu/cube-sdk';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import BaiduMarketTip from 'commonLibs/BaiduMarketTip';
import classNames from 'classnames';
import {noop} from 'lodash-es';
import {reportTypeMap, oCommentAppId, fcTimeUnitWithHour} from '../../fcCommon/config';
import {getSpecialColumnsRender} from '../../fcCommon/format';
import {useRecordByComponent} from '@/hooks';
import '../index.global.less';

const reportType = reportTypeMap.allAccount;

function AllAccountReport(props) {
    const {userId} = useUserInfo();
    const [sdkEntry, setSdkEntry] = useState();
    const {commonConfig, globalDefaultDate, handleGlobalDateChange, handleSmartQuery = noop,
        customColumnfilters, sortRule, cardType, rowCount, topCount,
        handleTimeStatusChange, newReportInteract, dateMinValue, handleDateChange, dateData} = props;
    const reportCls = classNames({
        'new-report-interact': newReportInteract && !dateData?.data.checked,
        'new-report-interact-compare': newReportInteract && dateData?.data.checked

    });
    useRecordByComponent({tag: 'AllAccountReport'});
    useEffect(() => {
        const config = {
            ...commonConfig,
            tableReportType: reportType,
            config: {
                withNewCategory: true,
                pageTitle: '',
                isShowMultipleAccountCharts: true,
                // 是否需要比较
                isNeedCompare: true,
                filterAreaConfig: {
                    filterList: ['timeUnit'],
                    titleTip: <BaiduMarketTip />,
                    filterData: {
                        timeUnit: fcTimeUnitWithHour
                    },
                    datePickerMinValue: dateMinValue || '2018/11/01',
                    // 是否打开全局时间初始值，非受控，当关闭全局时间时，globalDefaultDate 是undefined
                    isGlobalDateChecked: true,
                    // 隐藏时间切换开关
                    hiddenGlobalDateSwitchBtn: true,
                    // 基准时间初始值，如果报告除了全局时间外，有初始基准时间，可以这么写
                    // datePickerDefaultValue: globalDefaultDate || DEFAULT_DATE
                    datePickerDefaultValue: globalDefaultDate
                },
                // 表头筛选配置
                filter: {
                    stringFilterMaxLength: 100,
                    useNewRegionDatasource: true
                },
                cellRenderConfig: {
                    contentFormats: {
                        regionFormat: ({column, columnName}) => {
                            const field = column[columnName];
                            return getSpecialColumnsRender(field);
                        }
                    }
                },
                // 图表区配置，数组项对应一个tab
                reportArea: {
                    // 是否指标对比
                    // isIndicatorCompare: true,
                    tabs: [{
                        chartAreaList: [{
                            chartType: 'line',
                            chartReportType: reportType,
                            // 是否响应时间窗
                            isResponseTimeWindow: true,
                            // 是否响应时间对比
                            isResponseCompare: true,
                            // 是否显示下拉指标相关
                            isShowIndicator: true,
                            indicatorUiType: 'dropdown',
                            isCompareIndicator: true,
                            indicatorList: [
                                {label: '消费', btnLabel: '总消费', value: 'cost', unit: '元', dataType: {precision: 2}},
                                {label: '展现', btnLabel: '总展现', value: 'impression', unit: '次', dataType: {precision: 0}},
                                {label: '点击', btnLabel: '总点击', value: 'click', unit: '次', dataType: {precision: 0}},
                                {label: '平均点击率', btnLabel: '总点击率', value: 'ctr', unit: '%', dataType: {isPercent: true, precision: 2}},
                                {label: '平均点击价格', btnLabel: '平均点击价格', value: 'cpc', unit: '元', dataType: {precision: 2}}
                            ],
                            // x轴的字段
                            xAxisField: 'date',
                            // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                            defaultIndicator: 'cost',
                            operatorWidth: '256px',
                            isAddZeroRows: true
                        }],
                        // 一期的表格配置,和tableTabList的value对应
                        tableAreaConfig: {
                            // 页面初始化时的表格reportType
                            tableReportType: reportType,
                            withNewSortableSelector: true,
                            showPartFields: true,
                            isOComment: true,
                            oCommentAppId: oCommentAppId
                        }
                    }]
                }
            }
        };
        const sdk = new ReportSdkEntry(config);
        sdk.init('cube-sdk-all-account-report');
        sdk.on('sdk.event.globalDate.change', handleGlobalDateChange);
        sdk.on('sdk.event.date.change', handleDateChange);
        sdk.on('sdk.event.page.filter.changed', handleTimeStatusChange);
        sdk.on('sdk.event.smartQuery.search', handleSmartQuery);
        setSdkEntry(sdk);
        return () => {
            sdkEntry?.destroy();
        };
    }, []);
    return (
        <div id="cube-sdk-all-account-report" className={reportCls}>

        </div>
    );
}

export default AllAccountReport;
