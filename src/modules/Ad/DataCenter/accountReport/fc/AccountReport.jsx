/**
 @file 账户报告看板
 <AUTHOR>
 @date 2022-01-21 15:47:31
*/
/* eslint-disable */
import {PureComponent} from 'react';
import ReportSdkEntry from '@baidu/cube-sdk';
import classNames from 'classnames';
import {noop} from 'lodash-es';
import BaiduMarketTip from 'commonLibs/BaiduMarketTip';
import {reportTypeMap, oCommentAppId} from '../../fcCommon/config';
import {getSpecialColumnsRender} from '../../fcCommon/format';
import {showQuestionnaire} from '../../fcCommon/helper';
import '../index.global.less';

const reportType = reportTypeMap.fcAccount;
const dropdownIndicatorList = [
    {label: '消费', value: 'cost', unit: '元', dataType: {precision: 2}},
    {label: '展现', value: 'impression', unit: '次', dataType: {precision: 0}},
    {label: '点击', value: 'click', unit: '次', dataType: {precision: 0}}
];
const hourMap = [
    '0:00 ~ 1:00',
    '1:00 ~ 2:00',
    '2:00 ~ 3:00',
    '3:00 ~ 4:00',
    '4:00 ~ 5:00',
    '5:00 ~ 6:00',
    '6:00 ~ 7:00',
    '7:00 ~ 8:00',
    '8:00 ~ 9:00',
    '9:00 ~ 10:00',
    '10:00 ~ 11:00',
    '11:00 ~ 12:00',
    '12:00 ~ 13:00',
    '13:00 ~ 14:00',
    '14:00 ~ 15:00',
    '15:00 ~ 16:00',
    '16:00 ~ 17:00',
    '17:00 ~ 18:00',
    '18:00 ~ 19:00',
    '19:00 ~ 20:00',
    '20:00 ~ 21:00',
    '21:00 ~ 22:00',
    '22:00 ~ 23:00',
    '23:00 ~ 24:00'
];

class AccountReport extends PureComponent {
    componentDidMount() {
        const {commonConfig, globalDefaultDate, handleGlobalDateChange, handleSmartQuery = noop,
            customColumnfilters, sortRule, cardType, rowCount, topCount,
            fcTimeUnit, handleTimeStatusChange, dateMinValue, handleDateChange} = this.props;
        const {userId, optId} = commonConfig.hairuo;
        const config = {
            ...commonConfig,
            tableReportType: reportType,
            config: {
                withNewCategory: true,
                pageTitle: '',
                filterAreaConfig: {
                    filterList: ['timeUnit', 'device'],
                    titleTip: <BaiduMarketTip />,
                    datePickerMinValue: dateMinValue || '2018/11/01',
                    filterData: {
                        timeUnit: fcTimeUnit
                    },
                    // 是否展示全局时间的switch开关
                    isShowGlobalDateSwitch: true,
                    // 是否打开全局时间初始值，非受控，当关闭全局时间时，globalDefaultDate 是undefined
                    isGlobalDateChecked: true,
                    // 隐藏时间切换开关
                    hiddenGlobalDateSwitchBtn: true,
                    // 基准时间初始值，如果报告除了全局时间外，有初始基准时间，可以这么写
                    // datePickerDefaultValue: globalDefaultDate || DEFAULT_DATE
                    datePickerDefaultValue: globalDefaultDate,
                    isMccSearchAble: true
                },
                isShowMultipleAccountCharts: true,
                // 是否需要比较
                isNeedCompare: true,
                // 表头筛选配置
                filter: {
                    stringFilterMaxLength: 100,
                    useNewRegionDatasource: true
                },
                cellRenderConfig: {
                    contentFormats: {
                        regionFormat: ({column, columnName}) => {
                            const field = column[columnName];
                            return getSpecialColumnsRender(field);
                        }
                    }
                },
                // 图表区配置，数组项对应一个tab
                reportArea: {
                    titleArea: {
                        title: '重点关键词分析',
                        tip: '此处地域分布、时段分布图表不支持时间细分'
                    },
                    // 是否指标对比
                    isIndicatorCompare: true,
                    tabs: [{
                        tabLabel: '整体走势',
                        chartAreaList: [{
                            chartType: 'line',
                            chartReportType: reportType,
                            // 是否响应时间窗
                            isResponseTimeWindow: 'DAY',
                            // 是否响应时间对比
                            isResponseCompare: true,
                            // 是否显示下拉指标相关
                            isShowIndicator: true,
                            indicatorUiType: 'button',
                            isCompareIndicator: true,
                            /* eslint-disable */
                            indicatorList: [
                                {label: '消费', btnLabel: '总消费', value: 'cost', unit: '元', dataType: {precision: 2}},
                                {label: '展现', btnLabel: '总展现', value: 'impression', unit: '次', dataType: {precision: 0}},
                                {label: '上方展现胜出率', btnLabel: '上方展现胜出率', value: 'topPvWinA', unit: '%', dataType: {isPercent: true, precision: 2}},
                                {label: '上方展现胜出率（精确）', btnLabel: '上方展现胜出率（精确）', value: 'topPvWinP', unit: '%', dataType: {isPercent: true, precision: 2}},
                                {label: '点击', btnLabel: '总点击', value: 'click', unit: '次', dataType: {precision: 0}},
                                {label: '平均点击率', btnLabel: '总点击率', value: 'ctr', unit: '%', dataType: {isPercent: true, precision: 2}},
                                {label: '平均点击价格', btnLabel: '平均点击价格', value: 'cpc', unit: '元', dataType: {precision: 2}}
                            ],
                            /* eslint-disable */
                            // 是否响指标button改变
                            isResponseIndicatorButtonChange: true,
                            // 由函数决定是否展示指标button
                            checkIndicatorButtonShow: () => true,
                            // x轴的字段
                            xAxisField: 'date',
                            // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                            defaultIndicator: 'cost'
                        }],
                        // 一期的表格配置,和tableTabList的value对应
                        tableAreaConfig: {
                            // 页面初始化时的表格reportType
                            tableReportType: reportType,
                            withNewSortableSelector: true,
                            showPartFields: true,
                            isOComment: true,
                            oCommentAppId: oCommentAppId
                        }
                    },
                    {
                        tabLabel: '地域分布',
                        chartAreaList: [{
                            chartType: 'bar',
                            // titleArea: {
                            //     title: 'TOP地域{INDICATOR}'
                            // },
                            chartReportType: reportType,
                            // 是否响应时间窗
                            isResponseTimeWindow: 'SUMMARY',
                            // 是否响应时间对比
                            isResponseCompare: true,
                            // 是否显示下拉指标相关
                            isShowIndicator: true,
                            sortType: 'DESC',
                            indicatorUiType: 'dropdown',
                            indicatorList: dropdownIndicatorList,
                            // 改变指标时是否重新请求
                            isRefreshOnIndicatorChange: true,
                            // x轴的字段
                            xAxisField: 'provinceName',
                            // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                            defaultIndicator: 'cost',
                            // legendPosition: 'center'
                            // legend是否独自一行
                            isLegendTitleOccupyOneLine: true
                        }],
                        // 一期的表格配置,和tableTabList的value对应
                        tableAreaConfig: {
                            // 页面初始化时的表格reportType
                            tableReportType: reportType,
                            withNewSortableSelector: true,
                            showPartFields: true,
                            isOComment: true,
                            oCommentAppId: oCommentAppId
                        }
                    },
                    {
                        tabLabel: '时段分布',
                        chartAreaList: [{
                            chartType: 'bar',
                            // titleArea: {
                            //     title: '{INDICATOR}整体时段分布'
                            // },
                            chartReportType: reportType,
                            // 是否响应时间对比
                            isResponseCompare: true,
                            // 是否显示下拉指标相关
                            isShowIndicator: true,
                            // sortType: 'DESC',
                            // 是否响应时间窗
                            isResponseTimeWindow: 'SUMMARY',
                            indicatorUiType: 'dropdown',
                            indicatorList: dropdownIndicatorList,
                            // 改变指标时是否重新请求
                            isRefreshOnIndicatorChange: true,
                            // 是否响指标button改变
                            isResponseIndicatorButtonChange: true,
                            // x轴的字段
                            xAxisField: 'hour',
                            xAxisFieldFormatter: value => hourMap[value],
                            // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                            defaultIndicator: 'cost',
                            // legendPosition: 'center',
                            // legend是否独自一行
                            isLegendTitleOccupyOneLine: true,
                            isXAxisLabelInterval: true
                        }],
                        // 一期的表格配置,和tableTabList的value对应
                        tableAreaConfig: {
                            // 页面初始化时的表格reportType
                            tableReportType: reportType,
                            withNewSortableSelector: true,
                            showPartFields: true,
                            isOComment: true,
                            oCommentAppId: oCommentAppId
                        }
                    }]
                }
            }
        };
        //@ts-ignore
        this.sdkEntry = new ReportSdkEntry(config);
        this.sdkEntry.init('cube-sdk-account-report');
        this.sdkEntry.on('sdk.event.globalDate.change', handleGlobalDateChange);
        this.sdkEntry.on('sdk.event.page.filter.changed', handleTimeStatusChange);
        this.sdkEntry.on('sdk.event.date.change', handleDateChange);
        this.sdkEntry.on('sdk.event.smartQuery.search', handleSmartQuery);
        showQuestionnaire(this.sdkEntry);
    }
    componentWillUnmount() {
        this.sdkEntry.destroy();
    }
    render() {
        const reportCls = classNames({
            'new-report-interact': this.props.newReportInteract && !this.props.dateData?.data.checked,
            'new-report-interact-compare': this.props.newReportInteract && this.props.dateData?.data.checked

        });

        return (
            <div id="cube-sdk-account-report" className={reportCls}>

            </div>
        );
    }
}

export default AccountReport
