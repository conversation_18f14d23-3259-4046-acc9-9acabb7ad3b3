/*
 * @Author: fujunsheng
 * @Date: 2023-08-02
 * @Description: 服务直达账户层级报告
 */
/* eslint-disable */
import {useState, useEffect} from 'react';
import BaiduMarketTip from 'commonLibs/BaiduMarketTip';
import ReportSdkEntry from '@baidu/cube-sdk';
import classNames from 'classnames';
import {noop} from 'lodash-es';
import {reportTypeMap} from '../../fcCommon/config';
import {dashFormat, getSpecialColumnsRender} from '../../fcCommon/format';
const reportType = reportTypeMap.cplReport;

function CplAccountReport(props) {
    const [sdkEntry, setSdkEntry] = useState();
    const {commonConfig, globalDefaultDate, handleGlobalDateChange, handleSmartQuery = noop,
        customColumnfilters, sortRule, cardType, rowCount, topCount,
        fcTimeUnit, handleTimeStatusChange, dateMinValue, newReportInteract, dateData, handleDateChange} = props;

    const reportCls = classNames({
        'new-report-interact': newReportInteract && !dateData?.data.checked,
        'new-report-interact-compare': newReportInteract && dateData?.data.checked

    });

    useEffect(() => {
        const config = {
            ...commonConfig,
            tableReportType: reportType,
            config: {
                pageTitle: '',
                withNewCategory: true,
                filterAreaConfig: {
                    filterList: ['timeUnit'],
                    datePickerMinValue: dateMinValue || '2018/11/01',
                    titleTip: <BaiduMarketTip />,
                    filterData: {
                        timeUnit: fcTimeUnit
                    },
                    // 是否展示全局时间的switch开关
                    isShowGlobalDateSwitch: true,
                    // 是否打开全局时间初始值，非受控，当关闭全局时间时，globalDefaultDate 是undefined
                    isGlobalDateChecked: true,
                    // 隐藏时间切换开关
                    hiddenGlobalDateSwitchBtn: true,
                    // 基准时间初始值，如果报告除了全局时间外，有初始基准时间，可以这么写
                    // datePickerDefaultValue: globalDefaultDate || DEFAULT_DATE
                    datePickerDefaultValue: globalDefaultDate,
                    isMccSearchAble: true
                },
                // 表头筛选配置
                filter: {
                    stringFilterMaxLength: 100,
                    useNewRegionDatasource: true
                },
                cellRenderConfig: {
                    contentFormats: {
                        regionFormat: ({column, columnName}) => {
                            const field = column[columnName];
                            return getSpecialColumnsRender(field);
                        },
                        singleFormat: ({column, columnName}) => {
                            const field = column[columnName];
                            return Array.isArray(field) ? field[0] : field;
                        },
                        dashFormat: ({column, columnName}) => {
                            dashFormat({column, columnName, illegalLetter: '-1'});
                        }
                    }
                },
                // 图表区配置，数组项对应一个tab
                reportArea: {
                    tabs: [
                        {
                            tabLabel: '',
                            // 一期的表格配置,和tableTabList的value对应
                            tableAreaConfig: {
                                // 页面初始化时的表格reportType
                                tableReportType: reportType,
                                withNewSortableSelector: true
                            }
                        }
                    ]
                }
            }
        };
        const sdk = new ReportSdkEntry(config);
        sdk.init('cube-sdk-cpl-account-report');
        sdk.on('sdk.event.globalDate.change', handleGlobalDateChange);
        sdk.on('sdk.event.date.change', handleDateChange);
        sdk.on('sdk.event.page.filter.changed', handleTimeStatusChange);
        sdk.on('sdk.event.smartQuery.search', handleSmartQuery);
        setSdkEntry(sdk);

        return () => {
            sdkEntry?.destroy();
        };
    }, []);


    return (
        <div id="cube-sdk-cpl-account-report" className={reportCls}>

        </div>
    );
}

export default CplAccountReport;
