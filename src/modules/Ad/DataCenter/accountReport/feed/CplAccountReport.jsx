import {useEffect} from 'react';
import {useCubeSDKControl} from 'feedDataCenter/hooks/cubeSdk';
import CreateReport from 'feedDataCenter/CreateReport';
import BaiduMarketTip from 'commonLibs/BaiduMarketTip';
import {
    isFeedWithNewCategory, isFeedNewSortableSelector,
} from 'feedDataCenter/flags';
import {getSpecialColumnsRender} from '../../fcCommon/format';

export const cplSourceMap = {
    0: '默认',
    1: '百度自有流量',
    2: '百青藤',
};

// 根据后端指定的非法字符显示'-'
export const dashFormat = ({column, columnName, illegalLetter}) => {
    const field = column[columnName];
    if (field && Array.isArray(field)) {
        field.forEach((item, index) => {
            if (String(item) === illegalLetter) {
                field[index] = '-';
            }
        });
    }
    else {
        return String(field) === illegalLetter ? '-' : field;
    }
};

export const DEFAULT_DISPLAY = '-';

export default ({
    setDefaultDate, setCompareDate, setChecked,
    handleGlobalDateChange,
    handleTimeStatusChange,
    handleDateChange,
    handleSmartQuery,
}) => {
    const {
        Report,
        globalDefaultDate,
        datePickerMinValue,
        compareDate,
        checked,
        timeUnit,
        startDate,
        endDate,
        onReportEvent,
    } = useCubeSDKControl(CreateReport);

    useEffect(() => {
        if (globalDefaultDate) {
            const {defaultStartDate, defaultEndDate} = globalDefaultDate;
            if (defaultStartDate && defaultEndDate) {
                setDefaultDate([
                    defaultStartDate.split('/').join('-'),
                    defaultEndDate.split('/').join('-'),
                ]);
            }
        }
    }, [globalDefaultDate]);

    useEffect(() => {
        if (setCompareDate && compareDate && setChecked) {
            const [compareStartDate, compareEndDate] = compareDate;
            if (compareStartDate && compareEndDate) {
                setCompareDate([
                    compareStartDate.split('/').join('-'),
                    compareEndDate.split('/').join('-'),
                ]);
            }
            setChecked(checked);
        }
    }, [compareDate, checked, setCompareDate, setChecked]);

    // useCubeSDKControl虽然监听了这四个，但是是针对修改的sdk中的数据，在这里用的useCubeSdkCommonConfig中出来的数据
    useEffect(() => {
        onReportEvent('sdk.event.globalDate.change', handleGlobalDateChange);
    }, [onReportEvent, handleGlobalDateChange]);

    useEffect(() => {
        onReportEvent('sdk.event.page.filter.changed', handleTimeStatusChange);
    }, [onReportEvent, handleTimeStatusChange]);

    useEffect(() => {
        onReportEvent('sdk.event.date.change', handleDateChange);
    }, [onReportEvent, handleDateChange]);

    useEffect(() => {
        onReportEvent('sdk.event.smartQuery.search', handleSmartQuery);
    }, [onReportEvent, handleSmartQuery]);

    const initProps = {
        pageTitle: '',
        withNewCategory: isFeedWithNewCategory(),
        filterAreaConfig: {
            filterList: ['timeUnit'],
            filterData: {
                timeUnit: {
                    defaultValue: timeUnit || 'HOUR',
                    notSupportCompareUnits: ['WEEK', 'MONTH'],
                },
            },
            datePickerMinValue,
            titleTip: <BaiduMarketTip />,
            // 是否展示全局时间的switch开关
            isShowGlobalDateSwitch: true,
            hiddenGlobalDateSwitchBtn: true,
            isGlobalDateChecked: true,
            datePickerDefaultValue: globalDefaultDate || startDate && endDate ? {
                defaultStartDate: startDate?.split('-').join('/'),
                defaultEndDate: endDate?.split('-').join('/'),
            } : undefined,
            isMccSearchAble: true,
        },
        // 是否需要比较
        isNeedCompare: true,
        // 最大邮箱数
        maxEmails: 5,
        // 表头筛选配置
        filter: {
            stringFilterMaxLine: 20,
            stringFilterMaxLength: 100,
        },
        cellRenderConfig: {
            renders: {
                CplSource: props => {
                    const cplSource = props.column.cplSource;
                    if (props.column.isSummaryRow) {
                        return DEFAULT_DISPLAY;
                    }
                    return cplSourceMap[cplSource];
                },
            },
            contentFormats: {
                regionFormat: ({column, columnName}) => {
                    const field = column[columnName];
                    return getSpecialColumnsRender(field);
                },
                singleFormat: ({column, columnName}) => {
                    const field = column[columnName];
                    return Array.isArray(field) ? field[0] : field;
                },
                dashFormat: ({column, columnName}) => {
                    dashFormat({column, columnName, illegalLetter: '-1'});
                },
            },
        },
        // 图表区配置，数组项对应一个tab
        reportArea: {
            tabs: [{
                // 一期的表格配置,和tableTabList的value对应
                tableAreaConfig: {
                    // 页面初始化时的表格reportType
                    tableReportType: 2172680,
                    isOComment: true,
                    oCommentAppId: '2',
                    withNewSortableSelector: isFeedNewSortableSelector(),
                    infoList: [
                        <div key="0">
                            从2017.6.30起披露移动营销页的转化数据，包括浏览量、电话点击量、咨询点击量、预约点击量。
                        </div>,
                        <div key="1">
                            从2018.3.13起披露代码监测的分时转化数据，包括电话点击量、咨询点击量、表单按钮点击量、表单提交成功量。
                        </div>,
                    ],
                    showPartFields: true,
                },
            }],
        },
    };

    return (
        <Report
            initProps={initProps}
        />
    );
};
