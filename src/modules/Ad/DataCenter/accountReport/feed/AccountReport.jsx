/* eslint-disable */

import {useEffect} from 'react';
import {useCubeSDKControl} from 'feedDataCenter/hooks/cubeSdk';
import CreateReport from 'feedDataCenter/CreateReport';
import BaiduMarketTip from 'commonLibs/BaiduMarketTip';
import {
    isFeedWithNewCategory, isFeedNewSortableSelector,
} from 'feedDataCenter/flags';

export default ({
    setDefaultDate, setCompareDate, setChecked,
    handleGlobalDateChange,
    handleTimeStatusChange,
    handleDateChange,
    handleSmartQuery,
}) => {
    const {
        Report,
        globalDefaultDate,
        feedSubjectEnum,
        indicatorList,
        datePickerMinValue,
        compareDate,
        checked,
        source,
        timeUnit,
        customColumnfilters,
        sortRule,
        cardType,
        rowCount,
        topCount,
        startDate,
        endDate,
        onReportEvent,
    } = useCubeSDKControl(CreateReport);

    useEffect(() => {
        if (globalDefaultDate) {
            const {defaultStartDate, defaultEndDate} = globalDefaultDate;
            if (defaultStartDate && defaultEndDate) {
                setDefaultDate && setDefaultDate([
                    defaultStartDate.split('/').join('-'),
                    defaultEndDate.split('/').join('-')
                ]);
            }
        }
    }, [globalDefaultDate]);

    useEffect(() => {
        if (setCompareDate && compareDate && setChecked) {
            const [compareStartDate, compareEndDate] = compareDate;
            if (compareStartDate && compareEndDate) {
                setCompareDate([
                    compareStartDate.split('/').join('-'),
                    compareEndDate.split('/').join('-')
                ]);
            }
            setChecked(checked);
        }
    }, [compareDate, checked, setCompareDate, setChecked]);

    useEffect(() => {
        onReportEvent('sdk.event.globalDate.change', handleGlobalDateChange);
    }, [onReportEvent, handleGlobalDateChange]);

    useEffect(() => {
        onReportEvent('sdk.event.page.filter.changed', handleTimeStatusChange);
    }, [onReportEvent, handleTimeStatusChange]);

    useEffect(() => {
        onReportEvent('sdk.event.date.change', handleDateChange);
    }, [onReportEvent, handleDateChange]);

    useEffect(() => {
        onReportEvent('sdk.event.smartQuery.search', handleSmartQuery);
    }, [onReportEvent, handleSmartQuery]);

    const initProps = {
        pageTitle: '',
        withNewCategory: isFeedWithNewCategory(),
        filterAreaConfig: {
            filterList: ['timeUnit', 'feedSubjectEnum'],
            filterData: {
                timeUnit: {
                    defaultValue: timeUnit || 'HOUR',
                    notSupportCompareUnits: ['WEEK', 'MONTH']
                },
                feedSubjectEnum
            },
            datePickerMinValue,
            titleTip: <BaiduMarketTip />,
            // 是否展示全局时间的switch开关
            isShowGlobalDateSwitch: true,
            hiddenGlobalDateSwitchBtn: true,
            isGlobalDateChecked: true,
            datePickerDefaultValue: globalDefaultDate || startDate && endDate ? {
                defaultStartDate: startDate?.split('-').join('/'),
                defaultEndDate: endDate?.split('-').join('/')
            } : undefined,
            isMccSearchAble: true
        },
        // 是否需要比较
        isNeedCompare: true,
        // 最大邮箱数
        maxEmails: 5,
        // 表头筛选配置
        filter: {
            stringFilterMaxLine: 20,
            stringFilterMaxLength: 100
        },
        // 图表区配置，数组项对应一个tab
        reportArea: {
            tabs: [{
                chartAreaList: [{
                    chartType: 'line',
                    chartReportType: 2149145,
                    // 是否响应时间窗
                    isResponseTimeWindow: true,
                    // 是否响应时间对比
                    isResponseCompare: true,
                    // 是否显示下拉指标相关
                    isShowIndicator: true,
                    indicatorUiType: 'dropdown',
                    isCompareIndicator: true,
                    indicatorList,
                    // x轴的字段
                    xAxisField: 'date',
                    // 默认数据的字段，如果有选择指标，也会当作默认选择的指标项
                    defaultIndicator: 'cost',
                    operatorWidth: '256px',
                    isAddZeroRows: true
                }],
                // 一期的表格配置,和tableTabList的value对应
                tableAreaConfig: {
                    // 页面初始化时的表格reportType
                    tableReportType: 2149145,
                    isOComment: true,
                    oCommentAppId: '2',
                    withNewSortableSelector: isFeedNewSortableSelector(),
                    infoList: [
                        <div key="0">
                            从2017.6.30起披露移动营销页的转化数据，包括浏览量、电话点击量、咨询点击量、预约点击量。
                        </div>,
                        <div key="1">
                            从2018.3.13起披露代码监测的分时转化数据，包括电话点击量、咨询点击量、表单按钮点击量、表单提交成功量。
                        </div>
                    ],
                    showPartFields: true
                }
            }]
        }
    };

    return (
            <Report
                initProps={initProps}
            />
    );
};
