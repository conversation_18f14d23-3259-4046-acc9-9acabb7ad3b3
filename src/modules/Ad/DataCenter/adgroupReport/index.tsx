import queryString from 'query-string';
import {useLocation} from 'react-router-dom';
import {getUserId} from '@/utils';
import {formatString} from '@/utils/string';
import {appendQuery} from '@/utils/route';
import {getUrlWithQinggeQuery} from '@/utils/link';
import {PLATFORM_ENUM, PLATFORM_KEY_MAP} from '@/dicts';
import {useGlobalProductContext} from '@/hooks/productLine';
import {SingletonIframeInDataCenter} from '../fcCommon/SingletonIframeInDataCenter';

const stage = process.env.STAGE || 'ONLINE';
const customReportConfig = [
    {
        key: PLATFORM_KEY_MAP[PLATFORM_ENUM.FC],
        name: '凤巢',
        url: ({
            ONLINE: 'https://fengchao.baidu.com/fc/datacenter/qingge/adgroup/user/${userId}',
            PREONLINE: 'https://fengchao.baidu-int.com/fc/datacenter/qingge/adgroup/user/${userId}',
            TEST: 'http://fctest.baidu.com:${port}/fc/datacenter/qingge/adgroup/user/${userId}',
        })[stage],
    },
];

function AdgroupReport() {
    const {search} = useLocation();
    const {platform: queryProduct, ...restQueryParams} = queryString.parse(search);
    const {product} = useGlobalProductContext();
    const tabKey = PLATFORM_KEY_MAP[product];


    const url = formatString(
        customReportConfig.find(i => i.key === tabKey)?.url || '',
        {userId: getUserId(), port: location.port}
    );
    const urlWirhQuery = appendQuery(
        getUrlWithQinggeQuery(url),
        {
            in: 'iframe',
            'host_app': 'qingge',
            ...restQueryParams,
        }
    );

    return (
        <div className="aix-report-container">
            <div className="report-header">
                <div className="report-title">单元报告</div>
            </div>
            <SingletonIframeInDataCenter
                urlWirhQuery={urlWirhQuery}
            />
        </div>
    );
}

export default AdgroupReport;
