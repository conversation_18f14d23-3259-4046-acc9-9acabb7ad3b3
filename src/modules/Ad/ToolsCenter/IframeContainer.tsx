import {useEffect, useMemo} from 'react';
import queryString from 'query-string';
import {useGlobalProductContext} from '@/hooks/productLine';
import {getOperatorId, getUserId} from '@/utils';
import {appendHashQuery, appendQuery} from '@/utils/route';
import {formatString} from '@/utils/string';
import {getEnvPort} from '@/utils';
import {PLATFORM_ENUM} from '@/dicts';
import {sendMonitor} from '@/utils/logger';
import SingletonIframe from '@/components/common/iframe/SingletonIframe';
import {defaultIframeStyle} from '@/utils/iframeManager';
import {PageType} from '@/dicts/pageType';
import {IFRAME_DISABLE_LIST} from '@/components/common/iframe/config';
import './iframeContainer.global.less';

interface Platform {
    key: PLATFORM_ENUM;
    url: string;
}
interface ToolsCenterIFrameProps {
    platforms: Platform[];
    name: string;
    category?: string;
    hasSubTitle?: boolean;
    pageType: PageType;
}

const validHosts = [
    'qingge.baidu.com',
    'aix-test.baidu.com',
    'fengchao.baidu.com',
    'fctest.baidu.com',
    'feedads.baidu.com',
    'fcfeed.baidu.com',
    'dev.qingge.baidu.com',
    'dev.aix-test.baidu.com',
    'dev.fengchao.baidu.com',
    'dev.fctest.baidu.com',
    'dev.feedads.baidu.com',
    'dev.fcfeed.baidu.com',
];

function ToolsCenterIFrame({platforms, name, category, hasSubTitle = false, pageType}: ToolsCenterIFrameProps) {
    const {iframeUrl = '', ...restQuery} = queryString.parse(location.search);
    const {product: platform} = useGlobalProductContext();

    const urlFromQuery = decodeURIComponent(iframeUrl as string);

    const isHostValid = !!urlFromQuery && (() => {
        try {
            const url = new URL(urlFromQuery);
            return validHosts.includes(url.hostname);
        } catch (error) {
            console.error('无效的 URL:', urlFromQuery);
            return false;
        }
    })();

    const url = useMemo(
        () => {

            const urlFromConfig = (platforms.find(item => item.key === platform) || platforms[0])?.url;
            let url = isHostValid ? urlFromQuery : urlFromConfig;

            if (url) {
                const port = getEnvPort();
                url = formatString(url, {userId: getUserId(), port, optId: getOperatorId()});
                // 最老的feed的url处理简单一点
                if (url.includes('/nirvana/main.html')) {
                    return decodeURIComponent(appendHashQuery(url, {
                        in: 'iframe',
                        'host_app': 'qingge',
                    }));
                }
                const {search, pathname, protocol, host} = new URL(url);
                return appendQuery(
                    protocol + '//' + host + pathname,
                    {
                        ...queryString.parse(search),
                        ...restQuery,
                        in: 'iframe',
                        'host_app': 'qingge',
                    }
                );
            }
        },
        [platform, platforms, isHostValid, urlFromQuery, restQuery]
    );

    const iframePath = useMemo(() => {
        const {pathname, search} = new URL(url);
        return pathname + search;
    }, [url]);

    useEffect(() => {
        sendMonitor('click', {level: category, field: `show_${name}`, 'extra_params': platform});
    }, [category, name, platform]);

    return (
        <div className="tools-center-iframe-container">
            {
                !hasSubTitle && (
                    <div className="header">
                        <div className="name">{name}</div>
                    </div>
                )
            }
            {
                platform === PLATFORM_ENUM.FC && !IFRAME_DISABLE_LIST.includes(pageType) ? (
                    <SingletonIframe
                        iframePath={iframePath}
                        iframeClassName="tools-center-iframe"
                        iframeStyle={hasSubTitle
                            ? defaultIframeStyle + 'padding: 4px 12px;'
                            : defaultIframeStyle + 'padding: 24px;'}
                    />
                ) : (
                    <iframe
                        className="tools-center-iframe"
                        src={url}
                        frameBorder="0"
                        width="100%"
                        height="100%"
                        // @ts-ignore next-line
                        webkitallowfullscreen="true"
                        allowFullScreen
                        mozAllowFullScreen
                        style={hasSubTitle ? {padding: '4px 12px'} : {padding: '24px'}}
                    />
                )
            }
        </div>
    );
}

export default ToolsCenterIFrame;
