/* eslint-disable */
import {ReactNode} from 'react';
import {groupBy} from 'lodash-es';
import {Badge} from '@baidu/one-ui';
import {PageType} from '@/dicts/pageType';
import {PLATFORM_ENUM} from '@/dicts';
import {formatString} from '@/utils/string';
import {getOperatorId, getUserId, getToken} from '@/utils';
import {
    isFcIdmpUser2,
    isFeedIdmpUser3,
    isCompassAccountMoveGroup,
    isCompassWanhuatongIndependent,
    isClueRfqUser,
    isFcAssetUser,
    isFcTransAndAssetFuheUser,
    isAixWanhuatongUser,
    isAiMaxSmartControlUser,
    isAIMaxBasedScene,
    isAiBuildUser
} from '@/utils/getFlag';
import {getBjhUrl, getFcUrl, getFeedUrl} from '@/utils/format/url';
import {isFeedSmartControl, isFeedCommentInner, isFeedBjhVideoPluginUser} from '@/utils/getFeedFlag';
import queryString from 'query-string';
import globalData from '@/utils/globalData';
import {
    IconAppsSettingsSolid,
    IconBookSolid,
    IconComponentSolid,
    IconCubeSolid,
    IconReportTrendSwitchSolid,
    IconPvShieldSolid,
    IconReportSettingsSolid,
    IconTrendReportSolid,
    IconFolderStarSolid,
    IconFolderTimeSolid,
    IconImageDesktopSolid
} from 'dls-icons-react';
import {CustomLink} from '@/components/Header/CustomLink';

export enum ToolsType {
    account = 'account',
    optimize = 'optimize',
    share = 'share',
    creativeAssets = 'creativeAssets',
    creative = 'creative',
    trans = 'trans',
    orientation = 'orientation',
    marketing = 'marketing',
    other = 'other',
    landpage = 'landpage',
    platform = 'platform',
}

export const ToolsTypeNames: Record<string, string> = {
    [ToolsType.account]: '账户工具',
    [ToolsType.optimize]: '优化工具',
    [ToolsType.share]: '共享设置',
    [ToolsType.creative]: '创意工具',
    [ToolsType.trans]: '转化设置',
    [ToolsType.orientation]: '定向工具',
    [ToolsType.marketing]: '推广目标',
    [ToolsType.other]: '其他工具',
    [ToolsType.landpage]: '落地页工具',
    [ToolsType.creativeAssets]: '创意资产',
    [ToolsType.platform]: '平台工具',
};

export const ToolsTypeIcon: Record<string, ReactNode> = {
    [ToolsType.account]: <IconFolderTimeSolid className='header-group-icon' />,
    [ToolsType.optimize]: <IconTrendReportSolid className='header-group-icon' />,
    [ToolsType.share]: <IconReportSettingsSolid className='header-group-icon' />,
    [ToolsType.creative]: <IconImageDesktopSolid className='header-group-icon' />,
    [ToolsType.trans]: <IconReportTrendSwitchSolid className='header-group-icon' />,
    [ToolsType.orientation]: <IconPvShieldSolid className='header-group-icon' />,
    [ToolsType.marketing]: <IconAppsSettingsSolid className='header-group-icon' />,
    [ToolsType.other]: <IconComponentSolid className='header-group-icon' />,
    [ToolsType.landpage]: <IconBookSolid className='header-group-icon' />,
    [ToolsType.creativeAssets]: <IconFolderStarSolid className='header-group-icon' />,
    [ToolsType.platform]: <IconCubeSolid className='header-group-icon' />
};

export enum ImportByEnum {
    iframe = 'iframe',
    externalLink = 'externalLink',
    prompt = 'prompt',
}

interface ToolsConfigItem {
    key: PageType | string;
    category: string;
    name: string;
    description: string;
    importBy: ImportByEnum;
    type: ToolsType;
    platforms: Array<{key?: PLATFORM_ENUM; url?: string}>;
    tags?: ReactNode[];
    visible?: () => boolean;
    isSelf?: boolean;
    prompts?: [content: any, extra?: any];
    feedAgentFlag?: boolean; // 代理商帐户是否可访问
}

export const AI_SMART_GROUP_KEY = 'idmp';

export const getTools = (): ToolsConfigItem[] => {
    const isAgentUser = globalData.get('optAccountRoles').includes('AGENT_USER');
    const userId = getUserId();
    return [
        // ! 资产中心 - 共享设置
        {
            key: PageType.Tools_NegativePackets,
            category: '资产中心',
            name: '否定关键词包',
            description: '将多方案通用的否词保存为否词包，快速应用至多个方案',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/assetscenter/share/negative/user/${userId}')
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.share,
        },
        {
            key: PageType.Tools_TargetPackage,
            category: '资产中心',
            name: '定向包管理',
            description: '将常用地域、人群等设置保存为定向包，快速应用至多个方案',
            platforms: [
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/toolscenter/target/targetPackage?userId=${userId}')
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.share
        },
        {
            key: PageType.Tools_FcCrowds,
            category: '资产中心',
            name: '人群包',
            description: '将常用人群设置保存为人群包，快速应用至多个方案',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.share
        },
        ...(
            (isFeedIdmpUser3() || isFcIdmpUser2())
                ? [
                    {
                        key: AI_SMART_GROUP_KEY,
                        category: '资产中心',
                        name: 'AI智选人群',
                        description: '管理一方人群和行业专属人群包',
                        platforms: [
                            ...(
                                isFcIdmpUser2()
                                    ? [
                                        {
                                            key: PLATFORM_ENUM.FC,
                                            url: getFcUrl('/fc/assetscenter/share/idmp/crowdSquare/user/${userId}')
                                        }
                                    ]
                                    : []
                            ),
                            ...(
                                isFcIdmpUser2()
                                    ? [
                                        {
                                            key: PLATFORM_ENUM.FEED,
                                            url: getFeedUrl('/fc/assetscenter/share/idmp/crowdSquare/user/${userId}')
                                        }
                                    ]
                                    : []
                            ),
                        ],
                        importBy: ImportByEnum.iframe,
                        type: ToolsType.share,
                        tags: [<Badge key="new" textContent="New" style={{position: 'relative', top: -2}} />]
                    }
                ]
                : []
        ),
        ...(
            ((!isFeedIdmpUser3() || !isFcIdmpUser2()) && !isAgentUser)
                ? [
                    {
                        key: 'dmp',
                        category: '资产中心',
                        name: '自定义人群',
                        description: '管理一方人群和行业专属人群包',
                        platforms: [
                            ...(
                                !isFcIdmpUser2()
                                    ? [
                                        {
                                            key: PLATFORM_ENUM.FC,
                                            url: 'https://cas.baidu.com/?action=check&appid=590&rb=1&u=https%3a%2f%2'
                                                + 'fcdp.baidu.com%2fhairuo%2fmain.do%3flp%3dgroupList%26source%3d3%26tUserid%3d${userId}'
                                                + '%26rurl%3daHR0cHM6Ly9jZHAuYmFpZHUuY29tL3N0YXRpYy8jL21haW4=',
                                        }
                                    ]
                                    : []
                            ),
                            ...(
                                isFcIdmpUser2()
                                    ? [
                                        {
                                            key: PLATFORM_ENUM.FEED,
                                            url: 'https://cas.baidu.com/?action=check&appid=590&rb=1&u=https%3a%2f%2fcdp.baidu.com%2fhairuo%2fmain.do%3flp%3dgroupList%26source%3d422%26tUserid%3d${userId}%26rurl%3daHR0cHM6Ly9jZHAuYmFpZHUuY29tL3N0YXRpYy8jL21haW4=',
                                        }
                                    ]
                                    : []
                            ),
                        ],
                        importBy: ImportByEnum.externalLink,
                        type: ToolsType.share,
                    }
                ]
                : []
        ),
        {
            key: PageType.Tools_ScheduleTemplates,
            category: '资产中心',
            name: '时段模板',
            description: '统一新增并管理您的时段模版',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/assetscenter/share/schedule/user/${userId}'),
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.share
        },
        {
            key: PageType.Tools_PriceStrategy,
            category: '资产中心',
            name: '点击出价策略',
            description: '统一新增并管理您的优化排名出价策略',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/assetscenter/share/strategy/user/${userId}/type/0/level/3')
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.share
        },
        {
            key: PageType.Tools_PluginManage,
            category: '资产中心',
            name: '组件管理',
            description: '统一查看并管理您的组件',
            platforms: [
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/assetscenter/share/pluginManage?userId=${userId}'),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.share,
            feedAgentFlag: true,
        },
        // {
        //     key: PageType.Tools_SharedBudget,
        //     category: '资产中心',
        //     name: '共享预算',
        //     platforms: [
        //         {
        //             key: PLATFORM_ENUM.FC,
        //             url: 'https://fengchao.baidu.com/fc/assetscenter/share/sharedBudget/user/${userId}',
        //         },
        //     ],
        //     importBy: ImportByEnum.iframe,
        //     type: ToolsType.share
        // },
        // ! 资产中心 - 创意资产
        ...(isAgentUser ? [] : [
            {
                key: PageType.Tools_ImageLibrary,
                category: '资产中心',
                name: '图片库',
                description: '统一查看并管理您的图片',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: getFcUrl('/fc/assetscenter/creative/image/user/${userId}'),
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: getFeedUrl('/fd/assetscenter/creative/imageStore?userId=${userId}'),
                    }
                ],
                importBy: ImportByEnum.iframe,
                type: ToolsType.creativeAssets,
                feedAgentFlag: true,
            },
            {
                key: PageType.Tools_VideoLibrary,
                category: '资产中心',
                name: '视频库',
                description: '统一查看并管理您的视频',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: getFcUrl('/fc/assetscenter/creative/video/user/${userId}'),
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: getFeedUrl('/fd/assetscenter/creative/videoStore?userId=${userId}'),
                    }
                ],
                importBy: ImportByEnum.iframe,
                type: ToolsType.creativeAssets,
                feedAgentFlag: true,
            },
        ]),
        {
            key: PageType.Tools_Brands,
            category: '资产中心',
            name: '品牌信息',
            description: '统一查看并管理您的品牌信息',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/assetscenter/creative/brand/user/${userId}'),
                },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/assetscenter/creative/brandProfile?userId=${userId}'),
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.creativeAssets,
            feedAgentFlag: true,
        },
        ...(isFeedBjhVideoPluginUser() ? [{
            key: PageType.Tools_Bjh_Plugin,
            category: '资产中心',
            name: '原生组件',
            description: '统一查看并管理您的原生组件',
            platforms: [
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getBjhUrl(`/matrix/component/list?from=qingge&in=iframe&token=${getToken()}`),
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.creativeAssets,
            feedAgentFlag: true,
        }]: []),
        // ! 资产中心 - 转化设置
        {
            key: PageType.Tools_EventAsset,
            category: '资产中心',
            name: '事件管理',
            description: '原转化追踪升级版，集中管理全链路转化事件',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/assetscenter/trans/eventAsset/user/${userId}')
                },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fc/assetscenter/trans/eventAsset/user/${userId}') // 这里虽然是feed，但是path 以 fc 开头
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.trans,
            feedAgentFlag: true,
        },
        ...(
            (!isFcAssetUser() || isFcTransAndAssetFuheUser())
                ? [{
                    key: PageType.Tools_Track,
                    category: '资产中心',
                    name: '转化追踪',
                    description: '管理转化事件，完成转化事件设置与联调',
                    platforms: [
                        {
                            key: PLATFORM_ENUM.FC,
                            url: getFcUrl('/fc/toolscenter/optimize/track/user/${userId}?withPageTitle=true')
                        },
                        {
                            key: PLATFORM_ENUM.FEED,
                            url: getFeedUrl('/fc/toolscenter/optimize/track/user/${userId}?withPageTitle=true')
                        }
                    ],
                    importBy: ImportByEnum.iframe,
                    type: ToolsType.trans,
                    feedAgentFlag: true,
                }]
                : []
        ),
        // ! 资产中心 - 推广目标
        {
            key: PageType.Tools_AppCenter,
            category: '资产中心',
            name: '应用中心',
            description: '统一查看管理您使用和绑定的应用',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/assetscenter/saleTarget/app/user/${userId}'),
                },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/assetscenter/marketing/app/user/${userId}'),
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.marketing,
            feedAgentFlag: true,
        },
        {
            key: 'BMC',
            category: '资产中心',
            name: '商品中心',
            description: '统一查看并管理您的商品',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: 'https://product.baidu.com/bmc/index.html?operId=${userId}#/merchant/login',
                },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: 'https://product.baidu.com/bmc/index.html?operId=${userId}#/merchant/login',
                },
            ],
            importBy: ImportByEnum.externalLink,
            type: ToolsType.marketing,
            feedAgentFlag: true,
        },
        {
            key: 'storeCenter',
            category: '资产中心',
            name: '门店中心',
            description: '电商一站式全链路闭环管理平台',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: 'https://kaidian.baidu.com/mallplat/businessCenter?ucId=${userId}',
                },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: 'https://kaidian.baidu.com/mallplat/businessCenter?ucId=${userId}',
                },
            ],
            importBy: ImportByEnum.externalLink,
            type: ToolsType.marketing,
        },
        // ! 工具中心 - 账户工具
        {
            key: PageType.Tools_History,
            category: '工具中心',
            name: '历史操作记录',
            description: '查看账户历史操作记录',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/account/history/user/${userId}')
                },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/toolscenter/account/optLog?userId=${userId}')
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.account
        },
        {
            key: PageType.Tools_TaskRecord,
            category: '工具中心',
            name: '后台任务记录',
            description: '查看批量操作执行记录',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/account/taskRecord/taskList/user/${userId}')
                },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/toolscenter/account/taskRecord/taskList/user/${userId}')
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.account,
            feedAgentFlag: true,
        },
          ...(
            isAiBuildUser()
                ? [{
                    key: PageType.AIBuild,
                    category: '工具中心',
                    name: '智能搭建',
                    description: '智能搭建和管理广告账户',
                    platforms: [
                        {
                            key: PLATFORM_ENUM.FC,
                        },
                    ],
                    importBy: ImportByEnum.prompt,
                    prompts: ['帮我搭建账户'],
                    type: ToolsType.account
                } as ToolsConfigItem,
                {
                    key: PageType.AIBuildRefresh,
                    category: '工具中心',
                    name: '智能翻新',
                    description: '智能优化和翻新广告内容',
                    platforms: [
                        {
                            key: PLATFORM_ENUM.FC,
                        },
                    ],
                    importBy: ImportByEnum.prompt,
                    prompts: ['帮我翻新账户'],
                    type: ToolsType.account
                } as ToolsConfigItem]
            : []
        ),
        {
            key: PageType.Tools_ProductBatch,
            category: '工具中心',
            name: '商品目录投放批量工具',
            description: '统一管理您的商品目录投放批量工具',
            platforms: [
                // {
                //     key: PLATFORM_ENUM.FC,
                //     url: getFcUrl('/fc/toolscenter/account/taskRecord/taskList/user/${userId}')
                // },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/nirvana/main.html?userid=${userId}#/feed/index~module=apps&activeTab=paBatchTools&embed=mtdetail')
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.account,
            feedAgentFlag: true,
        },
        // ! 工具中心 - 定向工具
        {
            key: PageType.Tools_KR,
            category: '工具中心',
            name: '关键词规划师',
            description: '输入种子词获取相关关键词',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/orientation/kr/user/${userId}')
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.orientation
        },
        ...(isAgentUser ? [] : [
            {
                key: 'flowInsight',
                category: '工具中心',
                name: '行业流量洞察',
                description: '洞察全行业流量趋势，了解用户的搜索特征',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://cas.baidu.com/?' + queryString.stringify({
                            action: 'check',
                            appid: 590,
                            rb: 1,
                            u: `https://cdp.baidu.com/hairuo/main.do?lp=flowInsight&source=3&tUserid=${userId}&rurl=aHR0cHM6Ly9jZHAuYmFpZHUuY29tL3N0YXRpYy8jL21haW4=`,
                            })
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.orientation,
            },
        ]),
        {
            key: PageType.Tools_IntentWordManage,
            category: '工具中心',
            name: '意图词推荐',
            description: '统一查看并管理您的意图词',
            platforms: [
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/toolscenter/target/intentWordManage/intentRecommend?userId=${userId}'),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.orientation,
            feedAgentFlag: true,
        },
        {
            key: PageType.Tools_MediaManage,
            category: '工具中心',
            name: '媒体包管理',
            description: '统一查看并管理您的媒体包',
            platforms: [
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/toolscenter/target/mediaManage/mediaIDManage?userId=${userId}'),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.orientation,
            feedAgentFlag: true,
        },
        ...(isAgentUser ? [] : [
            // ! 工具中心 - 创意工具
            {
                key: 'qingduo',
                category: '工具中心',
                name: '擎舵',
                description: '创意文案、图片、视频，一键生成',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://qingduo.baidu.com/user/${userId}/home',
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: 'https://qingduo.baidu.com/user/${userId}/home',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.creative,
            },
        ]),
        // {
        //     key: 'hudongTool',
        //     category: '工具中心',
        //     name: '互动工具',
        //     description: '',
        //     platforms: [
        //         {
        //             key: PLATFORM_ENUM.FEED,
        //             url: 'https://qingduo.baidu.com/user/${userId}/hudongTool?from=feed',
        //         },
        //     ],
        //     importBy: ImportByEnum.externalLink,
        //     type: ToolsType.creative,
        // },
        {
            key: PageType.Tools_StyleAnalyse,
            category: '工具中心',
            name: '广告识别',
            description: '快捷查询广告所用的创意样式/组件',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/creativetools/styleAnalyse/user/${userId}'),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.creative
        },
        {
            key: PageType.Tools_Pna,
            category: '工具中心',
            name: '高级样式',
            description: '提交多样化创意组件',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/creativetools/pna/user/${userId}'),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.creative
        },
        ...(isAgentUser ? [] : [
            // {
            //     key: 'qingduoImage',
            //     category: '工具中心',
            //     name: '擎舵一键出图',
            //     description: '自定义一键生成营销图片',
            //     platforms: [
            //         {
            //             key: PLATFORM_ENUM.FC,
            //             url: 'https://qingduo.baidu.com/user/${userId}/imageCreate/marketingV2',
            //         },
            //         {
            //             key: PLATFORM_ENUM.FEED,
            //             url: 'https://qingduo.baidu.com/user/${userId}/imageCreate/marketingV2',
            //         },
            //     ],
            //     importBy: ImportByEnum.externalLink,
            //     type: ToolsType.creative,
            // },
            // {
            //     key: 'qingduoVideo',
            //     category: '工具中心',
            //     name: '擎舵一键成片',
            //     description: '自定义一键生成营销视频成片',
            //     platforms: [
            //         {
            //             key: PLATFORM_ENUM.FC,
            //             url: 'https://qingduo.baidu.com/user/${userId}/videoCreate/mixedV2',
            //         },
            //         {
            //             key: PLATFORM_ENUM.FEED,
            //             url: 'https://qingduo.baidu.com/user/${userId}/videoCreate/mixedV2',
            //         },
            //     ],
            //     importBy: ImportByEnum.externalLink,
            //     type: ToolsType.creative,
            // },
            {
                key: 'huihe',
                category: '工具中心',
                name: '慧合平台',
                description: '优质短视频广告撮合服务平台',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://join.baidu.com/advertiser/user/${userId}/demand',
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: 'https://join.baidu.com/advertiser/user/${userId}/demand',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.creative,
                feedAgentFlag: true,
            },
        ]),
        ...(isAgentUser ? [] : [
            // ! 工具中心 - 落地页工具
            {
                key: 'jimuyu',
                category: '工具中心',
                name: '基木鱼',
                description: '统一新增并管理您的基木鱼设置',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://wutong.baidu.com/platform/user/${optId}/home?ucUserId=${userId}',
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: 'https://wutong.baidu.com/platform/user/${optId}/home?ucUserId=${userId}',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.landpage,
            },
            {
                key: 'yingxiaotong',
                category: '工具中心',
                name: '营销通',
                description: '统一查看并管理您的线索',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://yingxiaotong.baidu.com/vector/user/${userId}/index',
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: 'https://yingxiaotong.baidu.com/vector/user/${userId}/index',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.landpage,
            },
            {
                key: 'aiagent',
                category: '工具中心',
                name: '巧舱',
                description: '使用商家智能体高效收集意向线索',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://aiagent.baidu.com/mbot/user/${optId}/role?ucUserId=${userId}',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.landpage,
            },
            {
                key: 'pageclick',
                category: '工具中心',
                name: '落地页热力图',
                description: '了解点击热度、访客关注点，优化页面设计',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://tongji.baidu.com/main/overview/${userId}/custom/pageclick',
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: 'https://tongji.baidu.com/main/overview/${userId}/custom/pageclick',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.landpage,
            },
        ]),
        {
            key: PageType.Tools_ideaWordsBag,
            category: '工具中心',
            name: '创意通配符',
            description: '统一查看您的创意通配符设置',
            platforms: [
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fd/toolscenter/idea/wordsBag?userId=${userId}'),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.creative,
            feedAgentFlag: true,
        },
        // ! 工具中心 - 优化工具
        {
            key: PageType.Tools_AutoRules,
            category: '工具中心',
            name: '盯盘助手',
            description: '设定盯盘规则、自动执行，盯盘更高效',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/optimize/autoRules/user/${userId}?withPageTitle=true')
                },
                {
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fc/toolscenter/optimize/autoRules/user/${userId}?withPageTitle=true')
                }
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.optimize,
            feedAgentFlag: true,
        },
        {
            key: PageType.Tools_AutoAdviceRules,
            category: '工具中心',
            name: '盯盘助手',
            description: '设定盯盘规则、自动执行，盯盘更高效',
            platforms: [
                ...(isAiMaxSmartControlUser() || isAIMaxBasedScene()) ? [{
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/optimize/adviceRules/user/${userId}?withPageTitle=true')
                }] : [],
                ...isFeedSmartControl() ? [{
                    key: PLATFORM_ENUM.FEED,
                    url: getFeedUrl('/fc/toolscenter/optimize/adviceRules/user/${userId}?withPageTitle=true')
                }] : []
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.optimize,
            visible: () => false,
        },
        {
            key: PageType.Tools_BusinessShield,
            category: '工具中心',
            name: '商盾-恶意点击屏蔽',
            description: '屏蔽恶意点击IP',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/manage/tools/user/${userId}/businessShield')
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.optimize
        },
        {
            key: PageType.Tools_Adpreview,
            category: '工具中心',
            name: '推广实况与诊断',
            description: '预览广告展现样式，诊断无展现原因',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl(
                        '/fc/toolscenter/optimize/adpreviewAndDiagnose/user/${userId}/type/${type}?withPageTitle=true',
                        {type: queryString.parse(location.search)?.type || 'adpreview'},
                        {pageType: PageType.Tools_Adpreview}),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.optimize,
        },
        ...(
            isClueRfqUser()
                ? [{
                    key: PageType.Tools_ClueRfq,
                    category: '工具中心',
                    name: '服务直达-线索加油包',
                    description: '为您匹配符合您推广目标的线索',
                    platforms: [
                        {
                            key: PLATFORM_ENUM.FC,
                            url: getFcUrl('/fc/toolscenter/optimize/clueRfq/user/${userId}')
                        },
                    ],
                    importBy: ImportByEnum.iframe,
                    type: ToolsType.optimize
                }]
                : []
        ),
        {
            key: PageType.Tools_KeywordPreCheck,
            category: '工具中心',
            name: '关键词质量度预检',
            description: '参考关键词字面和落地页预检搭建计划',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/optimize/keywordPreCheck/user/${userId}'),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.optimize
        },
        {
            key: PageType.Tools_NewDiagnosis,
            category: '工具中心',
            name: '广告诊断',
            description: '识别消费突降、超成本问题',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl(
                        '/fc/toolscenter/optimize/newDiagnosis/user/${userId}/tab/${tab}',
                        {tab: queryString.parse(location.search)?.tab || 'consumeReduce'}),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.optimize
        },
        {
            key: PageType.Tools_AbnormalFluctuationDiagnose,
            category: '工具中心',
            name: '消费波动诊断',
            description: '快捷查看并分析消费波动方案',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/optimize/abnormalFluctuationDiagnose/user/${userId}'),
                },
            ],
            importBy: ImportByEnum.prompt,
            prompts: ['诊断消费波动问题。'],
            type: ToolsType.optimize
        },
        {
            key: PageType.Tools_InvalidClueDiagnose,
            category: '工具中心',
            name: '线索有效性诊断',
            description: '线索有效性核查工具',
            platforms: [
                {
                    key: PLATFORM_ENUM.FC,
                    url: getFcUrl('/fc/toolscenter/optimize/invalidClueDiagnose/user/${userId}', {}, {pageType: PageType.Tools_InvalidClueDiagnose}),
                },
            ],
            importBy: ImportByEnum.iframe,
            type: ToolsType.optimize
        },
        ...(isFeedCommentInner() ? [
            {
                key: PageType.Tools_ReviewManage,
                category: '工具中心',
                name: '广告评论管理',
                description: '统一查看并管理您的广告评论',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: getFeedUrl('/fd/toolscenter/optimize/review?userId=${userId}'),
                    },
                ],
                importBy: ImportByEnum.iframe,
                type: ToolsType.optimize,
                feedAgentFlag: true,
            }
        ] : []),
        // ! 工具中心 - 其他工具
        ...(
            (isCompassAccountMoveGroup() || isCompassWanhuatongIndependent() || isAixWanhuatongUser()) ? [
                {
                    key: PageType.Tools_SuperManager,
                    category: '工具中心',
                    name: '超管万花筒',
                    description: '分析我管理范围内的所有账户数据',
                    platforms: [
                        {
                            url: 'https://compass.baidu.com/cp/post-research/kaleidoscope?from=qinggeiframe',
                        },
                    ],
                    importBy: ImportByEnum.iframe,
                    type: ToolsType.other,
                },
            ]
            : []
        ),
        ...(isAgentUser ? [] : [
            // ! 工具中心 - 平台工具
            {
                key: 'API',
                category: '工具中心',
                name: '百度营销API',
                description: '百度营销商业开发者中心',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://dev2.baidu.com/',
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: 'https://dev2.baidu.com/',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.platform,
                feedAgentFlag: true,
            },
            {
                key: 'EDITOR',
                category: '工具中心',
                name: '百度营销客户端',
                description: '百度营销桌面推广助手，提升投放效率',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://editor.baidu.com/',
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: 'https://editor.baidu.com/',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.platform,
                feedAgentFlag: true,
            },
            {
                key: 'APP',
                category: '工具中心',
                name: '百度营销APP',
                description: '百度营销手机端，随时随地关注推广',
                platforms: [
                    {
                        key: PLATFORM_ENUM.FC,
                        url: 'https://mobile2.baidu.com/index.html?navfrom=fctuiguangtools',
                    },
                    {
                        key: PLATFORM_ENUM.FEED,
                        url: 'https://mobile2.baidu.com/index.html?navfrom=feedtuiguangtools',
                    },
                ],
                importBy: ImportByEnum.externalLink,
                type: ToolsType.platform,
                feedAgentFlag: true,
            },
        ]),
    ];
}

export const TOOLS: ToolsConfigItem[] = getTools();
// 获取资产、工具的子导航
export const getAssetToolsChildrenNavigations = (category: string, platform?: PLATFORM_ENUM) => {
    const feedBasicInfo = globalData.get('feedBasicInfo');
    const cards = TOOLS
        .filter(item =>
            item.category === category
            && (!platform || item.platforms.some(p => !p.key || platform === p.key))
            && (item.visible ? item.visible() : true)
            && (platform === PLATFORM_ENUM.FEED && feedBasicInfo?.acctAuth?.readdls ? item.feedAgentFlag : true)
        );

    const groupedByaToolsType = groupBy(cards, 'type');
    return Object.keys(groupedByaToolsType).map(type => {
        const navItem = {
            key: type,
            label: ToolsTypeNames[type],
            icon: ToolsTypeIcon[type],
            children: groupedByaToolsType[type].map(item => {
                const {
                    key,
                    name,
                    platforms,
                    importBy,
                    isSelf,
                    prompts,
                } = item;
                const isExternalLink = importBy === ImportByEnum.externalLink;
                const isLinkToChat = importBy === ImportByEnum.prompt && prompts && prompts.length > 0;
                const {
                    url: urlTpl = ''
                } = platforms.find(i => i.key === platform) || platforms[0] || {};
                const formatedUrl = formatString(urlTpl, {userId: getUserId(), optId: getOperatorId()});
                const navItem = {
                    key,
                    isExternalLink,
                    target: isExternalLink ? '_blank' : '_self',
                    isSelf,
                    externalLink: formatedUrl,
                    isLinkToChat,
                    prompts,
                    type: isExternalLink ? 'external' : ''
                };
                const label = (<CustomLink {...navItem} name={name} navkey={key} key={key} />);
                return { ...navItem, label };
            }),
        };
        return navItem;
    });
};
