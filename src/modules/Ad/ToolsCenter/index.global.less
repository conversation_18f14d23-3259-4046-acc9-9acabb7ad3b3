.qingge-tools-center-container {
    display: flex;
    gap: 16px;
    width: 100%;
    height: 100%;
}

.aix-tools-center {
    width: 100%;
}

.tools-center-iframe {
    padding: 24px;
    background: #fff;
    border-radius: 10px;
}

// .tools-center-iframe-container {
//     width: 100%;
//     height: 100%;
//     display: flex;
//     flex-direction: column;

//     .header {
//         display: flex;
//         align-items: center;
//         margin-bottom: 16px;
//         background: #fff;
//         padding: 16px 24px;
//         border-radius: 10px;

//         .title {
//             font-size: 14px;
//             font-weight: 400;
//             color: #848B99;
//         }

//         .name {
//             font-size: 18px;
//             font-weight: 500;
//             color: #0E0F11;
//         }

//         & > * + * {
//             margin-left: 8px;
//         }
//     }

//     iframe {
//         padding: 24px;
//         background: #fff;
//         border-radius: 10px;
//     }

//     .tools-center-tabs {
//         padding: 0 20px;

//         .one-ai-tabs-bar {
//             padding: 0;
//         }
//     }
// }

.tools-center-fcCrowds {
    width: 100%;
    flex: 1;
    background: #fff;
    border-radius: 10px;
}

.tools-center-overview {
    height: 100%;

    &-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .title {
            color: #0e0f11;
            font-weight: 600;
            font-size: 18px;
            margin-right: 12px;
        }
    }

    &-anchor {
        .one-ai-anchor-medium {
            display: flex;
            border-left: none;
            border-bottom: 1px solid #e2e6f0;
            gap: 8px;
            background: #fff;
            padding-top: 12px;
        }

        .one-ai-anchor-link-title {
            position: relative;
        }

        .one-ai-anchor-link-active.one-ai-anchor-link-without-children .one-ai-anchor-link-title-active::before {
            left: 8px;
            width: 100%;
            height: 1px;
            bottom: -12px;
        }
    }

    &-groups {
        max-height: calc(100% - 52px);
        overflow-y: auto;
        padding-bottom: 20px;

        .tools-center-overview-group-title {
            font-size: 16px;
            font-weight: 500;
            color: #0e0f11;
            margin: 20px 0 16px;
        }
    }

    .tools-center-overview-group-items {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 16px;
    }

    .tools-center-overview-card {
        background: rgba(109, 159, 247, .071);
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        flex-direction: column;
        gap: 8px;
        height: 80px;
        padding: 10px 16px;

        .title {
            display: flex;
            gap: 4px;
            align-items: center;

            .name {
                font-weight: 500;
                font-size: 14px;
                color: #0e0f11;
            }

            .icon {
                color: #848b99;
            }
        }

        .description {
            font-size: 12px;
            font-weight: 400;
            color: #545b66;
            line-height: 16px;
        }

        &:hover {
            .name {
                color: #3a5bfd;
            }
        }

        &.empty-text {
            justify-content: center;
            align-items: center;
            color: #A8B0BF;

            & > div {
                width: 136px;
                text-align: center;
            }
        }
    }

    .tools-center-overview-line {
        height: 1px;
        background: #6692DE26;
        margin: 20px 0;
    }
}
