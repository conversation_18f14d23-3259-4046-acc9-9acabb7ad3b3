import {Suspense, useMemo} from 'react';
import {flatten, partial} from 'lodash-es';
import {VerticalNav} from '@baidu/one-ui-pro';
import {Boundary} from 'react-suspense-boundary';
import {ConfigProvider} from '@baidu/one-ui';
import {Switch, Route, Redirect} from 'react-router-dom';
import {useLocalStorage} from 'huse';
import {useGlobalProductContext} from '@/hooks/productLine';
import {AdModuleType, PageType, PageName} from '@/dicts/pageType';
import AdRoutes, {RoutesType, useAdRoute, getRoutesByPlatform} from '@/modules/Ad/routes';
import {renderError, recoverMethods} from '../hooks/routes';
import {TOOLS, ImportByEnum, getAssetToolsChildrenNavigations} from './config';
import './index.global.less';

export const ToolsCenter = (props: {type: 'ToolsCenter' | 'AssetsCenter'}) => {
    const {type = 'ToolsCenter'} = props;
    const {product} = useGlobalProductContext();
    const routes = useMemo(() => {
        return getRoutesByPlatform(AdRoutes.getRoutesByPrefixs?.([type]), product);
    }, [product, type]);


    return (
        <div className="aix-tools-center">
            <Switch>
                {
                    routes.map(route => {
                        if (route.type === 'redirect') {
                            return <Redirect key={route.name} to={route.fullPath!} />;
                        }
                        const Component = route.component;
                        const key = route.name;
                        const {
                            platforms, importBy, name, category,
                        } = TOOLS.find(option => option.key === key) || {};
                        const props: Record<string, any> = {};
                        if (importBy === ImportByEnum.iframe) {
                            props.platforms = platforms;
                            props.name = name;
                            props.category = category;
                            props.hasSubTitle = route.hasSubTitle;
                        }
                        props.pageType = key;
                        return (
                            <Route key={key} path={route.fullPath}>
                                <Suspense fallback={null}>
                                    <Component {...route.props} {...props} />
                                </Suspense>
                            </Route>
                        );
                    })
                }
            </Switch>
        </div>
    );
};

export default function ToolsCenterContainer() {
    return (
        <ConfigProvider theme="light-ai">
            <Boundary renderError={partial(renderError, {methods: [recoverMethods.refresh]})}>
                <div className="qingge-tools-center-container">
                    <ToolsCenterNav />
                    <ToolsCenter />
                </div>
            </Boundary>
        </ConfigProvider>
    );
}

export function ToolsCenterNav() {
    const {linkTo, adPageType, adModule, linkToChat} = useAdRoute();
    const {product} = useGlobalProductContext();
    const [collapsed, onCollapseChange] = useLocalStorage('ToolsCenterNav_collapsed', false);

    const navigations = useMemo(
        () => {
            if (adModule === AdModuleType.AssetsCenter) {
                return getAssetToolsChildrenNavigations(PageName[PageType.AssetsOverview], product);
            }
            if (adModule === AdModuleType.ToolsCenter) {
                return getAssetToolsChildrenNavigations(PageName[PageType.ToolsOverview], product);
            }
            return [];
        },
        [product, adModule]
    );
    const navProps = {
        className: 'aix-adModule-vertical-nav',
        type: 'ghost',
        options: navigations,
        menuProps: {
            mode: 'inline',
            selectedKeys: [adModule, adPageType],
            defaultOpenKeys: [navigations.find(item => item.children?.some(k => k.key === adPageType))?.key],
        },
        collapsable: true,
        collapsed,
        onCollapseChange,
        theme: 'light-ai',
    };
    return (
        <VerticalNav {...navProps} />
    );
}
