import {useEffect, useState} from 'react';
import {Tabs} from '@baidu/one-ui';
import {useGlobalProductContext} from '@/hooks/productLine';
import {getFeedUrl} from '@/utils/format/url';
import {PLATFORM_ENUM} from '@/dicts';
import {sendMonitor} from '@/utils/logger';
import './iframeContainer.global.less';

interface Platform {
    key: PLATFORM_ENUM;
    url: string;
}
interface MediaManageContainerProps {
    platforms: Platform[];
    name: string;
    category?: string;
    hasSubTitle?: boolean;
}

function MediaManageContainer({name, category, hasSubTitle = false}: MediaManageContainerProps) {
    const {product: platform} = useGlobalProductContext();
    const [currentTab, setCurrentTab] = useState<string>('mediaIDManage');

    const mediaTabs = [
        {
            key: 'mediaIDManage',
            title: '媒体ID筛选',
            // eslint-disable-next-line max-len
            url: getFeedUrl('/fd/toolscenter/target/mediaManage/mediaIDManage?from=qingge&userId=${userId}&globalProduct=2&in=iframe&host_app=qingge'),
        },
        {
            key: 'mediaPackageManage',
            title: '媒体包管理',
            // eslint-disable-next-line max-len
            url: getFeedUrl('/fd/toolscenter/target/mediaManage/mediaPackageManage?from=qingge&userId=${userId}&globalProduct=2&in=iframe&host_app=qingge'),
        },
    ];

    useEffect(() => {
        sendMonitor('click', {level: category, field: `show_${name}`, 'extra_params': platform});
    }, [category, name, platform]);

    return (
        <div className="tools-center-iframe-container">
            {
                !hasSubTitle && (
                    <div className="header">
                        <div className="name">{name}</div>
                    </div>
                )
            }
            <div className="tools-center-iframe-container-wrapper">
                <Tabs
                    className="tools-center-iframe-container-tabs"
                    activeKey={currentTab}
                    onChange={setCurrentTab}
                    style={{'--dls-tab-menu-padding': '0px'}}
                >
                    {
                        mediaTabs.map(({title, key}) => {
                            return (
                                <Tabs.TabPane tab={title} key={key} />
                            );
                        })
                    }
                </Tabs>
                <iframe
                    className="tools-center-iframe"
                    src={mediaTabs.find(tab => tab.key === currentTab)?.url}
                    frameBorder="0"
                    width="100%"
                    height="100%"
                    // @ts-ignore next-line
                    webkitallowfullscreen="true"
                    allowFullScreen
                    mozAllowFullScreen
                />
            </div>
        </div>
    );
}

export default MediaManageContainer;
