import {lazy} from 'react';

export const editorConfig = {
    budget: {
        title: '修改预算',
        width: 500,
        batch: lazy(() => import('./budget/BatchBudget')),
    },
    campaignOcpcBid: {
        title: '修改出价',
        width: 500,
        batch: lazy(() => import('./campaignOcpcBid/BatchCampaignOcpcBid')),
    },
    productClueInfos: {
        title: '修改线索特征',
        width: 1000,
        inline: lazy(() => import('./productClueInfos/InlineClueEditor')),
    },
    productWords: {
        title: '修改业务核心词',
        width: 1000,
        inline: lazy(() => import('./productWords/InlineProductWordsEditor')),
    },
};
