import {formatColumnConfiguration} from '@/api/configuration';
import {CampaignItemOfCPL} from '@/interface/campaign';
import {ReportConfigResponse} from '@/interface/report';
import {isFcRfqWordUser} from '@/utils/getFlag';
import operations from './columns/operations';
import campaignOcpcBid from './columns/ocpcBid';
import budget from './columns/budget';
import status from './columns/status';
import featureSource from './columns/featureSource';
import productWords from './columns/productWords';

export enum CampaignTransTypes {
    FORM = 1
}

export const CPL_CAMPAIGN_TRANS_TYPES_MAP = {
    [CampaignTransTypes.FORM]: '表单',
} as const;

const customColumns = [
    'campaignName', 'operations', 'productWords', 'status', 'featureSource',
    'campaignOcpcBid', 'budget', 'cost', 'cplClueCount',
    'addTime', 'projectName', 'campaignTransTypes',
];
const getColumnConfigs = () => ({
    campaignName: {
        category: '属性',
        columnName: 'campaignName',
        columnText: '方案名称',
        columnType: 'STRING',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: false,
        feConfig: {
            fixType: 'left',
            align: 'left',
            columnWidth: 120,
        },
    },
    status: {
        category: '属性',
        columnName: 'status',
        columnText: '方案状态',
        columnType: 'ENUM',
        filterable: true,
        optional: false,
        sortable: false,
        draggable: false,
        feConfig: {
            columnWidth: 120,
        },
    },
    operations: {
        category: '属性',
        columnName: 'operations',
        columnText: '操作',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: false,
        feConfig: {
            fixType: 'left',
            columnWidth: 120,
        },
    },
    featureSource: {
        category: '属性',
        columnName: 'featureSource',
        columnText: '线索来源',
        columnType: 'ENUM',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: false,
        feConfig: {
            columnWidth: 120,
        },
    },
    campaignOcpcBid: {
        category: '属性',
        columnName: 'campaignOcpcBid',
        columnText: '目标线索成本',
        columnType: 'DOUBLE',
        filterable: false,
        optional: false,
        sortable: true,
        draggable: false,
        feConfig: {},
    },
    budget: {
        category: '属性',
        columnName: 'budget',
        columnText: '预算',
        columnType: 'DOUBLE',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: false,
        feConfig: {},
    },
    cost: {
        category: '属性',
        columnName: 'cost',
        columnText: '消费',
        columnType: 'DOUBLE',
        filterable: false,
        optional: false,
        sortable: true,
        draggable: false,
        feConfig: {},
    },
    cplClueCount: {
        category: '属性',
        columnName: 'cplClueCount',
        columnText: '线索量',
        columnType: 'DOUBLE',
        filterable: false,
        optional: false,
        sortable: true,
        draggable: false,
        feConfig: {},
    },
    addTime: {
        category: '属性',
        columnName: 'addTime',
        columnText: '创建时间',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: false,
        feConfig: {},
    },
    projectName: {
        category: '属性',
        columnName: 'projectName',
        columnText: '所属项目',
        columnType: 'STRING',
        filterable: false,
        optional: false,
        sortable: false,
        draggable: false,
        feConfig: {},
    },
    ...(
        isFcRfqWordUser() ? {
            productWords: {
                category: '属性',
                columnName: 'productWords',
                columnText: '业务核心词',
                columnType: 'STRING',
                filterable: false,
                optional: false,
                sortable: false,
                draggable: false,
                feConfig: {},
            },
            campaignTransTypes: {
                category: '属性',
                columnName: 'campaignTransTypes',
                columnText: '线索接收形式',
                columnType: 'STRING',
                filterable: false,
                optional: false,
                sortable: false,
                draggable: false,
                feConfig: {},
            },
        } : {}
    ),
} as ReportConfigResponse['columnConfigs']);
export const tableFieldsMap = {
    campaignName: {
        render: (_: any, record: CampaignItemOfCPL) => (
            <div>
                <div className="multiple-cut">{record.campaignName}</div>
                <div style={{color: '#999'}}>ID：{record.campaignId}</div>
            </div>
        ),
        filters: {
            maxLine: 500,
        },
    },
    status,
    operations,
    budget,
    campaignOcpcBid,
    featureSource,
    projectName: {
        render: (_: any, record: CampaignItemOfCPL) => (
            <div>
                <div className="multiple-cut">{record.projectName || '-'}</div>
            </div>
        ),
    },
    productWords,
    campaignTransTypes: {
        render: (_: any, record: CampaignItemOfCPL) => (
            <div>
                <div className="multiple-cut">
                    {record.campaignTransTypes?.map(item => CPL_CAMPAIGN_TRANS_TYPES_MAP[item]).join(',') || '-'}
                </div>
            </div>
        ),
    },
};

export const getKey = (record: CampaignItemOfCPL) => record.campaignId;
export const columnConfiguration = formatColumnConfiguration({customColumns, columnConfigs: getColumnConfigs()});

export const relatedFields = {
    campaignName: [
        'campaignId', 'adType', 'industrySolution',
    ],
    operations: [
        'excludeBrands', 'productCategoryType', 'productClueInfos', 'featureSource',
        'structuredContentIds', 'clueRegionTarget', 'clueRegionTargetType',
        'schedule', 'intentionBrands',
    ],
    status: ['pause'],
    budget: ['campaignOcpcBid'],
    campaignOcpcBid: ['budget', 'productCategoryType', 'budgetType'],
    projectName: ['projectId'],
};
export const needReplaceFields = {};
