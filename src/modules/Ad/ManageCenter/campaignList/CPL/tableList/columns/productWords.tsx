import {IconEdit} from 'dls-icons-react';
import {createCustomRender} from 'commonLibs/utils/render';
import {isBjhOrPublicFeatureSource} from '@/dicts/campaign';

function ProductWords({record, trigger}: {
    record: any;
    trigger: any;
}) {
    const {campaignId, productWords = [], featureSource} = record;

    if (isBjhOrPublicFeatureSource(featureSource)) {
        return '-';
    }
    const onClick = () => {
        trigger('openEditor', 'inline', 'productWords', campaignId);
    };

    return (
        <div className="column-cell-inline-flex">
            <span className="multiple-cut">
                {
                    productWords.length
                        ? `已添加${productWords.length}个业务核心词`
                        : '未添加业务核心词'
                }
            </span>
            <IconEdit className="inline-operation-icon" onClick={onClick} />
        </div>
    );
}


export default {
    render: createCustomRender((configs, {trigger}) => (text, record) => (
        <ProductWords
            record={record}
            trigger={trigger}
        />
    )),
};

