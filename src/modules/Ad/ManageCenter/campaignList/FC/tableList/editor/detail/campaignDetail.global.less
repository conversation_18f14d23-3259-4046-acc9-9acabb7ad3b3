.campaign-detail-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 0 24px 24px 24px;

    .campaign-detail-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 12px;
    }

    .campaign-detail-data {
        .campaign-detail-data-list {
            display: flex;
            gap: 12px;
            justify-content: space-between;

            &-card {
                width: 241px;
                border-radius: 10px;
                padding: 16px;
                background: var(--Translucent-1, #6D9FF712);

                .select-container {
                    margin-bottom: 8px;
                }

                .indicator {
                    font-family: Baidu Number;
                    font-weight: 500;
                    font-size: 20px;
                    line-height: 24px;
                    color: #0E0F11;
                }
            }
        }
    }

    .campaign-detail-setting {
        .campaign-detail-setting-container {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            width: 1000px;
            height: 88px;
            border-radius: 10px;
            gap: 16px;
            padding: 16px;
            background: var(--Translucent-1, #6D9FF712);

            .campaign-detail-setting-item {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
                line-height: 20px;

                &-name {
                    width: 62px;
                    color: #545B66;
                }

                &-content {
                    width: 80px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }

                .column-cell-flex {
                    display: flex;
                    align-items: center;
                    flex: 1;
                }

                .inline-operation-icon {
                    color: #848B99;
                    margin-left: 4px;
                }
            }

            .campaign-detail-setting-item:last-child {
                grid-column-start: 4;
                grid-column-end: 6;
            }
        }
    }
}

.campaign-detail-sub-drawer {
    padding: 0% 24px;
}

.campaign-level-adgroup-creative-container {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 12px;

    .campaign-level-adgroup-list-container,
    .campaign-level-creative-list-container {
        min-height: 300px;

        .campaign-level-creative-list-content,
        .campaign-level-adgroup-list-content {
            width: 1000px;
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;

            .campaign-level-creative-list-content-item,
            .campaign-level-adgroup-list-content-item {
                display: flex;
                flex-direction: column;
                width: 241px;
                height: 128;
                border-radius: 10px;
                gap: 12px;
                padding: 16px;
                background: var(--Translucent-1, #6D9FF712);

                .name {
                    font-weight: 500;
                    line-height: 16px;
                    color: #0E0F11;
                }

                .data {
                    font-size: 12px;
                    line-height: 16px;
                    display: grid;
                    grid-template-columns: repeat(2, 1fr);
                    gap: 8px;
                }
            }
        }

        .campaign-level-list-filter {
            position: absolute;
            top: 0;
            right: 0;
            display: flex;
            gap: 12px;
        }

        .campaign-level-pagination {
            margin-top: 12px;
        }
    }
}

.campaign-detail-drawer-title {
    display: flex;
    align-items: center;
    gap: 24px;

    &-name {
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        color: #000;
    }

    .aix-status {
        line-height: 1;
        font-size: 12px;
        font-weight: 400;
    }
}

