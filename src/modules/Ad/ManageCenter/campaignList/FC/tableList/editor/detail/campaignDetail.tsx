import {Drawer, Pagination, Radio, Select} from '@baidu/one-ui';
import {forwardRef, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {getRegionShowText} from 'commonLibs/utils/handleRegion';
import {getIsSupportThirdRegion} from 'commonLibs/region';
import {brandBizPoint} from 'commonLibs/businessPoint';
import {useRequest, useRequestCallback} from 'huse';
import {IconEdit, IconGradeCircle} from 'dls-icons-react';
import {flatten, pick, values} from 'lodash-es';
import {InlinePopoverEditor} from '@/components/common/materialList/InlinePopoverEditor';
import {CommonFormEditor} from '@/components/common/materialList/commonFormEditor';
import {fetchFcMaterialAdgroupList, updateFcAdgroup} from '@/api/manage/adgroup';
import {FcIdType} from '@/dicts/idType';
import {getToday} from '@/utils/date';
import {fetchFcCreativeTextsList, updateFcCreative} from '@/api/manage/creativeText';
import {fcAdgroupFilterOptions} from '@/dicts/fcAdgroup';
import {FcAdgroupStatus} from '@/modules/Ad/ManageCenter/adgroupList/FC/tableList/columns/status';
import {fcCreativeFilterOptions} from '@/dicts/fcCreative';
import {FcCampaignCreativeStatus} from '@/modules/Ad/ManageCenter/creativeList/creativeTextList/columns/status';
import {FcSegmentTypeEnum, FcVideoSegmentTypeEnum} from '@/dicts/segmentType';
import {parseJsonWithFallback} from '@/utils/json';
import {CampaignSourceEnum} from '@/dicts/campaign';
import {CreativeTextsColumn} from '@/modules/Ad/ManageCenter/creativeList/creativeTextList/columns/creativeTexts';
import {Empty} from '@/modules/Ad/ManageCenter/common/Feedback';
import {EllipsisText} from '@/components/common/Text/ellipsisText';
import {campaignNameFormConfig} from '../../columns/campaignName';
import {Budget} from '../../columns/budget';
import {scheduleTypeNames} from '../../columns/schedule';
import {EquipmentTypeColumn} from '../../columns/equipmentType';
import {CampaignBidType} from '../../columns/campaignBidType';
import {CampaignOcpcBidType} from '../../columns/campaignOcpcBidType';
import InlineRegionEditor from '../inline/regionEditor';
import InlineScheduleEditor from '../schedule/inlineEditor';
import InlineBatchBusinessPoint from '../inline/businessPoint';
import {defaultList, indicatorKeysConfig, metriclist, MetricType} from './config';
import {CampaignDetailContext, useCampaignDetailContext} from './context';
import './campaignDetail.global.less';

const Option = Select.Option;

const SingleDataCard = ({defaultValue, record}: {
    defaultValue: MetricType;
    record: any;
}) => {
    const [value, setValue] = useState(defaultValue);
    const selectProps = {
        className: 'select-container',
        size: 'xsmall',
        width: 136,
        defaultValue,
        onChange: setValue,
        style: {backgroundColor: '#6d9ff700'},
    };
    return (
        <div className="campaign-detail-data-list-card" key={defaultValue}>
            <Select {...selectProps}>
                {
                    metriclist.map(keyName => {
                        const {label, trans, unit} = indicatorKeysConfig[keyName];
                        return (
                            <Option value={keyName} key={keyName}>{label}{unit ? `（${unit}）` : ''}</Option>
                        );
                    })
                }
            </Select>
            <div className="indicator">
                {indicatorKeysConfig[value].trans(record[value])}
            </div>
        </div>
    );
};

const CampaignSettingContainer = ({record, saveMethods}: {
    record: any;
    saveMethods: any;
}) => {
    const {
        inlineSaveCampaign, inlineSaveBusinessPoint, inlineSaveCampaignSchedule,
        inlineSaveCampaignBudget,
    } = saveMethods || {};
    const {
        regionTarget, regionType, marketingTargetId, campaignId,
        scheduleTemplateId, scheduleTemplateName, businessPointId,
        businessPointName, campaignName,
    } = record;
    const isSupportThirdRegion = getIsSupportThirdRegion({marketingTargetId});
    const content = getRegionShowText({regionTarget, regionType, isSupportThirdRegion});

    const {openEditor, closeEditor} = useCampaignDetailContext();
    return (
        <div className="campaign-detail-setting-container">
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">方案名称</span>
                <span className="campaign-detail-setting-item-content">
                    <EllipsisText>{record.campaignName}</EllipsisText>
                </span>
                <InlinePopoverEditor
                    renderEditor={CommonFormEditor}
                    initialData={{
                        campaignName,
                    }}
                    formConfig={campaignNameFormConfig}
                    onSave={async v => inlineSaveCampaign(campaignId, v)}
                />
            </div>
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">方案ID</span>
                {record.campaignId}
            </div>
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">预算</span>
                <Budget
                    campaignId={record.campaignId}
                    budget={record.budget}
                    projectId={record.projectId}
                    useSharedBudget={record.useSharedBudget}
                    budgetPeriodType={record.budgetPeriodType}
                    campaignOcpcBid={record.campaignOcpcBid}
                    campaignInfo={record}
                    onSaveCampaignBudget={inlineSaveCampaignBudget}
                />
            </div>
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">地域</span>
                <div className="column-cell-flex">
                    <div>
                        {content}
                        <IconEdit
                            className="inline-operation-icon"
                            onClick={() => openEditor({
                                title: '修改计划地域',
                                // eslint-disable-next-line max-len
                                Comp: forwardRef((_, ref) => <InlineRegionEditor currentId={campaignId} getMaterialById={(id: number) => record} inlineSaveCampaign={inlineSaveCampaign} ref={ref} closeEditor={closeEditor} />),
                            })}
                        />
                    </div>
                </div>
            </div>
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">时段</span>
                <div className="column-cell-flex">
                    <span className="multiple-cut">
                        {scheduleTypeNames[scheduleTemplateId] || scheduleTemplateName}
                    </span>
                    <IconEdit
                        className="inline-operation-icon"
                        onClick={() => openEditor({
                            title: '修改计划时段',
                            // eslint-disable-next-line max-len
                            Comp: forwardRef((_, ref) => <InlineScheduleEditor currentId={campaignId} getMaterialById={(id: number) => record} inlineSaveCampaignSchedule={inlineSaveCampaignSchedule} ref={ref} closeEditor={closeEditor} />),
                        })}
                    />
                </div>
            </div>
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">推广业务</span>
                <div className="column-cell-flex">
                    {
                        businessPointId && businessPointId === brandBizPoint
                            ? <IconGradeCircle className="brand-icon" />
                            : null
                    }
                    <EllipsisText>{businessPointName ? businessPointName : '去补充'}</EllipsisText>
                    <IconEdit
                        className="inline-operation-icon"
                        onClick={() => openEditor({
                            title: '修改推广业务',
                            // eslint-disable-next-line max-len
                            Comp: forwardRef((_, ref) => <InlineBatchBusinessPoint currentId={campaignId} getMaterialById={(id: number) => record} inlineSaveBusinessPoint={inlineSaveBusinessPoint} ref={ref} />),
                        })}
                    />
                </div>
            </div>
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">推广设备</span>
                <EquipmentTypeColumn
                    equipmentType={record.equipmentType}
                    campaignInfo={record}
                    subMarketingTargetId={record.subMarketingTargetId}
                    onSaveCampaign={inlineSaveCampaign}
                />
            </div>
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">优化方式</span>
                <CampaignBidType
                    campaignBidType={record.campaignBidType}
                    projectId={record.projectId}
                />
            </div>
            <div className="campaign-detail-setting-item">
                <span className="campaign-detail-setting-item-name">竞价策略</span>
                <CampaignOcpcBidType
                    campaignOcpcBidType={record.campaignOcpcBidType}
                    campaignId={record.campaignId}
                    projectId={record.projectId}
                    onSaveCampaign={inlineSaveCampaign}
                />
            </div>
        </div>
    );
};

const defaultStatusFilterOption = [{
    label: '不限',
    value: '',
}];

const defaultSortFilterOption = [{
    label: '不限',
    value: '',
}];

const sortOptions = defaultList.reduce((arr, field) => {
    const fieldName = indicatorKeysConfig[field].label;
    const newArr = [{
        label: `按照${fieldName}升序`,
        value: `${field}-ascend`,
    }, {
        label: `按照${fieldName}降序`,
        value: `${field}-descend`,
    }];
    return [...arr, ...newArr];
}, []);


const ListFilterSelect = ({
    statusFilter,
    setStatusFilter,
    sorter,
    setSorter,
    filterOptions,
}: {
    statusFilter: string;
    setStatusFilter: (value: string) => void;
    sorter: string;
    setSorter: (value: string) => void;
    filterOptions: Array<{
        label: string;
        value: string;
    }>;
}) => {
    return (
        <div className="campaign-level-list-filter">
            <Select
                value={statusFilter}
                onChange={setStatusFilter}
                customRenderTarget={(_, option) => `状态：${option.option.props?.label}`}
                width={120}
            >
                {
                    [...defaultStatusFilterOption, ...filterOptions].map(({value, label}) => (
                        <Option key={value} value={value} label={label}>{label}</Option>
                    ))
                }
            </Select>
            <Select
                value={sorter}
                onChange={setSorter}
                customRenderTarget={(_, option) => `数据指标：${option.option.props?.label}`}
                width={150}
            >
                {
                    [...defaultSortFilterOption, ...sortOptions].map(({value, label}) => (
                        <Option key={value} value={value} label={label}>{label}</Option>
                    ))
                }
            </Select>
        </div>
    );
};

const AdgroupList = ({record}: { record: any }) => {
    const {campaignId} = record;

    const [adgroupSorter, setAdgroupSorter] = useState<string>('');

    const [adgroupStatusFilter, setAdgroupStatusFilter] = useState('');
    const [pageNo, setPageNo] = useState(1);


    const {
        data: {rows: adgroupRows = [], totalRowCount: adgroupTotalRowCount} = {},
    } = useRequest(fetchFcMaterialAdgroupList, {
        fields: ['adgroupName', 'status', 'pause', 'cost', 'impression', 'click', 'ctr'],
        sorter: adgroupSorter
            ? {sortField: adgroupSorter.split('-')[0], sortType: adgroupSorter.split('-')[1]}
            : {sortField: '', sortType: ''},
        pagination: {pageNo, pageSize: 8},
        timeRange: {startDate: getToday(), endDate: getToday()},
        filters: adgroupStatusFilter ? [{column: 'status', values: [adgroupStatusFilter], operator: 'in'}] : [],
        reportType: 1620200,
        pageLevel: {idType: FcIdType.CAMPAIGN_LEVEL, levelId: campaignId},
    });

    const [adgroupDataMap, setAdgroupDataMap] = useState<Record<number, any>>({});
    useEffect(() => {
        setAdgroupDataMap(
            adgroupRows.reduce((acc, adgroup) => {
                acc[adgroup.adgroupId] = adgroup;
                return acc;
            }, {} as Record<number, any>)
        );
    }, [adgroupRows]);

    const onSaveAdgroupStatus = async (adgroupId: number, values: any) => {
        const data = await updateFcAdgroup(adgroupId, values);
        setAdgroupDataMap(prev => ({
            ...prev,
            [adgroupId]: {...prev[adgroupId], ...data[0]},
        }));
    };

    return (
        <div className="campaign-level-adgroup-list-container">
            <ListFilterSelect
                statusFilter={adgroupStatusFilter}
                setStatusFilter={setAdgroupStatusFilter}
                sorter={adgroupSorter}
                setSorter={setAdgroupSorter}
                filterOptions={fcAdgroupFilterOptions}
            />
            {
                adgroupRows.length ? (
                    <div className="campaign-level-adgroup-list-content">
                        {
                            adgroupRows?.map(adgroup => (
                                <div className="campaign-level-adgroup-list-content-item" key={adgroup.adgroupId}>
                                    <div className="name">{adgroup.adgroupName}</div>
                                    <div className="data">
                                        {
                                            defaultList.map(field => {
                                                const {label, trans} = indicatorKeysConfig[field];
                                                return (
                                                    <span key={field}>{label}：{trans(adgroup[field])}</span>
                                                );
                                            })
                                        }
                                    </div>
                                    <FcAdgroupStatus
                                        status={adgroupDataMap[adgroup.adgroupId]?.status || adgroup.status}
                                        pause={adgroupDataMap[adgroup.adgroupId]?.pause || adgroup.pause}
                                        adgroupId={adgroup.adgroupId}
                                        onSaveAdgroup={onSaveAdgroupStatus}
                                    />
                                </div>
                            ))
                        }
                    </div>
                ) : <Empty />
            }
            <Pagination
                className="campaign-level-pagination"
                total={adgroupTotalRowCount}
                pageNo={pageNo}
                pageSize={8}
                onPageNoChange={e => setPageNo(e.target.value)}
                showSizeChange={false}
                hideOnSinglePage
            />
        </div>
    );
};

const CreativeList = ({record}: {record: any}) => {
    const {campaignId} = record;

    const [creativeSorter, setCreativeSorter] = useState<string>('');

    const [creativeStatusFilter, setCreativeStatusFilter] = useState('');
    const [pageNo, setPageNo] = useState(1);


    const {
        data: {rows: creativeRows = [], totalRowCount: creativeTotalRowCount} = {},
    } = useRequest(fetchFcCreativeTextsList, {
        fields: [
            'creativeId', 'description1', 'description2', 'shadowTitle', 'shadowDescription1',
            'cost', 'impression', 'click', 'ctr', 'status', 'title', 'creativeType',
            'programTitles', 'programDescriptions', 'shadowProgramTitles', 'shadowProgramDescriptions',
            'campaignName', 'campaignId', 'adgroupId', 'adgroupName',
        ],
        sorter: creativeSorter
            ? {sortField: creativeSorter.split('-')[0], sortType: creativeSorter.split('-')[1]}
            : {sortField: '', sortType: ''},
        pagination: {pageNo, pageSize: 8},
        timeRange: {startDate: getToday(), endDate: getToday()},
        filters: creativeStatusFilter ? [{column: 'status', values: [creativeStatusFilter], operator: 'in'}] : [],
        pageLevel: {idType: FcIdType.CAMPAIGN_LEVEL, levelId: campaignId},
    });

    const [creativeDataMap, setCreativeDataMap] = useState<Record<number, any>>({});

    useEffect(() => {
        setCreativeDataMap(
            creativeRows.reduce((acc, creative) => {
                acc[creative.creativeId] = creative;
                return acc;
            }, {} as Record<number, any>)
        );
    }, [creativeRows]);

    const onSaveCreativeStatus = async (creativeId: number, values: any) => {
        const data = await updateFcCreative(creativeId, values);
        setCreativeDataMap(prev => ({
            ...prev,
            [creativeId]: {...prev[creativeId], ...data[0]},
        }));
    };

    return (
        <div className="campaign-level-creative-list-container">
            <ListFilterSelect
                statusFilter={creativeStatusFilter}
                setStatusFilter={setCreativeStatusFilter}
                sorter={creativeSorter}
                setSorter={setCreativeSorter}
                filterOptions={fcCreativeFilterOptions}
            />
            {
                creativeRows.length ? (
                    <div className="campaign-level-creative-list-content">
                        {
                            creativeRows?.map(creative => {
                                const images = flatten(values(pick(
                                    record.segmentBinds || {},
                                    [
                                        FcSegmentTypeEnum.BASE_CREATIVE_PC,
                                        FcSegmentTypeEnum.BASE_CREATIVE_WISE,
                                        FcSegmentTypeEnum.LARGE_PIC_PC,
                                        FcSegmentTypeEnum.LARGE_PIC_WISE,
                                    ]
                                ))).map(({
                                    content, auditContent,
                                }) => parseJsonWithFallback(auditContent || content, null)).filter(Boolean);

                                const videos = flatten(values(pick(
                                    record.segmentBinds || {},
                                    [
                                        FcVideoSegmentTypeEnum.VIDEO_NINE_SIXTEEN,
                                        FcVideoSegmentTypeEnum.VIDEO_ONE_ONE,
                                        FcVideoSegmentTypeEnum.VIDEO_SIXTEEN_NINE,
                                    ]
                                )))
                                    .map(({
                                        content, auditContent,
                                    }) => parseJsonWithFallback(auditContent || content, null)?.videos?.[0])
                                    .filter(Boolean);
                                return (
                                    <div
                                        className="campaign-level-creative-list-content-item"
                                        key={creative.creativeId}
                                    >
                                        <CreativeTextsColumn
                                            creativeType={creative.creativeType}
                                            title={creative.title}
                                            description1={creative.description1}
                                            description2={creative.description2}
                                            shadowTitle={creative.shadowTitle}
                                            shadowDescription1={creative.shadowDescription1}
                                            programTitles={creative.programTitles}
                                            programDescriptions={creative.programDescriptions}
                                            shadowProgramTitles={creative.shadowProgramTitles}
                                            shadowProgramDescriptions={creative.shadowProgramDescriptions}
                                            images={images}
                                            videos={videos}
                                            campaignName={creative.campaignName}
                                            campaignId={creative.campaignId}
                                            adgroupName={creative.adgroupName}
                                            adgroupId={creative.adgroupId}
                                            campaignSource={CampaignSourceEnum.FC}
                                            creativeId={creative.creativeId}
                                        />
                                        <div className="data">
                                            {
                                                defaultList.map(field => {
                                                    const {label, trans} = indicatorKeysConfig[field];
                                                    return (
                                                        <span key={field}>{label}：{trans(creative[field])}</span>
                                                    );
                                                })
                                            }
                                        </div>
                                        <FcCampaignCreativeStatus
                                            status={creativeDataMap[creative.creativeId]?.status || creative.status}
                                            creativeId={creative.creativeId}
                                            onSaveCreative={onSaveCreativeStatus}
                                            canShowOfflineReason={false}
                                        />
                                    </div>
                                );
                            })
                        }
                    </div>
                ) : <Empty />
            }
            <Pagination
                className="campaign-level-pagination"
                total={creativeTotalRowCount}
                pageNo={pageNo}
                pageSize={8}
                onPageNoChange={e => setPageNo(e.target.value)}
                showSizeChange={false}
                hideOnSinglePage
            />
        </div>
    );
};

const CampaignLevelAdgroupCreative = ({record}: {record: any}) => {

    const [radioValue, setRadioValue] = useState('adgroup');

    return (
        <div className="campaign-level-adgroup-creative-container">
            <Radio.Group value={radioValue} onChange={e => setRadioValue(e.target.value)}>
                <Radio.Button value="adgroup">单元</Radio.Button>
                <Radio.Button value="creative">创意</Radio.Button>
            </Radio.Group>
            {
                radioValue === 'adgroup' && (
                    <AdgroupList record={record} />
                )
            }
            {
                radioValue === 'creative' && (
                    <CreativeList record={record} />
                )
            }
        </div>
    );
};


const CampaignDetailComp = ({record, saveMethods}: {
    record: any;
    saveMethods: any;
}) => {
    const [openField, setOpenField] = useState('');
    const [editor, setEditor] = useState<any>(null);
    const closeEditor = useCallback(() => {
        setEditor(null);
    }, []);
    const openEditor = useCallback((editor: any) => {
        setEditor(editor);
    }, []);
    const Editor = useMemo(
        () => (editor?.Comp || (() => null)),
        [editor?.Comp]
    );
    const ref = useRef<HTMLDivElement>(null);

    const baseProps = {
        closeEditor,
        openEditor,
    };
    const editorRef = useRef({});

    const onSave = useCallback(async () => {
        if (editorRef.current?.onSave) {
            let data = null;
            try {
                data = await editorRef.current.onSave();
                closeEditor();
                return data;
            }
            catch (error) {
                throw error;
            }
        }
        return Promise.resolve();
    }, [closeEditor]);

    const onCancel = useCallback(() => {
        if (editorRef.current?.onCancel) {
            editorRef.current.onCancel();
        }
        closeEditor();
    }, [closeEditor]);
    const [onOk, {pending: isSaving}] = useRequestCallback(onSave, null);
    return (
        <CampaignDetailContext.Provider
            value={{openEditor, openField, closeEditor}}
        >
            <div ref={ref}>
                <div className="campaign-detail-container">
                    <div className="campaign-detail-data">
                        <div className="campaign-detail-title">关注指标</div>
                        <div className="campaign-detail-data-list">
                            {
                                defaultList.map(keyName => (
                                    <SingleDataCard
                                        key={keyName}
                                        defaultValue={keyName}
                                        record={record}
                                    />
                                ))
                            }
                        </div>
                    </div>
                    <div className="campaign-detail-setting">
                        <div className="campaign-detail-title">方案设置</div>
                        <CampaignSettingContainer record={record} saveMethods={saveMethods} />
                    </div>

                    <div className="campaign-detail-adgroup-creative">
                        <div className="campaign-detail-title">方案详情</div>
                        <CampaignLevelAdgroupCreative record={record} />
                    </div>
                </div>
                <Drawer
                    visible={!!editor}
                    onClose={closeEditor}
                    title={editor?.title || ''}
                    placement="bottom"
                    mask={false}
                    drawerStyle={{width: '1080px', height: '640px'}}
                    getContainer={() => ref.current as HTMLElement}
                    onOk={onOk}
                    onCancel={onCancel}
                    okProps={{loading: isSaving}}
                    okText="保存"
                    hideDefaultFooter={editor?.props?.hideDefaultFooter || false}
                >
                    <div className="campaign-detail-sub-drawer">
                        <Editor {...baseProps} {...(editor?.props || {})} ref={editorRef} />
                    </div>
                </Drawer>
            </div>
        </CampaignDetailContext.Provider>

    );
};

export default CampaignDetailComp;
