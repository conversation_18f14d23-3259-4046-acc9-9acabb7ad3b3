import {partial} from 'lodash-es';
import {<PERSON><PERSON>, Toolt<PERSON>} from '@baidu/one-ui';
import {deleteCampaigns} from '@/api/updateCampaign';
import {CampaignItemOfB2B} from '@/interface/campaign';
import {CampaignSourceEnum, PRODUCT} from '@/dicts/campaign';
import {OPERATION_TYPE} from '@/dicts/fieldOperationType';
import {createCustomRender} from '@/utils/render';
import {withSumCell} from '@/utils/handleSummary';
import {ConfirmToDeleteByOneUI} from '@/components/CampaignManagement/ConfirmToDelete';
import {makeLogger} from '@/modules/Ad/ManageCenter/common/utils';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {useAdRoute} from '@/modules/Ad/routes';

const FIELD = levelTypeMapForLogOrSwr.manageCampaignList;
const sendLog = makeLogger({level: FIELD, source: 'inline', 'extra_params': PRODUCT.B2B_PROMOTION});

interface OperationsProps {
    campaignName: CampaignItemOfB2B['campaignName'];
    campaignId: CampaignItemOfB2B['campaignId'];
    refreshList: () => void;
}
const Operations = ({campaignName, campaignId, refreshList}: OperationsProps) => {
    const onDeleteCampaign = async () => {
        await deleteCampaigns([campaignId]);
        sendLog({event: 'delete'});
        refreshList();
    };
    const {linkToChat} = useAdRoute();

    return (
        <div className="operations-column">
            <Tooltip title="将跳转至对话区进行查看">
                <Button
                    size="small"
                    type="text-strong"
                    onClick={() => linkToChat([
                        'user',
                        `查看【${campaignName}】方案内容。`,
                        {
                            trigger: 'manage_center',
                            custom: {
                                currentField: 'getCampaign',
                                currentFieldOperate: OPERATION_TYPE.GET,
                                campaignId,
                                campaignName,
                                campaignSource: CampaignSourceEnum.AIX,
                                params: {
                                    expandOption: true,
                                },
                            },
                        },
                    ])}
                >
                    管理
                </Button>
            </Tooltip>
            <ConfirmToDeleteByOneUI
                defaultVisible={false}
                onConfirm={onDeleteCampaign}
                content={`确定要删除营销方案【${campaignName}】吗？方案一旦删除无法恢复。`}
            >
                <Button size="small" type="text-strong">删除</Button>
            </ConfirmToDeleteByOneUI>
        </div>
    );
};

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell((text, record) => (
        <Operations
            campaignId={record.campaignId}
            campaignName={record.campaignName}
            refreshList={() => trigger('refresh')}
        />
    ))),
};
