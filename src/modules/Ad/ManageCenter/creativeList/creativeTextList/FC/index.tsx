/* eslint-disable max-statements */
/* eslint-disable no-negated-condition */
import {memo, useMemo, useEffect, useCallback, useImperativeHandle, forwardRef, Ref, useState} from 'react';
import {Pagination, Table, Button, Alert, Link} from '@baidu/one-ui';
import AsyncBanner, {fengchaoConfig} from 'commonLibs/asyncTaskBanner';
import classNames from 'classnames';
import queryString from 'query-string';
import {useResource} from 'react-suspense-boundary';
import getDepsFields from '@/utils/materialList/getDepsFields';
import {useSortableColumns, useDraggableColumns, useColumns} from '@/hooks/tableList/columns';
import {useObservedLocalStorage, getColumnWidthStorageKeyPath} from '@/hooks/storage';
import {Base} from '@/interface/base';
import {toOneUIPaginationProps} from '@/hooks/pagination';
import {unshift} from '@/utils/array';
import {useAdRoute} from '@/modules/Ad/routes';
import {toOneUIRowSelectionProps} from '@/utils/selection';
import {MaterialList} from '@/interface/tableList';
import MaterialTabs from '@/components/common/MaterialTabs';
import {Empty} from '@/components/common/materialList/emptyText';
import {FC_CREATIVE_TEXT_LIST_REPORT_TYPE} from '@/dicts/reportType';
import FilterList, {FilterListOperations} from '@/components/common/materialList/FilterList';
import {useMaterialListConfiguration} from '@/hooks/tableList/configuration';
import EditorContainer from '@/components/common/materialList/EditorContainer';
import ShieldContainer from '@/modules/Ad/ManageCenter/creativeList/components/ShieldContainer';
import {useControl} from '@/hooks/externalControl';
import {FcIdType} from '@/dicts/idType';
import {defaultOperatorConfig} from '@/config/tableList/defaultOperatorConfig';
import {scrollbarBottomFixedTableProps} from '@/config/tableList/config';
import {useFilteredIdTypeByQuery} from '@/hooks/tableList/query';
import {getUserId} from '@/utils';
import {PageType} from '@/dicts/pageType';
import {getTables} from '@/components/common/MaterialTabs/table';
import FilterEntry from '@/components/common/materialList/FilterEntry';
import {useManageCenterDateRange, getInitialDateRange} from '../../../context';
import {Calendar} from '../../../common/Calendar';
import SearchBox from '../../../common/SearchBox';
import {
    CreateCreativeMultiEntry,
    CreateCreativeEntryProps,
    CreateCreativeMultiEntryProps,
} from '../slots/CreativeNewEntry';
import {ProductAndCampaignTypeSelect} from '../../../common/ProductAndCampaignTypeSelect';
import {getCreativeRowKey, useFcCreativeTextsList, $configForPreload, metadata} from './materialQuery';
import {tableFieldsMap, relatedFields, needReplaceFields, supportedFilterEntryFields} from './tableConfig';
import {OperationBar} from './operationBar';
import {BatchOperation} from './batchOperation';
import {editorConfig} from './editor/config';
import './index.global.less';

export function getConfigForPreload({
    customColumns,
}: {reportType: number, customColumns: MaterialList.ColumnConfiguration['customColumns']}) {
    return {
        fetcher: $configForPreload.fetcher,
        params: {
            ...$configForPreload.params,
            timeRange: {startDate: getInitialDateRange()[0], endDate: getInitialDateRange()[1]},
            fields: getDepsFields(customColumns, relatedFields, needReplaceFields),
        },
        options: $configForPreload.options,
    };
}

const CREATIVE_CLEAN_UP_TIP = 'creative_clean_up_tip';

const AlertPageWithAsyncTasks = ({asyncTasks, refresh}: { asyncTasks: any, refresh: () => void }) => {
    const [asyncVisible, setAsyncVisible] = useState(false);
    useEffect(
        () => {
            if ((!!asyncTasks.unreadTask.length || !!asyncTasks.processingTask.length)) {
                setAsyncVisible(true);
            }
        },
        [asyncTasks.unreadTask, asyncTasks.processingTask]
    );
    const alertDataSource = useMemo(() => [
        ...(
            asyncVisible
                ? [
                    <AsyncBanner
                        key="async_banner"
                        {...asyncTasks}
                        refresh={refresh}
                        taskMapConfig={fengchaoConfig}
                        onAlertPageClose={() => setAsyncVisible(false)}
                    />,
                ]
                : []
        ),
        ...(
            !localStorage.getItem(CREATIVE_CLEAN_UP_TIP)
                ? [
                    <Alert
                        key="creative_clean_up_tip"
                        content={
                            <>
                                由于系统升级，自2025.6.5起不再支持在创意层级设置访问网址及监控后缀；2025.8.5后，创意层级的访问网址及监控后缀将不再生效。去
                                <Link size="small" type="strong" target="_blank" toUrl="https://yingxiao.baidu.com/product/active/detail?detailId=2956&from=yingxiao">营销中心</Link>
                                查看
                            </>
                        }
                        type="warning"
                        showIcon
                        closable
                        onClose={() => {
                            localStorage.setItem(CREATIVE_CLEAN_UP_TIP, true);
                        }}
                        size="small"
                    />,
                ]
                : []
        ),
    ], [asyncTasks, asyncVisible, refresh]);


    const onClose = (index: number) => {
        alertDataSource.splice(index, 1);
    };
    return (
        <Alert.Page dataSource={alertDataSource} onClose={onClose} size="small" />
    );
};

interface FcCreativeTableListProps extends CreateCreativeEntryProps, CreateCreativeMultiEntryProps {
    initialFilters: MaterialList.FilterItem[];
}

// eslint-disable-next-line max-statements
function FcCreativeTableList(
    {
        initialFilters,
        linkToCreateCreativeInChat,
        onClickCreateCreativeMenu,
        platform,
        campaignType,
        onFCCampaignTypeChange,
    }: FcCreativeTableListProps,
    ref: Ref<unknown>
) {
    const {startDate, endDate} = useManageCenterDateRange();
    const [ControlledEditor, {openEditor}] = useControl(EditorContainer);
    const [ControlledShield, {openShield}] = useControl(ShieldContainer);

    const {linkTo} = useAdRoute();

    const [columnConfiguration, modConfiguration, control_, updateConfiguration] = useMaterialListConfiguration(
        FC_CREATIVE_TEXT_LIST_REPORT_TYPE,
        {withNewCategory: true}
    );
    const {idType, id} = useFilteredIdTypeByQuery();
    const [
        tables,
    ] = useResource(getTables, {accountId: getUserId() as string, type: PageType.CreativeTextList});
    const {
        filters,
        columns: columns_,
        handleColumnAction,
        filterMethods,
        getFilterContentByField,
        getFilterDropdownByField,
    } = useColumns({columnConfiguration, tableFieldsMap, extraConfig: {initialFilters, enableParamsToQuery: true}});

    const {customColumns} = columnConfiguration;
    const customFields = useMemo(
        () => getDepsFields(customColumns, relatedFields, needReplaceFields),
        [customColumns]
    );

    const [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            sorter,
            pagination,
            selection,
            asyncTasks,
        },
        {
            setPageNo,
            setPageSize,
            refresh,
            onSort,
            inlineSaveCreative,
            inlineSaveCreativeSegments,
            inlineSaveCreativeMarkers,
            inlineSaveCopyCreative,
            // batchSaveCreativeText,
            selectionOperations,
            batchDeleteCreatives,
            inlineSaveCreativeUseBatchAPI,
            getCreativeById,
            batchMethods,
        },
    ] = useFcCreativeTextsList({
        filters,
        date: {startDate, endDate},
        customFields,
        pageLevel: {idType, levelId: +id},
        materialLevel: FcIdType.IDEA_LEVEL,
    });

    const dataSource = useMemo(
        () => (rows.length ? unshift(rows, {...summary, totalCount}) : []),
        [rows, summary, totalCount]
    );

    const {onSelectChange, selectAll, getSelectedInfo, resetRowSelection, selectRows} = selectionOperations;
    const columns = useSortableColumns(columns_, sorter);
    const headBordered = useMemo(() => columns.some(col => col.children), [columns]);
    const paginationProps = toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount);
    const columnWidthStorageKeyPath = useMemo(() => getColumnWidthStorageKeyPath(metadata.levelType), []);
    const columnWidthStorage = useObservedLocalStorage(columnWidthStorageKeyPath, {});
    const [
        columnsWithCustomWidth,
        {onDragEnd, resetColumnsWidth},
    ] = useDraggableColumns(columns, {storage: columnWidthStorage as Base.LocalStorageTuple<MaterialList.Columns>});

    // to one-ui props
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange, selectAll, showSelectAll: true},
            rows,
            {getId: getCreativeRowKey, multiPageSelection: true}
        ),
        [selection, onSelectChange, selectAll, rows],
    );

    const {selectedCount} = getSelectedInfo();

    const onAddCreative = useCallback(() => {
        openEditor('batch', 'addCreative', undefined, {hideDefaultFooter: true, type: 'basic'});
    }, [openEditor]);

    const linkToAddCreative = () => {
        linkTo(PageType.NewCreative, {inheritQuery: true});
    };
    const tableListProps = {
        rowKey: getCreativeRowKey,
        size: 'small',
        autoHideOperation: 'filter',
        locale: {
            emptyText: (
                <Empty
                    hasFilter={!!filters.length}
                    error={error}
                    text={(<div>当前暂无创意文案，去<Button type="text-strong" onClick={linkToAddCreative}>添加</Button></div>)}
                    name="创意文案"
                />
            ),
        },
        pagination: false, // 如果采用表格的分页器，数据要为前端分页，如果是后端排序，现在仅支持自己写pager
        updateWidthChange: true, // 注意此属性，如果为false，浏览器缩放时不会重新计算表格宽度
        loading: pending,
        onSortClick: onSort,
        columns: columnsWithCustomWidth,
        dataSource,
        headBordered,
        onDragEnd,
        rowSelection: rowSelectionProps,
        headerBottom: <AlertPageWithAsyncTasks asyncTasks={asyncTasks} refresh={refresh} />,
        ...scrollbarBottomFixedTableProps,
        headerFixTop: selectedCount ? 36 : 0,
    };

    useEffect(() => {
        // 添加创意sug点击会跳转到本页面，如果url中有openDrawer=true，则打开新建创意抽屉
        const query = queryString.parse(location.search);
        if (query.openDrawer) {
            openEditor('batch', 'addCreative', undefined, {hideDefaultFooter: true, type: 'basic'});
        }
    }, [openEditor]);

    useEffect(
        () => handleColumnAction('refresh', refresh),
        [handleColumnAction, refresh]
    );

    useEffect(
        () => handleColumnAction('inlineSaveCreative', inlineSaveCreative),
        [handleColumnAction, inlineSaveCreative]
    );

    useEffect(
        () => handleColumnAction('inlineSaveCreativeSegments', inlineSaveCreativeSegments),
        [handleColumnAction, inlineSaveCreativeSegments]
    );

    useEffect(
        () => handleColumnAction('inlineSaveCreativeMarkers', inlineSaveCreativeMarkers),
        [handleColumnAction, inlineSaveCreativeMarkers]
    );

    useEffect(
        () => handleColumnAction('inlineSaveCopyCreative', inlineSaveCopyCreative),
        [handleColumnAction, inlineSaveCopyCreative]
    );

    useEffect(
        () => handleColumnAction('inlineSaveCreativeUseBatchAPI', inlineSaveCreativeUseBatchAPI),
        [handleColumnAction, inlineSaveCreativeUseBatchAPI]
    );

    useEffect(
        () => handleColumnAction('openEditor', openEditor),
        [handleColumnAction, openEditor]
    );

    const filterListProps = {
        filters,
        deleteFilterByIndex: filterMethods.deleteFilterByIndex,
        changeFilterByIndex: filterMethods.changeFilterByIndex,
        getFilterContentByField,
    };

    const filterBarProps = {
        columnConfiguration,
        modConfiguration,
        updateConfiguration,
        summary,
        openShield,
        reportType: FC_CREATIVE_TEXT_LIST_REPORT_TYPE,
        refresh,
        pending,
        resetColumnsWidth,
    };

    const batchEditProps = {
        selection,
        selectionOperations,
        totalCount,
        getMaterialById: getCreativeById,
        currentPageCount: rows.length,
        batchSaveCreatives: batchMethods.batchSaveCreatives,
        refresh,
        batchDeleteCreatives,
        openEditor,
    };

    const dialogEditorProps = {
        rows,
        getSelectedInfo,
        resetRowSelection,
        selectRows,
        getMaterialById: getCreativeById,
        editorConfig,
        refreshList: refresh,
        filters,
        sorter,
        targetNameField: 'title',
        targetLevelName: '创意文案',
        saveMethods: {
            inlineSaveCreative,
            inlineSaveCreativeSegments,
            inlineSaveCopyCreative,
            ...batchMethods,
        },
    };

    const controlledShieldProps = {
        url: 'creativeshieldTitle',
    };
    const onSearch = useCallback(
        (value?: string, {operator}: {operator?: string} = {}) => {
            if (!value) {
                return;
            }
            filterMethods.changeFilterByField(
                'creativeText',
                {
                    values: [value],
                    operator: operator || defaultOperatorConfig.string[0].value,
                }
            );
        },
        [filterMethods.changeFilterByField]
    );
    useImperativeHandle(ref, () => ({onAddCreative, onSearch}));
    const cls = classNames({
        'manage-center-creative-text-list__fc': true,
        'manage-center-creative-text-list__fc_tabs': tables.length > 1,
    });

    return (
        <>
            <MaterialTabs currentPageType={PageType.CreativeTextList} />
            {
                selectedCount ? (
                    <div className="creative-text-list-fc-batch-operation-bar">
                        <BatchOperation {...batchEditProps} />
                    </div>
                ) : (
                    <div className="manage-center-creative-text-list-header">
                        <div>
                            <CreateCreativeMultiEntry onClickCreateCreativeMenu={onClickCreateCreativeMenu} />

                            <ProductAndCampaignTypeSelect
                                value={campaignType}
                                platform={platform}
                                // @ts-ignore
                                onChange={onFCCampaignTypeChange}
                                style={{width: 170}}
                                allowClear={false}
                            />

                            {/* 日期区间 */}
                            <Calendar />

                            {/* 搜索框 */}
                            <SearchBox fieldName="创意文案" onSearch={onSearch} width={190} />

                            <FilterEntry
                                materialLevel={FcIdType.IDEA_LEVEL}
                                filterFields={supportedFilterEntryFields}
                                columnConfigs={columnConfiguration.columnConfigs}
                                replaceFilters={filterMethods.replaceFilters}
                                changeFilterByField={filterMethods.changeFilterByField}
                                getFilterDropdownByField={getFilterDropdownByField}
                            />
                        </div>
                        <div>
                            <OperationBar {...filterBarProps} />
                        </div>
                    </div>
                )
            }
            <FilterList {...filterListProps}>
                <FilterListOperations
                    materialLevel={FcIdType.IDEA_LEVEL}
                    filters={filters}
                    resetFilters={filterMethods.resetFilters}
                />
            </FilterList>
            <div className={cls}>
                <div className="table-list-container">
                    {(
                        // @ts-ignore
                        <Table {...tableListProps} />
                    )}
                    <Pagination className="table-pagination" {...paginationProps} />
                </div>
                {/* @ts-ignore */}
                {ControlledEditor && <ControlledEditor {...dialogEditorProps} />}
                {ControlledShield && <ControlledShield {...controlledShieldProps} />}
            </div>
        </>
    );
}

export default memo(forwardRef(FcCreativeTableList));
