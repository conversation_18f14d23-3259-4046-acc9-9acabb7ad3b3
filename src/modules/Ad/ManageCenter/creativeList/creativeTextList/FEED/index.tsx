import {use<PERSON><PERSON>back, Ref, forwardRef, useImperativeHandle, useRef, useState} from 'react';
import {SharedInfoContext} from 'commonLibs/context/sharedInfo';
import {createPortal} from 'react-dom';
import {addFeedFilterTemplate} from '@/api/manage/filters';
import {useColumns} from '@/hooks/tableList/columns';
import {useMaterialListConfiguration} from '@/hooks/tableList/configuration';
import FilterList, {FilterListOperations} from '@/components/common/materialList/FilterList';
import FilterEntry from '@/components/common/materialList/FilterEntry';
import {statusKeyMap} from '@/config/feed/creative';
import {MaterialList} from '@/interface/tableList';
import {PageType} from '@/dicts/pageType';
import {FeedMaterialLevel, MATERIAL_LEVEL} from '@/dicts/idType';
import {FEED_CREATIVE_LIST_REPORT_TYPE} from '@/dicts/reportType';
import {getUserId} from '@/utils';
import FeedMaterialTabs from '@/components/common/FeedMaterialTabs';
import {defaultOperatorConfig} from '@/config/tableList/defaultOperatorConfig';
import {isFeedPromotionMainUser} from '@/utils/getFlag';
import {isFeedAIRepairVideoUser} from '@/utils/getFeedFlag';
import {getEntryFields} from '@/utils/tableList/filters';
import {useControl} from '@/hooks/externalControl';
import {saveMixedCustomColumns, fetchMixedCusomColumns} from '@/api/feed/reportConfig';
import {useURLQuery} from '@/hooks/tableList/query';
import {FEED_PROJECT_IDEA_TYPE} from '@/interface/aixProject/feedProject';
import {PortalEnum} from '@/config/feed/portal';
import {Calendar} from '../../../common/Calendar';
import SearchBox from '../../../common/SearchBox';
import {
    CreateCreativeChatEntry, CreateCreativeEntryProps,
    CreateCreativeMultiEntryProps, CreateFeedCreativeMultiEntry,
} from '../slots/CreativeNewEntry';
import AggregationListSelection from '../../../common/aggregative/AggregationListSelection';
import {AggrListType} from '../../../common/aggregative/tabs';
import {tableFieldsMap} from './tableList/tableConfig';
import {AiRepairTipMini} from './tableList/components/aiRepairTip';
import TableList from './tableList';

interface CreativeListContainerProps extends CreateCreativeEntryProps, CreateCreativeMultiEntryProps {
    initialFilters?: MaterialList.FilterItem[];
}
function FeedCreativeListContainer(
    {
        initialFilters,
        linkToCreateCreativeInChat,
        onClickCreateCreativeMenu,
    }: CreativeListContainerProps,
    ref: Ref<unknown>
) {
    const [columnConfiguration, modConfiguration, _control, updateConfiguration] = useMaterialListConfiguration(
        FEED_CREATIVE_LIST_REPORT_TYPE,
        {
            fetchMaterialConfigApi: fetchMixedCusomColumns,
            modMaterialConfigApi: saveMixedCustomColumns,
            withNewCategory: true,
        }
    );
    const [tableRows, setTableRows] = useState<Array<Record<string, any>>>([]);
    const [{adgroupId}] = useURLQuery();

    const [AggregationListSelectionDialog, {open: openAggreDialog}] = useControl(AggregationListSelection);
    const creativeListRef = useRef();
    useImperativeHandle(ref, () => ({
        openAggreDialog,
        ...(creativeListRef.current || {}),
    }));

    const {
        filters,
        columns,
        handleColumnAction,
        filterMethods,
        getFilterContentByField,
        getFilterDropdownByField,
    } = useColumns({columnConfiguration, tableFieldsMap, extraConfig: {initialFilters, enableParamsToQuery: true}});

    const filterListProps = {
        filters,
        deleteFilterByIndex: filterMethods.deleteFilterByIndex,
        changeFilterByIndex: filterMethods.changeFilterByIndex,
        getFilterContentByField,
    };

    // 单元里有程序化创意则不能再新建创意
    const disableAddCreative = adgroupId && tableRows.length
        && tableRows[0].ideatype === FEED_PROJECT_IDEA_TYPE.PROGRAM;

    const onSearch = useCallback(
        (value?: string, {operator}: {operator?: string} = {}) => {
            if (!value) {
                return;
            }
            filterMethods.changeFilterByField(
                'ideaname',
                {
                    values: [value],
                    operator: operator || defaultOperatorConfig.string[0].value,
                }
            );
        },
        [filterMethods.changeFilterByField]
    );

    const userId = getUserId();
    const sharedData = {
        userId,
        product: 'fd',
    };

    const onViewAiRepairCreative = useCallback((checked: boolean) => {
        if (checked) {
            filterMethods.updateOrAddFilterByField(
                'ideastat',
                // @ts-ignore
                {
                    values: [statusKeyMap.ONLY_DISPLAY_REPAIR_NO_PASS, statusKeyMap.ONLY_DISPLAY_REPAIR_PART_VALID],
                }
            );
        }
        else {
            filterMethods.deleteFilterByField('ideastat');
        }
    }, [columns, filters, filterMethods]);
    return (
        // 异步任务使用SharedInfoContext
        <SharedInfoContext.Provider value={sharedData}>
            <FeedMaterialTabs currentPageType={PageType.CreativeTextList} onAdd={openAggreDialog} />
            {isFeedAIRepairVideoUser() && createPortal(
                <AiRepairTipMini onViewAiRepairCreative={onViewAiRepairCreative} />,
                document.getElementById(PortalEnum.HorizontalNavSubMenuRight) || document.body
            )}
            <div className="manage-center-creative-text-list-header">
                <div>
                    {
                        isFeedPromotionMainUser()
                            ? (
                                <CreateFeedCreativeMultiEntry
                                    onClickCreateCreativeMenu={onClickCreateCreativeMenu}
                                    disabled={disableAddCreative}
                                />
                            )
                            : <CreateCreativeChatEntry linkToCreateCreativeInChat={linkToCreateCreativeInChat} />
                    }

                    {/* 日期区间 */}
                    <Calendar />

                    {/* 搜索框 */}
                    <SearchBox fieldName="创意文案" onSearch={onSearch} width={190} />
                    <FilterEntry
                        useFeedApi
                        materialLevel={[MATERIAL_LEVEL.IDEA_LEVEL, FeedMaterialLevel.IDEA_LEVEL]}
                        filterFields={getEntryFields(columnConfiguration.columnConfigs)}
                        columnConfigs={columnConfiguration.columnConfigs}
                        replaceFilters={filterMethods.replaceFilters}
                        changeFilterByField={filterMethods.changeFilterByField}
                        getFilterDropdownByField={getFilterDropdownByField}
                    />
                </div>
                <div id={PortalEnum.CreativeListOperationBar} />
            </div>
            <FilterList {...filterListProps}>
                <FilterListOperations
                    materialLevel={MATERIAL_LEVEL.IDEA_LEVEL}
                    filters={filters}
                    resetFilters={filterMethods.resetFilters}
                    addFilterTemplate={addFeedFilterTemplate}
                />
            </FilterList>
            <TableList
                reportType={FEED_CREATIVE_LIST_REPORT_TYPE}
                modConfiguration={modConfiguration}
                updateConfiguration={updateConfiguration}
                filters={filters}
                columns={columns}
                handleColumnAction={handleColumnAction}
                columnConfiguration={columnConfiguration}
                onSearch={onSearch}
                ref={creativeListRef}
                onRowsChange={setTableRows}
            />
            <AggregationListSelectionDialog type={AggrListType.creatives} />
        </SharedInfoContext.Provider>
    );
}

export default forwardRef(FeedCreativeListContainer);
