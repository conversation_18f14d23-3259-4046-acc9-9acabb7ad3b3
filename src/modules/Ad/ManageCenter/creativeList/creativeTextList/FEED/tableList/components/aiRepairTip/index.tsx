import {useCallback} from 'react';
import {IconBellSolid, IconLightningSolid, IconBulbTrendSolid, IconTrendReportSolid} from 'dls-icons-react';
import {Switch} from '@baidu/one-ui';
import AIFixTip from '@/styles/assets/aiFixTip.svg?react';
import {PRODUCT} from '@/dicts/campaign';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {makeLogger} from '@/modules/Ad/ManageCenter/common/utils';
import './index.global.less';

const FIELD = levelTypeMapForLogOrSwr.manageCreativeTextList;
const sendLog = makeLogger({level: FIELD, source: 'inline', 'extra_params': PRODUCT.FEED});

interface Props {
    onViewAiRepairCreative: (checked: boolean) => void;
}

export function AiRepairTipMini({onViewAiRepairCreative}: Props) {
    const onChecked = useCallback((val: boolean) => {
        sendLog({event: 'click_ai_repair_creative_switch'});
        onViewAiRepairCreative(val);
    }, [onViewAiRepairCreative]);

    return (
        <div className="feed-creative-ai-repair-tip-mini">
            <AIFixTip />
            <span className="feed-creative-ai-repair-tip-mini-title">建议使用:</span>
            <span className="feed-creative-ai-repair-tip-mini-important">AI拒审修复</span>
            已对广告拒审素材进行修复，过审率高。仅展示可AI一键修复创意
            <Switch size="small" onChange={onChecked} />
        </div>
    );
}

export default function AIRepairTip({onViewAiRepairCreative}: Props) {
    const onChecked = useCallback((val: boolean) => {
        sendLog({event: 'click_ai_repair_creative_switch'});
        onViewAiRepairCreative(val);
    }, [onViewAiRepairCreative]);

    return (
        <div className="feed-creative-ai-repair-tip-container">
            <div className="tip-content-wrapper">
                <div className="tip-bell-icon">
                    <IconBellSolid />
                </div>
                <div className="tip-content">
                    <div className="first-line">
                        <span className="title">AI一键修复全新上线：快速修复拒审素材</span>
                        <span className="tag">
                            <IconLightningSolid />
                            时间更短
                        </span>
                        <span className="tag">
                            <IconBulbTrendSolid />
                            成功率更高
                        </span>
                        <span className="tag">
                            <IconTrendReportSolid />
                            质量更优
                        </span>
                    </div>
                    <div className="second-line">
                        若创意状态列可见「AI一键修复」入口，点击即可查看修复建议详情，您可在状态筛选中筛选「仅展示可一键修复创意」，即可查看全部可修复创意。
                    </div>
                </div>
            </div>
            <div className="tip-action">
                <Switch onChange={onChecked} />
                <span>仅展示可AI一键修复创意</span>
            </div>
        </div>
    );
}
