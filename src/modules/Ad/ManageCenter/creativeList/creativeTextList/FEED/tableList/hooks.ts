import {useEffect, useCallback, useMemo} from 'react';
import {useRequest} from 'huse';
import {useAsyncTaskBanner} from 'commonLibs/hooks/asyncTask';
// import {createError} from '@/utils/error';
import {useRequestCallback} from '@huse/request';
// 注：这里为啥不用第9行cangjie 的createError 呢？
// 因为createError，包含了errorCode的错误码和错误信息映射关系。
// errorCode 的注册在 feedCommonLibs/qinggeProvider 已经进行了 errorMessageGetters的注册
import {createError, ErrorFromEnum} from '@/utils/error';
import {useKeyOrientedArray} from '@/hooks/collection/array';
import {useQueryTableSort, OneUiSortParams} from '@/hooks/tableList/sorter';
import {RowSelection, RowSelectionMethodsProps, useRowSelection} from '@/hooks/selection';
import {Pagination, useQueryTablePagination} from '@/hooks/pagination';
import {
    fetchFeedCreativeList,
    normalizeFeedCreativeItem,
    batchCopyFeedCreatives,
    updateFeedCreative,
    batchDeleteFeedCreatives,
    inlineCopyFeedCreative,
} from '@/api/manage/creative';
import {inlineUpdateFeedCreative, inlineDeleteFeedCreative} from '@/api/manage/creative';
import {Sorter} from '@/interface/report';
import {MaterialList, AsyncTasks} from '@/interface/tableList';
import {makeLogger, getSaveEventByParams} from '@/modules/Ad/ManageCenter/common/utils';
import {PRODUCT} from '@/dicts/campaign';
import {FeedIdType} from '@/dicts/idType';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {useFeedConfByQuery, useFeedFilteredByQuery} from '@/hooks/tableList/query';
import {PageType} from '@/dicts/pageType';
import {DateRange} from '../../../../context';
import {transInlineKey} from '../utils';

const FIELD = levelTypeMapForLogOrSwr.manageCreativeTextList;
const sendLog = makeLogger({level: FIELD, 'extra_params': PRODUCT.FEED});
const initialListData = {listData: [], sum: {}, totalCount: 0};
export const getCreativeRowKey = (record: any) => record.ideaid;

const IdeaAsyncTaskIdType = [FeedIdType.IDEA_LEVEL, FeedIdType.IDEA_IMG_LEVEL];

interface IProps {
    filters: MaterialList.FilterItem[];
    date: DateRange;
    customFields: string[];
}
export type IReturn = [
    {
        pending: boolean;
        error: Error | undefined;
        sorter: Sorter;
        pagination: Pagination;
        data: {summary: Record<string, any>, rows: Array<Record<string, any>>, totalCount: number};
        selection: RowSelection;
        asyncTasks: AsyncTasks;
    },
    {
        setPageNo: (pageNo: number) => void;
        setPageSize: (pageSize: number) => void;
        refresh: () => void;
        getCreativeById: (key: string) => Record<string, any> | undefined;
        getCreativesByKeys: (keys: Array<string|number>) => Array<(Record<string, any> | undefined)>;
        onSort: (sorter: OneUiSortParams) => void;
        selectionOperations: RowSelectionMethodsProps;
        inlineUpdateCreative: (createdId: number, values: any) => Promise<any>;
        inlineDeleteCreative: (createdId: number) => Promise<any>;
        batchUpdateCreatives: (values: any) => Promise<any[]>;
        inlineCopyCreative: (ideaId: number, values: any) => Promise<any[]>;
        batchCopyCreatives: (mtlIds: number[], values: any) => Promise<any[]>;
        batchDeleteCreatives: () => Promise<any[]>;
    }
];

export function useTableList({filters, date, customFields: fields}: IProps): IReturn {
    const [sorter, onSort] = useQueryTableSort();
    const [pagination, {setPageNo, setPageSize}] = useQueryTablePagination();
    const tabFilters = useFeedFilteredByQuery(PageType.CreativeTextList);
    const {projectId} = useFeedConfByQuery();
    const {
        data: {listData: rawRows_, totalCount, sum: summary = {}} = initialListData,
        pending,
        error,
        refresh,
    } = useRequest(
        fetchFeedCreativeList,
        {sorter, pagination, filters, timeRange: date, fields, tabFilters, projectId},
    );
    const rawRows = useMemo(() => rawRows_.map(normalizeFeedCreativeItem), [rawRows_]);

    const [rows, {
        updateByKey: updateCreativeById,
        getItemByKey: getCreativeById,
        set: setCreatives,
    }] = useKeyOrientedArray(rawRows, {getKey: getCreativeRowKey});

    useEffect(() => {
        setCreatives(rawRows);
    }, [rawRows]);

    const getCreativesByKeys = useCallback(
        (keys: Array<string|number>) => keys.map(key => rows.find(item => getCreativeRowKey(item) === key)),
        [rows]
    );

    const listIds = useMemo(() => rows.map(getCreativeRowKey), [rows]);
    const [selection, selectionOperations] = useRowSelection({ids: listIds, totalCount});
    const [asyncTasks, asyncTaskMethods] = useAsyncTaskBanner({
        levelIdType: IdeaAsyncTaskIdType
    });
    const {startQueryAsyncTask} = asyncTaskMethods;

    useEffect(
        () => {
            selectionOperations.resetRowSelection();
        },
        [filters, sorter, pagination.pageSize, pagination.pageNo]
    );

    // 行内操作 启停
    const inlineUpdateCreative = useCallback(
        async (creativeId: number, values: any) => {
            let result = null;
            try {
                const event = getSaveEventByParams(values, {aixProduct: PRODUCT.FEED});
                result = await inlineUpdateFeedCreative(creativeId, values);
                sendLog({event, source: 'inline'});
            }
            catch (error) {
                throw createError(error, {errorFrom: ErrorFromEnum.FEED});
            }
            const data = result?.[0];
            updateCreativeById(creativeId, transInlineKey(data));
            return data;
        },
        [updateCreativeById]
    );

    // 行内操作 删除
    const inlineDeleteCreative = useCallback(
        async (creativeId: number) => {
            try {
                sendLog({event: 'launch_delete', source: 'inline'});
                await inlineDeleteFeedCreative(creativeId);
                sendLog({event: 'delete', source: 'inline'});
            }
            catch (error) {
                throw createError(error, {errorFrom: ErrorFromEnum.FEED});
            }
            refresh();
        },
        [refresh]
    );

    const batchUpdateCreatives = useCallback(
        async (values: any, overrideParams?: Record<string, any>) => {
            let data = null;
            const selectionInfo = selectionOperations.getSelectedInfo();
            try {
                const event = getSaveEventByParams(values, {aixProduct: PRODUCT.FEED, isForm: true});
                data = await updateFeedCreative({selectionInfo, filters, timeRange: date}, values, overrideParams);
                sendLog({event, count: selectionInfo.selectedCount, source: 'operation_bar'});
            }
            catch (error) {
                throw createError(error, {errorFrom: ErrorFromEnum.FEED});
            }
            if (data.isAsyncTask) {
                startQueryAsyncTask(true);
                return [];
            }
            refresh();
            return data.dataList;
        },
        [refresh, filters, date, startQueryAsyncTask, selectionOperations.getSelectedInfo]
    );

    const batchDeleteCreatives = useCallback(
        async () => {
            let data = null;
            const selectionInfo = selectionOperations.getSelectedInfo();
            try {
                const baseParams = {count: selectionInfo.selectedCount, source: 'operation_bar'};
                sendLog({...baseParams, event: 'launch_delete'});
                data = await batchDeleteFeedCreatives({selectionInfo, filters, timeRange: date});
                sendLog({...baseParams, event: 'delete'});
            }
            catch (error) {
                throw createError(error, {errorFrom: ErrorFromEnum.FEED});
            }
            refresh();
            return data.dataList;
        },
        [refresh, filters, date, selectionOperations.getSelectedInfo]
    );

    const batchCopyCreatives = useCallback(
        async (mtlIds: number[], values: any) => {
            let data = null;
            const selectionInfo = selectionOperations.getSelectedInfo();
            try {
                const baseParams = {count: selectionInfo.selectedCount, source: 'operation_bar'};
                sendLog({...baseParams, event: 'launch_copy'});
                data = await batchCopyFeedCreatives({filters, timeRange: date, selectionInfo}, mtlIds, values);
                sendLog({...baseParams, event: 'copy'});
            }
            catch (error) {
                throw createError(error, {errorFrom: ErrorFromEnum.FEED});
            }
            if (data.isAsyncTask) {
                startQueryAsyncTask(true);
                return;
            }
            refresh();
            return data.dataList;
        },
        [filters, date, refresh, selectionOperations.getSelectedInfo]
    );

    const inlineCopyCreative = useCallback(
        async (ideaId: number, values: any) => {
            let data = null;
            try {
                sendLog({event: 'launch_copy', source: 'inline'});
                data = await inlineCopyFeedCreative(ideaId, values);
                sendLog({event: 'copy', source: 'inline'});
            }
            catch (error) {
                throw createError(error, {errorFrom: ErrorFromEnum.FEED});
            }
            refresh();
            return data.dataList;
        },
        [refresh]
    );

    return [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            sorter,
            pagination,
            selection,
            asyncTasks,
        },
        {
            setPageNo,
            setPageSize,
            refresh,
            getCreativeById,
            getCreativesByKeys,
            onSort,
            selectionOperations,
            inlineUpdateCreative,
            inlineDeleteCreative,
            batchUpdateCreatives,
            batchDeleteCreatives,
            batchCopyCreatives,
            inlineCopyCreative,
        },
    ];
}

export function useHybridRequest(task: (...params: any) => Promise<any>, params: any, options?: Record<string, any>) {
    const [request, result] = useRequestCallback(task, params, options);
    useEffect(() => request(), [request]);
    return [request, result];
}
