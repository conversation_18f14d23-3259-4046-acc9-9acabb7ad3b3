import {lazy} from 'react';

export const editorConfig = {
    title: {
        title: '修改创意标题', // 操作名称
        width: 680, // 如果需要自定义宽度
        batch: lazy(() => import('./editor/BatchModifyCreativeTitleEditor')),
    },
    url: {
        title: '修改URL追踪',
        width: 680,
        batch: lazy(() => import('./editor/UrlTraceEditor')),
    },
    copy: {
        title: '推广创意复制',
        width: 680,
        inline: lazy(() => import('./editor/copy/inline')),
        batch: lazy(() => import('./editor/copy/batch')),
    },
    livePreview: {
        title: '预览实况',
        width: 880,
        isCustomTitle: true,
        inline: lazy(() => import('./editor/LivePreview')),
    },
    materialPreview: {
        title: '预览素材',
        width: 680,
        program: lazy(() => import('./editor/MaterialPreviewEditor/ProgramMaterials')),
        custom: lazy(() => import('./editor/MaterialPreviewEditor/CustomMaterials')),
    },
    materialEdit: {
        title: '编辑素材',
        width: 790,
        program: lazy(() => import('./editor/MaterialEdit/program')),
        custom: lazy(() => import('./editor/MaterialEdit/custom')),
    },
    addCreative: {
        title: '新建创意',
        width: 660,
        batch: lazy(() => import('./editor/unitSelector')),
        isCustomTitle: true,
    },
    accountType: {
        title: '修改推广身份',
        width: 640,
        batch: lazy(() => import('./editor/AccountType')),
        isCustomTitle: true,
    },
    detail: {
        title: '程序化创意详情',
        width: 1080,
        inline: lazy(() => import('./editor/programCreativeDeatil')),
        isCustomTitle: true,
    },
};
