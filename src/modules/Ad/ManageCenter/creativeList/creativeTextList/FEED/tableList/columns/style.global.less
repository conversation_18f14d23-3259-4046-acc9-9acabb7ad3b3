.manage-center-feed-creative-list {
    .contain-shadow-material {
        background-color: #fdcccc;

        .fdcp {
            background-color: #fdcccc;
        }
    }

    .preview-material-info {
        display: flex;
        min-width: 286px;
    }

    .one-popover-medium .one-popover-inner-content {
        padding: 6px;
    }

    .one-popover-inner-content:has(.fdcp-vvideo) {
        padding: 16px;
    }

    .material-edit {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .desc-material {
            display: inline-block;
            width: 50px;
        }
    }

    .feed-inline-idea-material-edit {
        display: inline-block;
        transform: translate(-16px, -2px);
        width: 1px;
        height: 1px;
    }

    .manage-dashboard-feed_creative_list-table {
        .preview-material-info {
            .fdcp-hd-pic-sdk {
                width: 248px;
            }
        }
    }

    .material-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;

        .mini-item {
            cursor: default;
            display: flex;
            justify-content: space-around;
            align-items: center;

            .fe-idea-preview-root-max-zoom {
                zoom: 0.5 !important;
            }

            .fe-idea-preview-root {
                border: 1px solid #ebedf5;
                zoom: 0.2;
                pointer-events: none;
            }

            .fdcp-default-preview {
                width: 220px;
            }
        }

        .mini-item-title {
            margin-left: 8px;
        }
    }

    .preview-material-info.idea-compact {
        background: transparent;

        .material-item {
            flex: 1;
        }

        .column-cell-flex {
            width: auto;
        }
    }

    span.inline-operation-icon.button-icon,
    button.inline-operation-icon.button-icon {
        font-size: 12px;
        height: 14px;
        width: 14px;
    }

    span.inline-operation-icon.button-icon {
        height: 18px;

        > button {
            height: 14px;
            width: 14px;
        }
    }

    .feed-table-list-columns-operations {
        display: flex;
        gap: 4px;
    }
}

.feed-creative-list-preview-item {
    position: relative;

    .fe-idea-preview-root {
        display: flex;
        justify-content: center;
    }

    &-mask {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 9999;
        background: rgba(255, 255, 255, .9);

        .report-link {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            outline: none;
        }
    }
}
