@import 'src/styles/mixin/list.global.less';

.creative-list-operation-bar {
    .operation-bar-right {
        display: flex;
        align-items: center;
        gap: 8px;
    }
}

.manage-center-feed-creative-list {
    .common-table-columns();
    .common-manage-center-table-list-layout();
    .common-manage-center-batch-operation-bar();

    .creative-list-operation-bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: absolute;
        top: 105px;
        right: 24px;
        z-index: 2;

        .operation-bar-right {
            display: flex;
            align-items: center;
            gap: 8px;
        }
    }

    .name-column {
        width: 100%;

        & > span {
            text-overflow: ellipsis;
            overflow: hidden;
        }
    }

    .column-cell-flex {
        ul {
            list-style: none;
        }
    }

    &.has-filters {
        .common-manage-center-batch-operation-bar(-96px, 60px);
    }
}

.manage-center-feed-creative-list.has-ai-repair-tip {
    .creative-list-operation-bar {
        top: 193px;
    }
}

.manage-center-feed-creative-list.manage-center-feed-creative-list_tabs {
    .creative-list-operation-bar {
        top: 146px;
        right: 24px;
    }
}

.manage-center-feed-creative-list.has-ai-repair-tip.manage-center-feed-creative-list_tabs {
    .creative-list-operation-bar {
        top: 234px;
    }
}
