@import 'src/styles/mixin/list.global.less';

.feed-creative-ai-repair-tip-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 10px 16px;
    border-radius: 8px;
    border: solid 1px rgba(86, 138, 246, 0.08);
    background: linear-gradient(271deg, #F6F3FF -0.08%, #F2FDFF 99.45%);

    .tip-bell-icon {
        display: inline-block;
        color: #06F;
        font-size: 32px;
    }

    .tip-content-wrapper {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .tip-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
        font-size: 12px;

        .first-line {
            display: flex;
            align-items: center;
            gap: 16px;

            .title {
                font-size: 14px;
                font-weight: 500;
                color: #0E0F11;
            }

            .tag {
                display: flex;
                align-items: center;
                gap: 4px;
                padding: 4px 8px;
                border-radius: 50px;
                color: rgba(46, 52, 64, 0.9);
                background-color: #fff;

                svg {
                    color: #3F9EFD;
                }
            }
        }

        .second-line {
            color: #545B66;
        }
    }

    .tip-action {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #545B66;

        > span {
            margin-left: 4px;
        }
    }
}

.feed-creative-ai-repair-tip-mini {
    display: flex;
    gap: 4px;
    height: 28px;
    align-items: center;
    background-color: #e8f4ff;
    border-radius: @dls-border-radius-2;
    padding: 0 6px;

    &-title {
        font-family: FZPinShangHeiS-B-GB;
        color: @dls-color-brand-7;
    }

    &-important {
        font-weight: @dls-font-weight-3;
        margin-right: 4px;
    }
}
