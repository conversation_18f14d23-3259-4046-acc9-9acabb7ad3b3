/*
 * @Description: feed 创意列表 - 素材数量（本期暂不支持编辑）
 * @Author: lv<PERSON><PERSON>(<EMAIL>)
 * @Date: 2024-09-18 20:14:23
 * @Last Modified by: lv<PERSON><PERSON>@baidu.com
 * @Last Modified time: 2025-01-14 10:33:22
 */

import {get} from 'lodash-es';
import {Button, Tooltip} from '@baidu/one-ui';
import {
    isInteraction,
    isGestrurePicInteraction,
    isGestrureVideoInteraction,
} from 'feedCommonLibs/config/idea';
import {fullCheckSupportEdit} from 'feedManageCenter/utils/editMaterial';
import {IconEye, IconEdit} from 'dls-icons-react';
import {
    isSupportFeedHudongImage as getHD2,
    getFeedProgFit,
    isSupportFeedVerticalImage as getVerticalCoverFlag,
} from '@/utils/getFeedFlag';
import {IDEA_TYPE} from '@/config/feed';
import {withSumCell} from '@/utils/handleSummary';
import {createCustomRender} from '@/utils/render';
import {sendMonitor} from '@/utils/logger';
import './style.global.less';

const interactionFields: string[] = ['lensStretchImages', 'colorRenderImages', 'wordAppearImages'];
const videosFields: string[] = ['horizontalVideos', 'verticalVideos'];
const largePicFields: string[] = ['largePic', 'pictureLargeLianmeng'];
const profitFields: string[] = ['oriSinglePic', 'oriColorRenderImages', 'oriLensStretchImages', 'oriWordAppearImages'];

interface Elements {
    [key: string]: any;
    singlePic?: any;
    largePic?: any;
    verticalLargePic?: any;
    horizontalVideos?: any;
    verticalVideos?: any;
    package3Pic?: any;
    activeElements?: any;
    profitSingleCount?: any;
  }

const COUNT_MAP = {
    profitSingleCount: '单图',
    singlePicCount: getVerticalCoverFlag() ? '横版小图' : '小图',
    largePicCount: getVerticalCoverFlag() ? '横版大图' : '大图',
    videoCount: '视频',
    package3PicCount: '三图',
    verticalLargePicCount: '竖版图片',
    interactionCount: '互动图',
};

// from fc-fe/feed packages/manage-center/src/utils/editMaterial.ts
export function getProgramDesCountInfo(elements: Elements): string {
    if (!elements) {
        return '';
    }
    // eslint-disable-next-line complexity
    const countInfo = Object.keys(elements).reduce((sum, cur) => {
        // 在getFeedProgFit()名单里，只有单图。三图。视频，其他隐藏
        if (!getFeedProgFit() && cur === 'singlePic') {
            sum.singlePicCount += elements[cur]?.length;
        }
        if (!getFeedProgFit() && largePicFields.includes(cur)) {
            sum.largePicCount += elements[cur]?.length;
        }
        if (getFeedProgFit() && profitFields.includes(cur)) {
            sum.profitSingleCount += elements[cur]?.length;
        }
        if (cur === 'package3Pic') {
            sum.package3PicCount += elements[cur]?.length;
        }
        if (cur === 'verticalLargePic') {
            sum.verticalLargePicCount += elements[cur]?.length;
        }
        if (videosFields.includes(cur)) {
            sum.videoCount += elements[cur]?.length;
        }
        // 在getHD2()名单里，大图和视频tab下有互动图/视频
        if (getHD2() && (cur === 'activeElements')) {
            (get(elements, 'activeElements', [])).forEach(({realMaterialStyle}: any) => {
                // 普通互动图或者手势互动
                if (isInteraction(realMaterialStyle) || isGestrurePicInteraction(realMaterialStyle)) {
                    sum.largePicCount++;
                }
                // 手势互动视频
                if (isGestrureVideoInteraction(realMaterialStyle)) {
                    sum.videoCount++;
                }
            });
        }
        // 不在getHD2()名单内，且不在getFeedProgFit()名单内，有互动图tab
        if (!getHD2() && !getFeedProgFit() && interactionFields.includes(cur)) {
            sum.interactionCount += elements[cur]?.length;
        }
        return sum;
    }, {
        largePicCount: 0, profitSingleCount: 0, package3PicCount: 0, singlePicCount: 0,
        pkgPicCount: 0, videoCount: 0, interactionCount: 0, verticalLargePicCount: 0,
    });
    const nonZeroEntries = Object.entries(countInfo).filter(([, value]) => value !== 0);
    return nonZeroEntries.map(([key, value]) => `${value}个${COUNT_MAP[key as keyof typeof COUNT_MAP]}`).join(' ');
}

function DescMaterial({record, isProgram}: {record: Record<string, any>, isProgram: boolean}) {
    const materialInfo = record.ideamaterial || {};
    const {videoid, activeId, pictures = []} = materialInfo;
    // 程序化
    if (isProgram) {
        return (
            <span className="desc-material">
                {/* 如果是程序化适配名单内的存量会存在无的情况，以-兜底 */}
                {getProgramDesCountInfo(materialInfo?.elements) || '-'}
            </span>
        );
    }
    // 自定义
    const hasVideo = !!videoid;
    const picShow = activeId ? '1张图片' : pictures.length + '张图片';
    const materialShow = hasVideo ? ' 1个视频' : picShow;
    return (
        <span className="desc-material">
            {materialShow}
        </span>
    );
}

export function CreativeMaterial(
    {record, isHideMaterialCount = false, trigger}:
    {record: Record<string, any>, isHideMaterialCount?: boolean, trigger?: any}
) {
    const {ideatype, creativeFeedId} = record;
    const isProgram = ideatype === IDEA_TYPE.PROGRAM;

    const onClick = () => {
        const type = isProgram ? 'program' : 'custom';
        trigger(
            'openEditor', type, 'materialPreview', creativeFeedId,
            {containerType: 'dialog', hideDefaultFooter: true}
        );
        sendMonitor('click', {
            level: 'feed_idea_list',
            field: 'idea_list',
            'item': 'preview',
        });
    };

    const supportEdit = fullCheckSupportEdit(record, {isProgFit: getFeedProgFit()});
    const editProps = {
        type: 'text-strong',
        size: 'small',
        icon: IconEdit,
        onClick: () => {
            const type = isProgram ? 'program' : 'custom';
            trigger(
                'openEditor', type, 'materialEdit', creativeFeedId,
                {containerType: 'dialog'}
            );
        }
    } as any;

    return (
        <div className="column-cell-flex">
            <span className="material-edit">
                {!isHideMaterialCount && <DescMaterial isProgram={isProgram} record={record} />}
                {supportEdit ? (
                    <Tooltip title="快捷编辑素材">
                        <Button {...editProps} />
                    </Tooltip>
                ) : null}
                {/* 不支持商品目录 */}
                <Tooltip title="点击查看素材">
                    <Button
                        type="text-strong"
                        size="small"
                        icon={IconEye}
                        onClick={onClick}
                    />
                </Tooltip>
            </span>
        </div>
    );

}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (_, record) => (
            <CreativeMaterial record={record} trigger={trigger} />
        )
    )),
};
