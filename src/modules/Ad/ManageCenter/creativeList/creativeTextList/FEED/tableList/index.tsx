import {memo, useMemo, useCallback, useEffect, forwardRef, useImperativeHandle, lazy, useState} from 'react';
import {createPortal} from 'react-dom';
import classNames from 'classnames';
import queryString from 'query-string';
import {Pagination, Table, TableProps} from '@baidu/one-ui';
import AsyncBanner from 'commonLibs/asyncTaskBanner';
import {FeedProviderForQingge} from 'feedCommonLibs/qinggeProvider';
import {Empty} from '@/components/common/materialList/emptyText';
import {PRODUCT} from '@/dicts/campaign';
import {PageType} from '@/dicts/pageType';
import {PortalEnum} from '@/config/feed/portal';
import {useControl} from '@/hooks/externalControl';
import {useSortableColumns} from '@/hooks/tableList/columns';
import {toOneUIPaginationProps} from '@/hooks/pagination';
import {useRegister} from '@/hooks/tableList/register';
import {useFeedConfByQuery} from '@/hooks/tableList/query';
import {useAdRoute} from '@/modules/Ad/routes';
import {makeLogger} from '@/modules/Ad/ManageCenter/common/utils';
import {unshift} from '@/utils/array';
import getDepsFields from '@/utils/materialList/getDepsFields';
import {toOneUIRowSelectionProps} from '@/utils/selection';
import EditorContainer from '@/components/common/materialList/EditorContainer';
import {MaterialList} from '@/interface/tableList';
import {useGetTables} from '@/components/common/FeedMaterialTabs/table';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {getUserId} from '@/utils';
import {isFeedAIRepairVideoUser} from '@/utils/getFeedFlag';
import {getFeedAdgroupList} from '@/api/manage/adgroup';
import globalData from '@/utils/globalData';
import {useManageCenterDateRange} from '../../../../context';
import {relatedFields, needReplaceColmns} from './tableConfig';
import {useTableList, getCreativeRowKey} from './hooks';
import OperationBar from './components/operationBar';
import {BatchOperation} from './components/batchOperation';
import {editorConfig} from './editorConfig';
import './index.global.less';


// 一定要懒加载，不然有可能出现组件内部小流量控制失败的问题，原因：轻舸外层会有对feed module 小流量的初始化（FeedProviderForQingge），懒加载确保初始化完毕。
const AuditDrawer = lazy(() => import('feedManageCenter/components/auditDrawer'));
const AiRepairDrawer = lazy(() => import('feedManageCenter/components/auditDrawer/aiRepair'));
const FIELD = levelTypeMapForLogOrSwr.manageCreativeTextList;
const sendLog = makeLogger({level: FIELD, source: 'inline', 'extra_params': PRODUCT.FEED});

interface FeedCreativeTableListProps extends Pick<
    MaterialList.useColumnsRes, 'columns' | 'handleColumnAction' | 'filters'
> {
    reportType: number;
    columnConfiguration: MaterialList.ColumnConfiguration;
    modConfiguration: MaterialList.ModConfiguration;
    updateConfiguration: MaterialList.ModConfiguration;
    onRowsChange: (rows: Array<Record<string, any>>) => void;
    onSearch: (value?: string, options?: {operator?: string}) => void;
}

function Main({
    reportType,
    filters,
    columns: columns_,
    handleColumnAction,
    updateConfiguration,
    modConfiguration,
    columnConfiguration,
    onRowsChange,
    onSearch,
}: FeedCreativeTableListProps, ref: any) {
    const {startDate, endDate} = useManageCenterDateRange();
    const {customColumns} = columnConfiguration;
    const customFields = useMemo(
        () => getDepsFields(customColumns, relatedFields, needReplaceColmns),
        [customColumns]
    );
    const tables = useGetTables({
        accountId: getUserId(), type: PageType.CreativeTextList,
    });

    const [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            sorter,
            pagination,
            selection,
            asyncTasks,
        },
        {
            setPageNo,
            setPageSize,
            refresh,
            onSort,
            selectionOperations,
            getCreativeById,
            getCreativesByKeys,
            inlineUpdateCreative,
            inlineCopyCreative,
            inlineDeleteCreative,
            batchCopyCreatives,
            batchDeleteCreatives,
            batchUpdateCreatives,
        },
    ] = useTableList({filters, date: {startDate, endDate}, customFields});

    const dataSource = useMemo(
        () => (rows.length ? unshift(rows, {...summary, totalCount}) : []),
        [rows, summary, totalCount]
    );

    useEffect(() => {
        const hasRepairSolution = isFeedAIRepairVideoUser()
            && rows?.some(item => item?.repairSolutions?.length > 0);
        if (hasRepairSolution) {
            sendLog({event: 'exposure_ai_repair_creative_btn'});
        }
    }, [rows]);
    const columns = useSortableColumns(columns_, sorter);

    const {onSelectChange, selectAll, resetRowSelection, selectRows, getSelectedInfo} = selectionOperations;
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange, selectAll},
            rows,
            {getId: getCreativeRowKey, multiPageSelection: false}
        ),
        [selection, onSelectChange, selectAll, rows],
    );

    const refreshAndResetRowSelection = useCallback(
        () => {
            refresh();
            resetRowSelection();
        },
        [refresh, resetRowSelection]
    );

    const {selectedCount} = getSelectedInfo();
    // 表格props
    const tableListProps = {
        className: 'manage-center-creative-table',
        rowKey: getCreativeRowKey,
        headerFixTop: selectedCount ? 36 : 0,
        size: 'small',
        autoHideOperation: 'filter',
        locale: {
            emptyText: (
                <Empty
                    hasFilter={!!filters.length}
                    error={error}
                    text="暂无数据"
                    name="创意"
                />
            ),
        } as TableProps['locale'],
        pagination: false, // 如果采用表格的分页器，数据要为前端分页，如果是后端排序，现在仅支持自己写pager
        updateWidthChange: true, // 注意此属性，如果为false，浏览器缩放时不会重新计算表格宽度
        loading: pending,
        onSortClick: onSort as TableProps['onSortClick'],
        columns,
        dataSource,
        rowSelection: rowSelectionProps,
        headerBottom: <AsyncBanner {...asyncTasks} refresh={refreshAndResetRowSelection} />,
    };
    // 分页器props
    const paginationProps = toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount);

    // 顶部header-自定义列 or 下载组件props
    const operationBarProps = {
        columnConfiguration,
        updateConfiguration,
        modConfiguration,
        summary,
        reportType,
        refresh,
        pending,
    };

    // 刷新操作
    const registerRefresh = useCallback(() => {
        return handleColumnAction('refresh', refresh);
    }, [handleColumnAction, refresh]);
    useRegister(registerRefresh);
    // 行内编辑启停
    useEffect(
        () => handleColumnAction('inlineUpdateCreative', inlineUpdateCreative),
        [handleColumnAction, inlineUpdateCreative]
    );
    // 行内编辑删除
    useEffect(
        () => handleColumnAction('inlineDeleteCreative', inlineDeleteCreative),
        [handleColumnAction, inlineDeleteCreative]
    );
    const [ControlledEditor, {openEditor}] = useControl(EditorContainer);
    useEffect(
        () => handleColumnAction('openEditor', openEditor),
        [handleColumnAction, openEditor]
    );

    useEffect(() => {
        onRowsChange(rows);
    }, [rows, onRowsChange]);

    // 审核详情弹窗
    const [CreativeAuditDrawer, {open: openAuditDrawer, close: closeAuditDrawer}] = useControl(AuditDrawer);
    const registerOpenAuditDrawer = useCallback(
        () => handleColumnAction('openAuditDrawer', openAuditDrawer),
        [handleColumnAction, openAuditDrawer]);
    useRegister(registerOpenAuditDrawer);

    // AI一键修复弹窗
    const [CreativeAiRepairDrawer, {open: openAiRepairDrawer, close: closeAiRepairDrawer}] = useControl(AiRepairDrawer);
    const registerOpenAiRepairDrawer = useCallback(
        () => handleColumnAction('openAiRepairDrawer', openAiRepairDrawer),
        [handleColumnAction, openAiRepairDrawer]);
    useRegister(registerOpenAiRepairDrawer);

    const editorSaveMethods = useMemo(
        () => ({
            batchCopyCreatives, batchDeleteCreatives, inlineUpdateCreative, inlineCopyCreative,
            batchUpdateCreatives,
        }),
        [batchCopyCreatives, batchDeleteCreatives, inlineUpdateCreative, inlineCopyCreative, batchUpdateCreatives]
    );
    const {adgroupId} = useFeedConfByQuery();
    const {linkTo} = useAdRoute();
    const onAddCreative = useCallback(async () => {
        if (adgroupId) {
            const {rows} = await getFeedAdgroupList({adgroupId});
            if (rows?.length) {
                const [selectedAdgroupInfo] = rows;
                linkTo(PageType.CreateFeedCreative, {
                    query: {
                        campaignId: selectedAdgroupInfo.planid,
                        adgroupId: selectedAdgroupInfo.unitid,
                        subject: selectedAdgroupInfo.subjecttype,
                        bsType: selectedAdgroupInfo.bstype,
                    },
                });
            }
        }
        openEditor('batch', 'addCreative');
    }, [openEditor, adgroupId]);

    useImperativeHandle(ref, () => ({onSearch, onAddCreative}));

    useEffect(() => {
        // 添加单元sug点击会跳转到本页面，如果url中有openDrawer=true，则打开新建单元抽屉
        const query = queryString.parse(location.search);
        if (query.openDrawer) {
            openEditor('batch', 'addCreative');
        }
    }, [openEditor]);

    // 批量操作器props
    const batchOperationProps = {
        selection,
        selectionOperations,
        totalCount,
        currentPageCount: rows.length,
        getCreativesByKeys,
        batchUpdateCreatives,
        refresh,
        batchDeleteCreatives,
        openEditor,
    };

    const dialogEditorProps = {
        rows,
        getSelectedInfo,
        getMaterialById: getCreativeById,
        refreshList: refresh,
        saveMethods: editorSaveMethods,
        resetRowSelection,
        selectRows,
        editorConfig,
        filters,
        sorter,
        targetNameField: 'ideaname',
        targetLevelName: '创意',
    };

    const cls = classNames({
        'manage-center-feed-creative-list': true,
        'manage-center-feed-creative-list_tabs': tables.length > 1,
        'has-ai-repair-tip': isFeedAIRepairVideoUser(),
        'has-filters': !!filters.length,
    });

    const jumpToEdit = useCallback(record => {
        const {
            planid,
            unitid,
            ideaid,
            subjecttype,
            bstype,
            projectid,
            ftyperange,
        } = record;
        linkTo(PageType.EditFeedCreative, {
            target: '_blank',
            query: {
                campaignId: planid,
                adgroupId: unitid,
                creativeId: ideaid,
                subject: subjecttype,
                bsType: bstype,
                ...(projectid && ftyperange ? {projectId: projectid} : {}),
            },
        });
    }, []);

    const onAcceptAiRepairAdvice = useCallback(async (...args) => {
        sendLog({event: 'accept_ai_repair_creative_solutions'});
        closeAuditDrawer();
        closeAiRepairDrawer();
        batchUpdateCreatives(...args);
    }, [closeAuditDrawer, batchUpdateCreatives]);
    const [feedBasicInfo] = useState(() => globalData.get('feedBasicInfo'));

    return (
        <FeedProviderForQingge basicInfo={feedBasicInfo}>
            <div className={cls}>
                {
                    createPortal(
                        <OperationBar {...operationBarProps} />,
                        document.getElementById(PortalEnum.CreativeListOperationBar) || document.body
                    )
                }
                {!!selection.value.length && <BatchOperation {...batchOperationProps} />}
                <div className="table-list-container">
                    <Table {...tableListProps} />
                    <Pagination className="table-pagination" {...paginationProps} />
                </div>
                {/* 批量编辑窗口用 */}
                {ControlledEditor && <ControlledEditor {...dialogEditorProps} />}
                {CreativeAuditDrawer && (
                    <CreativeAuditDrawer
                        canUseAiRepair={isFeedAIRepairVideoUser()}
                        jumpToEdit={jumpToEdit}
                        onAcceptAiRepairAdvice={onAcceptAiRepairAdvice}
                    />
                )}
                {CreativeAiRepairDrawer && (
                    <CreativeAiRepairDrawer
                        onAcceptAiRepairAdvice={onAcceptAiRepairAdvice}
                    />
                )}
            </div>
        </FeedProviderForQingge>
    );
}

export default memo(forwardRef(Main));
