import {SWRConfig} from 'swr/_internal';
import {useColumns} from '@/hooks/tableList/columns';
import {useMaterialListConfiguration} from '@/hooks/tableList/configuration';
import {MaterialList} from '@/interface/tableList';
import {FC_MARKET_POINT_LIST_REPORT_TYPE} from '@/dicts/reportType';
import {useGlobalSwrCacheContext} from '@/dbProvider';
import {tableFieldsMap} from './tableList/tableConfig';
import FcMarketPointTableList from './tableList';

interface FcMarketPointListContainerProps {
    initialFilters?: MaterialList.FilterItem[];
}
export function FcMarketPointListContainer({initialFilters}: FcMarketPointListContainerProps) {
    const [columnConfiguration, modConfiguration, control_, updateConfiguration] = useMaterialListConfiguration(
        FC_MARKET_POINT_LIST_REPORT_TYPE,
        {withNewCategory: true}
    );

    const {
        filters,
        columns,
        handleColumnAction,
        filterMethods,
        getFilterContentByField,
    } = useColumns({columnConfiguration, tableFieldsMap, extraConfig: {initialFilters, enableParamsToQuery: true}});

    const {dbProvider} = useGlobalSwrCacheContext();
    return (
        <>
            <SWRConfig value={{provider: dbProvider}}>
                <FcMarketPointTableList
                    reportType={FC_MARKET_POINT_LIST_REPORT_TYPE}
                    modConfiguration={modConfiguration}
                    filters={filters}
                    columns={columns}
                    handleColumnAction={handleColumnAction}
                    updateConfiguration={updateConfiguration}
                    columnConfiguration={columnConfiguration}
                    filterMethods={filterMethods}
                    getFilterContentByField={getFilterContentByField}
                />
            </SWRConfig>
        </>
    );
}
