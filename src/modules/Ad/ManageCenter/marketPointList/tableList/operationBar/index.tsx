import {useCallback} from 'react';
import {ButtonSize, <PERSON><PERSON><PERSON>, But<PERSON>} from '@baidu/one-ui';
import {IconAdjust, IconFitWidth} from 'dls-icons-react';
import {MaterialList} from '@/interface/tableList';
import RefreshOperation from '@/components/RefreshOperation';
import CustomFieldsTemplateSelector from '@/components/common/CustomFieldsSelector/CustomFieldsTemplateSelector';
import {
    deleteColumnTemplates,
    fetchTemplatesApi,
    markAsCurrentColumnTemplate,
    saveColumnTemplate,
} from '@/api/getReportTemplate';
import Download from '@/components/ChatCards/Chart/download';
import {getUserId} from '@/utils';
import {materialToken} from '@/config/tableList/config';
import {defaultOperatorConfig} from '@/config/tableList/defaultOperatorConfig';
import {useManageCenterDateRange} from '../../../context';
import {Calendar} from '../../../common/Calendar';
import SearchBox from '../../../common/SearchBox';
import './index.global.less';

interface CustomProps {
    columnConfiguration: MaterialList.ColumnConfiguration;
    summary: Record<string, any>;
    modConfiguration: MaterialList.ModConfiguration;
    resetColumnsWidth: () => void;
    pending: boolean;
    refresh: () => void;
    updateConfiguration: MaterialList.updateConfiguration;
    changeFilterByField: MaterialList.FilterMethods['changeFilterByField'];
}
export function OperationBar({
    columnConfiguration,
    modConfiguration,
    summary,
    reportType,
    resetColumnsWidth,
    pending,
    refresh,
    updateConfiguration,
    changeFilterByField,
}: CustomProps & {reportType: number}) {
    const {customColumns, defaultColumns, columnConfigs, columnCategories, allColumns} = columnConfiguration;

    const customFieldsProps = {
        modConfiguration,
        customColumns,
        summary,
        defaultColumns,
        columnConfigs,
        columnCategories,
        allColumns,
        size: 'medium' as ButtonSize,
        isUseColumnTemplate: true,
        reportType,
        updateConfiguration,
        templateId: columnConfiguration.templateId,
        fetchTemplatesApi,
        saveColumnTemplate,
        markAsCurrentColumnTemplate,
        deleteColumnTemplates,
        resetProps: {
            groupSidenav: true,
        },
        buttonProps: {
            showArrow: true,
            split: true,
            buttonProps: {
                type: 'normal',
            },
        },
        buttonTitle: <><IconAdjust /></>,
    };

    const {startDate, endDate} = useManageCenterDateRange();
    const downloadProps = {
        reportParams: {
            token: materialToken,
            reportType,
            startDate,
            endDate,
            timeUnit: 'SUMMARY',
            columns: customColumns,
            startRow: 0,
            rowCount: 100000,
            needSum: true,
            userIds: [getUserId()] as unknown as number[],
        },
        size: 'medium' as ButtonSize,
        downloadText: '',
    };

    const refreshOperationProps = {
        loading: pending,
        refreshList: refresh,
    };

    const onSearch = useCallback(
        (value?: string, {operator}: {operator?: string} = {}) => {
            if (!value) {
                return;
            }
            changeFilterByField(
                'wInfoNameStatus',
                {
                    values: [value],
                    operator: operator || defaultOperatorConfig.string[0].value,
                }
            );
        },
        [changeFilterByField]
    );

    return (
        <div className="market-point-list-operation-bar">
            <div className="operation-bar-left">
                {/* 日期区间 */}
                <Calendar />

                <SearchBox
                    fieldName="营销要点"
                    onSearch={onSearch}
                    width={190}
                />
            </div>
            <div className="operation-bar-right">
                <Tooltip title="刷新">
                    <div>
                        <RefreshOperation {...refreshOperationProps} />
                    </div>
                </Tooltip>
                <Tooltip title="重置列宽">
                    <div>
                        <Button onClick={resetColumnsWidth} size="medium" icon={<IconFitWidth />} />
                    </div>
                </Tooltip>
                <Tooltip title="自定义列">
                    <div>
                        <CustomFieldsTemplateSelector {...customFieldsProps} />
                    </div>
                </Tooltip>
                <Tooltip title="下载">
                    <div>
                        <Download {...downloadProps} />
                    </div>
                </Tooltip>
            </div>
        </div>
    );
}
