/* eslint-disable no-negated-condition */
import {memo, useMemo, useEffect} from 'react';
import {Pagination, Table, TableProps, Button} from '@baidu/one-ui';
import getDepsFields from '@/utils/materialList/getDepsFields';
import {useSortableColumns} from '@/hooks/tableList/columns';
import {toOneUIPaginationProps} from '@/hooks/pagination';
import MaterialTabs from '@/components/common/MaterialTabs';
import {unshift} from '@/utils/array';
import {Base} from '@/interface/base';
import {PageType} from '@/dicts/pageType';
import FilterList from '@/components/common/materialList/FilterList';
import {toOneUIRowSelectionProps} from '@/utils/selection';
import {MaterialList} from '@/interface/tableList';
import {Empty} from '@/components/common/materialList/emptyText';
import {useDraggableColumns} from '@/hooks/tableList/columns';
import {useObservedLocalStorage, getColumnWidthStorageKeyPath} from '@/hooks/storage';
import {scrollbarBottomFixedTableProps} from '@/config/tableList/config';
import {useManageCenterDateRange} from '../../context';
import {getMarketPointRowKey, useFcMarketPointList} from './materialQuery';
import {relatedFields, needReplaceFields} from './tableConfig';
import {OperationBar} from './operationBar';
import {BatchOperation} from './batchOperation';
import './index.global.less';

interface FcMarketPointTableListProps extends Pick<
    MaterialList.useColumnsRes,
    'columns' | 'handleColumnAction' | 'filters' | 'filterMethods' | 'getFilterContentByField'
> {
    reportType: number;
    columnConfiguration: MaterialList.ColumnConfiguration;
    modConfiguration: MaterialList.ModConfiguration;
    updateConfiguration: MaterialList.updateConfiguration;
}

function FcMarketPointTableList({
    reportType,
    filters,
    columns: columns_,
    handleColumnAction,
    modConfiguration,
    columnConfiguration,
    updateConfiguration,
    filterMethods,
    getFilterContentByField,
}: FcMarketPointTableListProps) {
    const {startDate, endDate} = useManageCenterDateRange();
    const {customColumns} = columnConfiguration;

    const customFields = useMemo(
        () => getDepsFields(customColumns, relatedFields, needReplaceFields),
        [customColumns]
    );

    const [
        {
            pending,
            error,
            data: {summary, rows, totalCount},
            sorter,
            pagination,
            selection,
            selectedInfo,
        },
        {
            setPageNo,
            setPageSize,
            refresh,
            onSort,
            selectionOperations,
            batchSetPermanentMarketPoints,
            batchDeleteMarketPoints,
        },
    ] = useFcMarketPointList({filters, date: {startDate, endDate}, customFields, reportType});

    const dataSource = useMemo(
        () => (rows.length ? unshift(rows, {...summary, totalCount}) : []),
        [rows, summary, totalCount]
    );

    const {onSelectChange, selectAll} = selectionOperations;
    const columns = useSortableColumns(columns_, sorter);
    const headBordered = useMemo(() => columns.some(col => col.children), [columns]);
    const paginationProps = toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount);
    const columnWidthStorageKeyPath = useMemo(() => getColumnWidthStorageKeyPath('markPointList'), []);
    const columnWidthStorage = useObservedLocalStorage(columnWidthStorageKeyPath, {});
    const [
        columnsWithCustomWidth,
        {onDragEnd, resetColumnsWidth},
    ] = useDraggableColumns(columns, {storage: columnWidthStorage as Base.LocalStorageTuple<MaterialList.Columns>});

    // to one-ui props
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange, selectAll},
            rows,
            {getId: getMarketPointRowKey, multiPageSelection: false}
        ),
        [selection, onSelectChange, selectAll, rows],
    );

    const tableListProps: TableProps = {
        rowKey: getMarketPointRowKey,
        ...scrollbarBottomFixedTableProps,
        headerFixTop: selection.value.length ? 32 : 0,
        size: 'small',
        autoHideOperation: 'filter',
        locale: {
            emptyText: (
                <Empty
                    hasFilter={!!filters.length}
                    error={error}
                    text="暂无数据"
                    name="营销要点"
                />
            ),
        } as TableProps['locale'],
        pagination: false, // 如果采用表格的分页器，数据要为前端分页，如果是后端排序，现在仅支持自己写pager
        updateWidthChange: true, // 注意此属性，如果为false，浏览器缩放时不会重新计算表格宽度
        className: 'manage-center-market-point-table',
        loading: pending,
        onSortClick: onSort as TableProps['onSortClick'],
        columns: columnsWithCustomWidth,
        dataSource,
        headBordered,
        onDragEnd,
        rowSelection: rowSelectionProps,
    };

    useEffect(
        () => handleColumnAction('refresh', refresh),
        [handleColumnAction, refresh]
    );

    useEffect(
        () => handleColumnAction('batchSetPermanentMarketPoints', batchSetPermanentMarketPoints),
        [handleColumnAction, batchSetPermanentMarketPoints]
    );

    const filterBarProps = {
        columnConfiguration,
        modConfiguration,
        summary,
        reportType,
        resetColumnsWidth,
        updateConfiguration,
        pending,
        refresh,
        changeFilterByField: filterMethods.changeFilterByField,
    };

    const batchEditProps = {
        selection,
        selectedInfo,
        selectionOperations,
        totalCount,
        currentPageCount: rows.length,
        batchSetPermanentMarketPoints,
        batchDeleteMarketPoints,
    };

    const filterListProps = {
        filters,
        deleteFilterByIndex: filterMethods.deleteFilterByIndex,
        changeFilterByIndex: filterMethods.changeFilterByIndex,
        getFilterContentByField,
    };

    return (
        <>
            <MaterialTabs currentPageType={PageType.MarketPointList} />
            <div className="manage-center-market-point-list">
                {selection.value.length ? (
                    <div className="market-point-list-batch-operation-bar">
                        <BatchOperation {...batchEditProps} />
                    </div>
                ) : (
                    <OperationBar {...filterBarProps} />
                )}
                <FilterList {...filterListProps}>
                    <Button type="text-strong" onClick={filterMethods.resetFilters}>清空</Button>
                </FilterList>
                <div className="table-list-container">
                    <Table {...tableListProps} />
                    <Pagination className="table-pagination" {...paginationProps} />
                </div>
            </div>
        </>
    );
}

export default memo(FcMarketPointTableList);
