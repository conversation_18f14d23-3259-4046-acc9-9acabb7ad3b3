import {memo, useMemo, useEffect} from 'react';
import {formatString} from '@/utils/string';
import {getFcUrl} from '@/utils/format/url';
import {getEnvPort, getUserId} from '@/utils';
import {useAdRoute} from '@/modules/Ad/routes';
import {PageType} from '@/dicts/pageType';
import {PostMessageEventType, PostMessageEvent} from '@/interface/iframe';
import MaterialTabs from '@/components/common/MaterialTabs';
import {useLevelInfo} from '@/hooks/levelInfo';
import SingletonIframe from '@/components/common/iframe/SingletonIframe';
import './style.global.less';

function formatQueryWordListUrl({campaignId, adgroupId, marketingTargetId, businessPointId}: {
    campaignId?: number;
    adgroupId?: number;
    marketingTargetId?: number;
    businessPointId?: number;
}) {
    let urlTpl = '';
    if (adgroupId) {
        // eslint-disable-next-line max-len
        urlTpl = '/fc/managecenter/qingge/querywords/user/${userId}/mt/${marketingTargetId}/campaign/${campaignId}/adgroup/${adgroupId}';
    }
    else if (campaignId) {
        urlTpl = '/fc/managecenter/qingge/querywords/user/${userId}/mt/${marketingTargetId}/campaign/${campaignId}';
    } else if (businessPointId) {
        urlTpl = '/fc/managecenter/qingge/querywords/user/${userId}/biz/${businessPointId}';
    }
    else {
        urlTpl = '/fc/managecenter/qingge/querywords/user/${userId}';
    }
    const url = formatString(
        getFcUrl(urlTpl),
        {userId: getUserId(), port: getEnvPort(), campaignId, adgroupId, businessPointId, marketingTargetId}
    );
    const {search, pathname, protocol, host} = new URL(url);
    return pathname + search;
}

function QueryWordList() {
    const {linkTo} = useAdRoute();
    const {data: {campaignId, adgroupId, marketingTargetId} = {}} = useLevelInfo();
    const url = useMemo(
        () => formatQueryWordListUrl({campaignId, adgroupId, marketingTargetId}),
        [campaignId, adgroupId, marketingTargetId]
    );

    useEffect(() => {
        function onMessage(event: PostMessageEvent<PostMessageEventType.linkToQueryWordList>) {
            const {type, payload} = event.data;
            if (type === PostMessageEventType.linkToQueryWordList) {
                if (payload.adgroupId) {
                    linkTo(PageType.QueryWordList, {
                        query: {
                            adgroupId: payload.adgroupId,
                            preLevelDataName: payload.preLevelDataName,
                        },
                    });
                }
                else if (payload.campaignId) {
                    linkTo(PageType.QueryWordList, {
                        query: {
                            campaignId: payload.campaignId,
                            preLevelDataName: payload.preLevelDataName,
                        },
                    });
                }
                else {
                    linkTo(PageType.QueryWordList);
                }
            }
        }
        window.addEventListener('message', onMessage);
        return () => window.removeEventListener('message', onMessage);
    }, [linkTo]);

    return (
        <>
            <MaterialTabs currentPageType={PageType.QueryWordList} />
            <div className="qingge-managecenter-queryword-list">
                <SingletonIframe
                    className="queryword-list-iframe"
                    iframePath={url}
                    style={{
                        width: '100%',
                        height: '100vh',
                    }}
                />
            </div>
        </>
    );
}
export default memo(QueryWordList);
