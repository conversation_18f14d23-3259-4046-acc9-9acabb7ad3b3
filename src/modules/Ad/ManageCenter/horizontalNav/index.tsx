import {<PERSON><PERSON>, Tabs} from '@baidu/one-ui';
import {useMemo} from 'react';
import {find, pick} from 'lodash-es';
import classNames from 'classnames';
import {PageType} from '@/dicts/pageType';
import {useFeedIdTypeByQuery, useURLQuery} from '@/hooks/tableList/query';
import {FC_MARKET_TARGET} from '@/dicts/marketingTarget';
import {BreadcrumbData} from '@/api/fcBreadcrumb';
import {useLevelInfo} from '@/hooks/levelInfo';
import {NavigatorLogType, sendNavigatorLog} from '@/components/Header/util';
import {useGlobalProductContext} from '@/hooks/productLine';
import {PLATFORM_ENUM} from '@/dicts';
import {PortalEnum} from '@/config/feed/portal';
import {isFeedCreativeListAdviceCard} from '@/utils/getFeedFlag';
import {useAdRoute} from '../../routes';
import {getFcManageCenterNavigations, getFeedManageCenterNavigations, NavigationConfigs} from '../config';
import {getCreativeComOptions} from '../creativeComponent/config';
import {ActionSuggest} from './components/ActionSuggest';
import {getParentKey} from './config';
import './style.global.less';

const TabPane = Tabs.TabPane;

const defaultLevelInfo = {} as BreadcrumbData;

function TabPaneRender(props) {
    const {
        item,
        adPageType,
        changeNavItem,
        product,
    } = props;
    if (item?.children) {
        return (
            <Tabs
                activeKey={adPageType}
                onChange={key => {
                    changeNavItem(key as PageType);
                    sendNavigatorLog({
                        field: key,
                        params: NavigatorLogType.HorizontalNav,
                        info: product,
                    });
                }}
                className="horizontal-sub-nav"
                style={{'--dls-tab-menu-padding': '0px'}}
                hideSpace
                type="simple"
            >
                {
                    item.children.map(child => (
                        <TabPane tab={child.label} key={child.key} />
                    ))
                }
            </Tabs>
        );
    }
    return null;
}

export default function HorizontalNav({isCommodity, navigations}: {
    isCommodity: boolean;
    navigations: NavigationConfigs[];
}) {
    const {adPageType, linkTo} = useAdRoute();
    const [query, {setQuery}] = useURLQuery();
    const {
        creativeLevel,
        creativeSubLevel,
    } = query as {creativeLevel?: string, creativeSubLevel?: string};

    const defaultCacaderValue = creativeLevel
        ? [creativeLevel, creativeSubLevel].filter(Boolean)
        : ['xst', 'phone'];

    const currentNavItemKey = useMemo(() => {
        return getParentKey(navigations, adPageType);
    }, [adPageType, navigations]);
    const changeNavItem = (key: string) => {
        linkTo(key, {
            query: pick(query, [
                'campaignId', 'adgroupId', 'projectId', 'preLevelDataName', 'businessPointId',
            ]),
        });
    };
    const {product} = useGlobalProductContext();

    const tabPaneProps = {
        item: find(navigations, {key: currentNavItemKey}),
        adPageType,
        product,
        changeNavItem,
    };

    const isFeedCreativeList = isFeedCreativeListAdviceCard()
        && currentNavItemKey === 'creative'
        && product === PLATFORM_ENUM.FEED;

    return (
        <div className="aix-manage-center-horizontal-nav">
            <Tabs
                onChange={(key: string) => {
                    const item = navigations.find(item => item.key === key);
                    const pageType = item?.children?.length ? item.children[0].key : key;
                    changeNavItem(pageType);
                    sendNavigatorLog({field: key, params: NavigatorLogType.HorizontalNav, info: product});
                }}
                activeKey={currentNavItemKey}
                hideSpace
                className="horizontal-nav"
                style={{'--dls-tab-menu-padding': '0px'}}
                size="large"
            >
                {
                    navigations.map(item => {
                        return (
                            <TabPane tab={item.label} key={item.key} />
                        );
                    })
                }
            </Tabs>
            {isFeedCreativeList && <ActionSuggest />}
            <div className={classNames('aix-manage-center-horizontal-nav-sub', {
                'has-children': !!tabPaneProps.item?.children?.length,
            })}
            >
                <TabPaneRender {...tabPaneProps} />
                <div id={PortalEnum.HorizontalNavSubMenuRight} />
            </div>
            {
                adPageType === PageType.CreativeComponent && !isCommodity && (
                    <Cascader
                        options={getCreativeComOptions()}
                        defaultValue={defaultCacaderValue}
                        onChange={value => {
                            setQuery({creativeLevel: value[0], creativeSubLevel: value[1]});
                        }}
                        className="creative-component-cascader"
                        size="small"
                        before="组件类型："
                        popupClassName="creative-component-cascader-menu"
                    />
                )
            }
        </div>

    );
}

export function FcHorizontalNav() {
    const {
        data: {
            marketingTargetId,
        } = defaultLevelInfo,
        idType,
    } = useLevelInfo();
    const isCommodity = marketingTargetId === FC_MARKET_TARGET.COMMODITY;
    const navigations = getFcManageCenterNavigations({isCommodity, idType});
    return (
        <HorizontalNav isCommodity={isCommodity} navigations={navigations} />
    );
}

export function FeedHorizontalNav() {
    const {idType} = useFeedIdTypeByQuery();
    const navigations = getFeedManageCenterNavigations({idType});
    return (
        <HorizontalNav navigations={navigations} />
    );
}
