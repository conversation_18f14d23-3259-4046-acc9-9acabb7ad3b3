@import 'src/styles/mixin/list.global.less';

.aix-manage-center-horizontal-nav {
    background-color: #fff;
    position: relative;

    .creative-component-cascader {
        position: absolute;
        left: 357px;
        top: 69px;
    }

    .horizontal-nav {
        > .one-ai-tabs-bar {
            margin-bottom: 16px;
        }
    }

    .horizontal-sub-nav {
        display: flex;
        height: 36px;
        align-items: center;

        .one-ai-tabs-title-medium {
            font-size: 14px;
        }
    }

    &-sub {
        display: flex;
        justify-content: space-between;
        align-items: center;

        &.has-children {
            margin-bottom: 16px;
        }
    }
}

.creative-component-cascader-menu {
    --dls-dropdown-max-display-items: 5;
}
