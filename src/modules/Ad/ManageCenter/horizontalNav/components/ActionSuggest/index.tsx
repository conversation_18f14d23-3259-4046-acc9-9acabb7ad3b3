import {Button, Carousel} from '@baidu/one-ui';
import {useBoolean, useElementResize, useRequest, useRequestCallback} from 'huse';
import {IconChevronDown, IconChevronUp} from 'dls-icons-react';
import {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import classNames from 'classnames';
import {request} from '@/utils/ajax';
import {useAdRoute} from '@/modules/Ad/routes';
import {PageType} from '@/dicts/pageType';
import {AdviceTypeEnum} from '@/dicts/optimizeAdvice';
import SuggestCard from '../SuggestCard';
import './index.global.less';
import {useOnceExpose} from './hooks';

function queryFeedOutline() {
    return request<any[]>('optcenter/GET/AdviceService/queryFeedOutline', {
        adviceKeys: [
            'modExpiredMaterialFeed',
            'addFeedCreativePictureConversion',
            'addFeedCreativeConversion',
            'addFeedCreativeVideoConversion',
            'removeDuplicateCreativeFeed',
        ],
        source: 251,
    });
}

export function ActionSuggest() {

    const [visible, {toggle}] = useBoolean(true);
    const [refresh, res] = useRequestCallback(queryFeedOutline, {});
    const afterApply = useCallback(() => {
        refresh();
    }, [refresh]);
    const {linkTo} = useAdRoute();
    const {pending, data = []} = res;
    const cardList = useMemo(() => {
        return data.slice().sort((a, b) => a.feedIdeaWebAdvicePriority - b.feedIdeaWebAdvicePriority);
    }, [data]);

    const {expose} = useOnceExpose();
    const carouselRef = useRef();
    const [curIndex, setCurIndex] = useState(0);
    const [containerSize, setContainerSize] = useState();

    const containerRef = useElementResize(setContainerSize);

    const hiddenNext = useMemo(() => {
        const cardWidth = 350;
        return (cardList.length - curIndex) * cardWidth <= containerSize?.offsetWidth;
    }, [curIndex, containerSize?.offsetWidth]);

    useEffect(() => {
        refresh();
    }, []);

    if (pending) {
        return null;
    }

    const headerDesc = (
        <>
            有
            <span className="header-desc-count">{data.length}</span>
            条行动建议待您查看！采纳建议提升创意质量，提升投放效果。
        </>
    );

    const showActions = data?.length > 0;

    if (!showActions) {
        return null;
    }

    return (
        <div className="feed-action-suggest">
            <div className="feed-action-suggest-header">
                <span className="header-left">
                    <span className="header-title">行动建议</span>
                    <span className="header-desc">
                        {headerDesc}
                    </span>
                    {showActions && (
                        <Button
                            onClick={() => linkTo(PageType.OptimizeAdvices, {
                                query: {adviceType: AdviceTypeEnum.IDEA},
                            })}
                            type="text-strong"
                            size="small"
                        >
                            查看更多
                        </Button>
                    )}
                </span>
                <span className="header-right">
                    {showActions && (
                        <Button type="text-strong" size="small" onClick={toggle}>
                            {
                                visible ? (
                                    <>
                                        收起详情
                                        <IconChevronUp />
                                    </>
                                )
                                    : (
                                        <>
                                            展开详情
                                            <IconChevronDown />
                                        </>
                                    )
                            }
                        </Button>
                    )}
                </span>
            </div>
            {visible && (
                <div className="feed-action-suggest-body" ref={containerRef}>
                    <Carousel
                        ref={carouselRef}
                        draggable={false}
                        slidesToScroll={1}
                        showButton
                        slidesToShow={1}
                        sliderMode="hide"
                        afterChange={setCurIndex}
                        variableWidth
                        centerPadding="0px"
                        nextButtonProps={{
                            className: classNames('feed-action-suggest-next-button', {
                                hidden: hiddenNext,
                            }),
                        }}
                        prevButtonProps={{className: 'feed-action-suggest-prev-button'}}
                    >
                        {
                            cardList.map(cardItem => {
                                return (
                                    <SuggestCard
                                        key={cardItem.adviceKey}
                                        {...cardItem}
                                        onExpose={expose}
                                        afterApply={afterApply}
                                    />
                                );
                            })
                        }
                    </Carousel>
                </div>
            )}
        </div>
    );
}
