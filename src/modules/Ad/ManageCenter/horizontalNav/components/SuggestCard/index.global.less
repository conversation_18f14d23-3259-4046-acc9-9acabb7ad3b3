@import 'src/styles/mixin/list.global.less';

.feed-suggest-card {
    display: flex;
    flex-direction: column;
    margin-right: 12px;
    width: 350px;
    height: 132px;
    padding: 12px;
    border-radius: @dls-border-radius-2;
    background-color: @dls-color-translucent-1;

    .card-header {
        display: flex;
        gap: @dls-padding-unit * 2;

        .tag-type {
            margin-top: 0;
        }
    }

    .card-title {
        font-weight: @dls-font-weight-2;
        line-height: @dls-padding-unit * 5;
    }

    .card-body {
        margin-top: 10px;
        flex: 1;

        .card-desc {
            .ellipsis-n-line(1);

            color: @dls-color-gray-8;
            font-size: @dls-font-size-0;
            line-height: @dls-padding-unit * 4;
        }

        .card-effect {
            display: flex;
            gap: 8px;
            margin-top: @dls-padding-unit * 3;
            color: #0E0F11;

            &-item {
                font-size: @dls-font-size-0;
                line-height: @dls-padding-unit * 4;
            }

            &-value {
                font-family: baidu number;
                font-size: @dls-font-size-4;
                line-height: @dls-padding-unit * 4;
            }

            &-icon {
                color: @dls-color-error-7;
                margin-left: 2px;
                transform: rotateZ(90deg);
            }

            &-spliter {
                width: 1px;
                background: linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(102, 146, 222, 0.15) 24.85%, rgba(102, 146, 222, 0.15) 74.65%, rgba(255, 255, 255, 0.15) 100%);
            }
        }
    }

    .card-footer {
        margin-top: auto;
        display: flex;
        gap: @dls-padding-unit * 3;
    }
}
