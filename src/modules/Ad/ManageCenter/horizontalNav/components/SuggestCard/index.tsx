import {isEmpty, find, get} from 'lodash-es';
import {<PERSON>ton, Dialog, Toast} from '@baidu/one-ui';
import {useInView} from 'react-intersection-observer';
import {useCallback, useEffect} from 'react';
import {IconUndoSolid} from 'dls-icons-react';
import Tag from '@/components/common/AdviceCard/components/Tag';
import {PageType} from '@/dicts/pageType';
import {useAdRoute} from '@/modules/Ad/routes';
import {AdviceOperateType, AdviceOperationsConfig, OperationMap, VarPlaceholder} from '@/dicts/optimizeAdvice';
import {sendMonitor} from '@/utils/logger';
import './index.global.less';
import {acceptOptimizeAdvice} from './api';

const unitMap = {
    week: '周',
    day: '天',
    hour: '小时',
    minute: '分钟',
};

function EffectItem(props) {
    const {desc, value, unit} = props;
    return (
        <span className="card-effect-item">
            {desc}
            <span className="card-effect-value">+{value}</span>
            {' '}/{unitMap[unit]}
            {value > 0 && <IconUndoSolid className="card-effect-icon" />}
        </span>
    );
}

const source = 251;

export const ACCEPT_RESP = {
    all: 'all',
    success: 'success',
    fail: 'fail',
};

enum AdviceEventEnum {
    accpet = 'advice_accept',
    expose = 'advice_expose',
}

export const acceptMonitor = (params, status, error = {}) => {
    const {adviceKey, adviceId, source, operationType, type} = params;
    const errorcode = get(error, 'errors.[0].code');

    const info = JSON.stringify({
        advice: adviceId,
        source,
        opttype: operationType,
        status: status === ACCEPT_RESP.success ? 0 : error.status,
        errorcode,
        host_app: 'qingge',
    });

    sendMonitor('action', {
        target: 'advice',
        type,
        item: operationType,
        level: 'creativeList',
        extra_params: info,
        value: adviceKey,
    });
};


export default function SuggestCard(props) {
    const {
        adviceKey,
        adviceId,
        adviceName,
        attributes,
        effect,
        feedIdeaWebAdviceOutline,
        afterApply,
        onExpose,
    } = props;
    const [ref, inView] = useInView({triggerOnce: true});
    const {improveClick = {}, improveConversion = {}} = effect?.effectMap || {};
    const {cardTag: [cardTagType]} = attributes;

    const {linkTo} = useAdRoute();

    const applySuggest = useCallback(() => {
        linkTo(PageType.OptimizeAdvices, {
            inheritQuery: false,
            query: {
                adviceKey,
                source,
            },
        });
    }, []);

    const config = AdviceOperationsConfig[adviceId as keyof typeof AdviceOperationsConfig];
    const {text: viewDetailText} = config[0];
    const {type: operationType, overridePath, modifyParams, text, remindMsg} = find(
        config,
        item => item.mold === AdviceOperateType.ACCEPT_ONCE
    ) || {};
    const [msg] = remindMsg || [];
    const {successMsg = ''} = find(config, item => item.type === operationType) || {};

    const totalCount = attributes.materialCount;
    const onQuickAcceptAdvice = useCallback(() => {
        let params = {
            adviceKey: adviceKey,
            operationType,
            source,
            overridePath,
            operationParam: {
                [OperationMap[operationType!]]: {
                    totalCount,
                },
            },
        };

        if (modifyParams) {
            params = modifyParams(params);
        }

        const monitorParams = {adviceId, source, operationType, type: AdviceEventEnum.accpet};
        acceptMonitor(monitorParams, ACCEPT_RESP.all);
        return Promise.resolve(acceptOptimizeAdvice(params)).then(res => {
            if (res.isAsyncTask) {
                Toast.info({
                    content: '一键采纳进行中，可前往后台任务记录查看进度',
                });
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
            else {
                const message = successMsg.replace(VarPlaceholder, totalCount);
                Toast.success({content: message});
                if (typeof afterApply === 'function') {
                    afterApply();
                }
            }
        }).catch(console.error);
    }, []);

    const dialogConfirm = useCallback(() => {
        Dialog.confirm({
            title: '确认操作',
            content: msg,
            onOk: onQuickAcceptAdvice,
        });
    }, [onQuickAcceptAdvice]);

    useEffect(() => {
        if (inView) {
            onExpose?.(
                adviceId,
                () => acceptMonitor({
                    adviceId,
                    source,
                    operationType,
                    type: AdviceEventEnum.expose,
                }, ACCEPT_RESP.all)
            );
        }
    }, [inView]);

    return (
        <div className="feed-suggest-card" ref={ref}>
            <div className="card-header">
                <Tag type={cardTagType} />
                <span className="card-title">{adviceName}</span>
            </div>
            <div className="card-body">
                <div className="card-desc" title={feedIdeaWebAdviceOutline}>{feedIdeaWebAdviceOutline}</div>
                <div className="card-effect">
                    {
                        !isEmpty(improveClick) && (
                            <EffectItem {...improveClick} />
                        )
                    }
                    <span className="card-effect-spliter" />
                    {
                        !isEmpty(improveConversion) && (
                            <EffectItem {...improveConversion} />
                        )
                    }
                </div>
            </div>
            <div className="card-footer">
                <Button type="text-strong" size="small" className="custom-one-btn" onClick={applySuggest}>
                    {viewDetailText}
                </Button>
                {
                    operationType && (
                        <Button
                            size="small"
                            type="text-strong"
                            className="custom-one-btn quick-accept-btn"
                            onClick={dialogConfirm}
                        >
                            {text}
                        </Button>
                    )
                }
            </div>
        </div>
    );
}
