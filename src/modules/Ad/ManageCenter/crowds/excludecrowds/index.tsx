import {memo} from 'react';
import SingletonIframe from '@/components/common/iframe/SingletonIframe';
import {fcCrowdListUrls} from '../config';
import './style.global.less';

function ExcludeCrowdsList() {

    return (
        <div className="qingge-managecenter-excludecrowds-list">
            <SingletonIframe
                iframePath={fcCrowdListUrls.excludeCrowds}
                autoShow
                style={{
                    width: '100%',
                    height: '100vh',
                }}
            />
        </div>
    );
}
export default memo(ExcludeCrowdsList);
