import {ButtonSize, Tooltip} from '@baidu/one-ui';
import {IconAdjust} from 'dls-icons-react';
import {MaterialList} from '@/interface/tableList';
import RefreshOperation from '@/components/RefreshOperation';
import Download from '@/components/ChatCards/Chart/download';
import {LEVEL_TYPE} from '@/dicts/level';
import {getUserId} from '@/utils';
import {materialToken} from '@/config/tableList/config';
import {
    deleteColumnTemplates,
} from '@/api/feed/reportConfig';
import CustomFieldsTemplateSelector from '@/components/common/CustomFieldsSelector/CustomFieldsTemplateSelector';
import {useFeedTemplate} from '@/hooks/feedReportConfig';
import {isFeedMaterialsHasMovedQingggeUser} from '@/utils/getFeedFlag';
import {AiTagEnum} from '@/dicts/project';
import {useManageCenterDateRange} from '../../../../context';

interface CustomProps {
    columnConfiguration: MaterialList.ColumnConfiguration;
    summary: Record<string, any>;
    modConfiguration: MaterialList.ModConfiguration;
    refresh: () => void;
    pending: boolean;
}
export function OperationBar(props: CustomProps & {reportType: number}) {
    const {
        columnConfiguration, modConfiguration, updateConfiguration, summary, reportType,
        pending, refresh,
    } = props;

    const {customColumns, defaultColumns, columnConfigs, columnCategories, allColumns} = columnConfiguration;

    const {fetchTemplatesApi, markAsCurrentColumnTemplate, saveColumnTemplate} = useFeedTemplate(reportType);

    const buttonProps = {
        type: 'normal',
        showArrow: false,
        split: false,
        buttonProps: {
            icon: <IconAdjust />,
        },
    };
    const customProps = {
        buttonProps,
        split: false,
        buttonTitle: null,
    };
    const isUseColumnTemplate = true; // 自定义字段模板
    const customFieldsProps = {
        ...customProps,
        isUseColumnTemplate,
        isShowTransSelect: false,
        resetProps: {
            groupSidenav: true,
        },
        ...(isUseColumnTemplate ? {
            reportType,
            showQuestionnaireWhenClose: true,
            updateConfiguration,
            templateId: columnConfiguration.templateId,
            fetchTemplatesApi,
            saveColumnTemplate,
            markAsCurrentColumnTemplate,
            deleteColumnTemplates,
        } : {}),
        key: reportType,
        modConfiguration,
        customColumns,
        summary,
        defaultColumns,
        columnConfigs,
        columnCategories,
        allColumns,
        size: 'medium' as ButtonSize,
    };

    const {startDate, endDate} = useManageCenterDateRange();
    const downloadProps = {
        reportParams: {
            token: materialToken,
            reportType,
            startDate,
            endDate,
            timeUnit: 'DAY',
            userIds: [getUserId() as string],
            startRow: 0,
            rowCount: 100000,
            needSum: true,
            columns: customColumns,
            filters: isFeedMaterialsHasMovedQingggeUser() ? []
                : [{column: 'aiTag', operator: 'IN', values: [AiTagEnum.ON]}],
        },
        level: LEVEL_TYPE.account,
        size: 'medium' as ButtonSize,
        downloadText: '下载',
    };
    const refreshOperationProps = {
        loading: pending,
        refreshList: refresh,
    };
    return (
        <div className="operation-bar feed-project-operation-bar">
            <div className="right">
                <Tooltip title="刷新">
                    <div>
                        <RefreshOperation {...refreshOperationProps} />
                    </div>
                </Tooltip>
                <Tooltip title="自定义列">
                    <div>
                        <CustomFieldsTemplateSelector {...customFieldsProps} />
                    </div>
                </Tooltip>
                <Tooltip title="下载">
                    <div>
                        <Download {...downloadProps} />
                    </div>
                </Tooltip>
            </div>
        </div>
    );
}
