import {identity} from 'lodash-es';
import {Tooltip} from '@baidu/one-ui';
import MARKET_TARGET, {mtTextMap} from 'commonLibs/config/marketTarget';
import {PRODUCT} from '@/dicts/campaign';
import {withSumAndEmptyExpanedCell} from '@/utils/handleSummary';
import {createCustomRender} from '@/utils/render';
import {
    FcOcpcBidTypeText, FcOcpcBidTypeEnum, FcBidTypeEnum, FcBidTypeText,
    FcDeepTransTypeModeEnum, FcDeepTransTypeModeText
} from '@/dicts/ocpc';
import {FcProjectModeType, fcProjectModeTypeText} from '@/dicts/project';
import {isAiAgentOpen} from '@/components/List/ProjectList/AiAgent/util';
import {FC_MARKET_TARGET, FC_SUB_MARKET_TARGET, fcSubMtTextMap} from '@/dicts/marketingTarget';
import {RELATED_PRODUCT_TYPE} from '@/dicts/relatedProductsType';
import {ProjectName} from '../../projectName';
import {getIsProjectFieldInvisible} from '../utils';
import operations from './columns/operations';
import sharedBudget from './columns/sharedBudget';
import transTypesColumn from './columns/transTypes';
import deepTransTypesColumn from './columns/deepTransTypes';
import ocpcBid from './columns/ocpcBid';
import ocpcDeepBid from './columns/ocpcDeepBid';
import deepTransTypeStatus from './columns/deepTransTypeStatus';
import campaignItems from './columns/campaignItems';
import liftBudgetStatus from './columns/liftBudget';
import targetRoiRatio from './columns/targetRoiRatio';
import {AgentCell} from './columns/agentCell';
import projectBudgetStatus from './columns/projectStatus';
import promotionScene from './columns/promotionScene';

export const tableFieldsMap = {
    projectName: {
        render: createCustomRender((configs, {trigger}) => withSumAndEmptyExpanedCell(
            (text, record) => (
                <ProjectName
                    projectId={record.projectId}
                    projectName={record.projectName}
                    aixProduct={PRODUCT.FC}
                    industrySolution={record.industrySolution}
                    isAiMax={isAiAgentOpen({
                        budgetType: record.sharedBudgetType,
                        smartLiftBudget: record.smartLiftBudget,
                        liftBudgetMode: record.liftBudgetMode,
                        aiMaxMarketingLevel: record.aiMaxPromotion?.level,
                        ocpcBidRatioType: record.ocpcBidRatioType,
                        projectSource: PRODUCT.FC,
                        fcProjectModeType: record.projectModeType,
                        smartBidCostControl: record.smartBidCostControl,
                        smartTargetingCostControl: record.smartTargetingCostControl,
                        smartInvalidClueControl: record.smartInvalidClueControl,
                        smartBaseAdjustSwitch: record.smartBaseAdjustSwitch,
                        smartBaseControlCostSwitch: record.smartBaseControlCostSwitch,
                        smartLiftBid: record.smartLiftBid,
                    })}
                    onSaveProject={(...args: any[]) => trigger('inlineSaveProject', ...args)}
                    projectType={record.projectType}
                    taskStatus={record.taskStatus}
                />
            ),
            {needSum: true})
        ),
        filters: {
            maxLine: 500,
        },
        config: {
            width: 160,
            minWidth: 94,
        },
    },
    projectId: {
        render: withSumAndEmptyExpanedCell(identity),
        filters: {
            filterType: 'string',
            operatorOptions: [
                {value: 'in', label: '包含'},
                {value: 'eq', label: '等于'},
            ],
            defaultOperatorValue: 'in',
        },
        config: {
            width: 104,
            minWidth: 94,
            align: 'left',
        },
    },
    operations,
    bindIds: campaignItems,
    marketingTargetId: {
        render: withSumAndEmptyExpanedCell(value => {
            return mtTextMap[value] || '-';
        }),
        filters: {
            filterType: 'enum',
            options: [
                MARKET_TARGET.CPQL,
                MARKET_TARGET.APP,
                FC_MARKET_TARGET.STORE,
                FC_MARKET_TARGET.NATIVE,
                FC_MARKET_TARGET.COMMODITY,
                FC_MARKET_TARGET.SHOP,
            ].map(value => ({
                value,
                label: mtTextMap[value],
            })),
        },
        config: {
            width: 94,
            align: 'left',
        },
    },
    subMarketingTargetId: {
        render: withSumAndEmptyExpanedCell((value, record) => {
            const invisible = getIsProjectFieldInvisible('subMarketingTargetId', record as any);
            if (invisible) {
                return (
                    <Tooltip title={invisible}>
                        <div className="column-cell-flex">-</div>
                    </Tooltip>
                );
            }
            return fcSubMtTextMap[value] || '-';
        }),
        filters: {
            filterType: 'enum',
            options: [
                FC_SUB_MARKET_TARGET.NORMAL,
                FC_SUB_MARKET_TARGET.LIVE,
                FC_SUB_MARKET_TARGET.DQA,
                FC_SUB_MARKET_TARGET.NOTE,
            ].map(value => ({
                value,
                label: fcSubMtTextMap[value],
            })),
        },
        config: {
            width: 94,
            align: 'left',
        },
    },
    sharedBudget, // todo zhangbo35
    projectModeType: {
        render: withSumAndEmptyExpanedCell((value: FcProjectModeType, record) => {
            const invisible = getIsProjectFieldInvisible('projectModeType', record as any);
            if (invisible) {
                return (
                    <Tooltip title={invisible}>
                        <div className="column-cell-flex">-</div>
                    </Tooltip>
                );
            }
            return fcProjectModeTypeText[value] || '-';
        }),
        filters: {
            filterType: 'enum',
            options: [FcProjectModeType.AIMAX, FcProjectModeType.CUSTOM].map(value => ({
                value,
                label: fcProjectModeTypeText[value],
            })),
        },
        config: {
            width: 94,
        },
    },
    bidType: {
        render: withSumAndEmptyExpanedCell((value: FcBidTypeEnum) => {
            return FcBidTypeText[value] || '-';
        }),
        filters: {
            filterType: 'enum',
            options: [FcBidTypeEnum.OCPC, FcBidTypeEnum.CPC].map(value => ({
                value,
                label: FcBidTypeText[value],
            })),
        },
        config: {
            width: 94,
        },
    },
    ocpcBidType: {
        render: withSumAndEmptyExpanedCell((value: FcOcpcBidTypeEnum) => {
            return FcOcpcBidTypeText[value] || '-';
        }),
        filters: {
            filterType: 'enum',
            options: [
                FcOcpcBidTypeEnum.CPC,
                FcOcpcBidTypeEnum.OCPC,
                FcOcpcBidTypeEnum.ECPC,
                FcOcpcBidTypeEnum.CVMAX,
                FcOcpcBidTypeEnum.ROI,
            ].map(value => ({
                value,
                label: FcOcpcBidTypeText[value],
            })),
        },
    },
    transTypes: transTypesColumn,
    assistTransTypes: deepTransTypesColumn,
    ocpcBid,
    ocpcDeepBid,
    deepTransTypeStatus,
    liftBudgetStatus,
    targetRoiRatio,
    projectAgentUrl: {
        render: createCustomRender((configs, {trigger}) => withSumAndEmptyExpanedCell(
            (value, record) => {
                return (
                    <AgentCell
                        record={record}
                        inlineUpdateAgent={(...args: any[]) => trigger('inlineUpdateAgent', ...args)}
                    />
                );
            },
            {needSum: false})
        ),
    },
    projectBudgetStatus,
    relatedProducts: {
        render: withSumAndEmptyExpanedCell((value, record) => {
            if (record.relatedProducts === RELATED_PRODUCT_TYPE.SET) {
                return '已设置';
            }
            else if (record.relatedProducts === RELATED_PRODUCT_TYPE.NO_SET) {
                return '未设置';
            }
            return '-';
        }),
        filters: {
            filterType: 'enum',
            options: [{
                value: RELATED_PRODUCT_TYPE.SET,
                label: '已设置',
            }, {
                value: RELATED_PRODUCT_TYPE.NO_SET,
                label: '未设置',
            }],
        },
    },
    promotionScene,
    deepTransTypeMode: {
        render: withSumAndEmptyExpanedCell((value: FcDeepTransTypeModeEnum) => {
            return FcDeepTransTypeModeText[value] || '-';
        }),
        filters: {
            filterType: 'enum',
            options: [
                FcDeepTransTypeModeEnum.OPTIMIZE_BEHAVIOR,
                FcDeepTransTypeModeEnum.OPTIMIZE_ROI
            ].map(value => ({
                value,
                label: FcDeepTransTypeModeText[value],
            })),
        },
    }
};

export const relatedFields = {
    projectName: [
        'projectName', 'projectId', 'bindIds', 'productCategoryTypeStatus', 'stableStatus',
        'marketingTargetId', 'projectModeType', 'productCategoryType', 'ocpcBidType', 'ocpcBid', 'liftBudget',
        'liftBudgetStatus', 'liftBudgetMode', 'liftBudgetSchedule', 'transManagerMode', 'targetPackageStatus',
        'projectApplicationType', 'projectApplicationSid',
        'sharedBudget', 'sharedBudgetType', 'budgetContinueDay', 'useSharedBudget', 'sharedBudgetStartTime',
        'aixProjectStatus', 'smartLiftBudget', 'aiMaxPromotion', 'useIndustryAIMaxPromotion', 'consumptionProgress',
        'transTypeMultiBidStatus', 'ocpcBidRatioType', 'bidType', 'deepTransTypeMode',
        'projectBudgetStatus', 'industrySolution', 'subMarketingTargetId', 'projectType', 'pause', 'taskStatus',
        'smartBidCostControl', 'smartTargetingCostControl', 'smartInvalidClueControl', 'smartBaseAdjustSwitch',
        'smartBaseControlCostSwitch', 'smartLiftBid',
    ],
    operations: ['addFrom', 'liftBudgetStatus'],
    transTypeStat: ['transTypeStat', 'transTypesShadow'],
    deepTransTypeStatus: ['deepTransTypeStatus', 'deepTypeStatDetail', 'deepTransTypeMode'],
    ocpcBid: ['ocpcDeepBid', 'suggestBid', 'ocpcBidType', 'transTypes'],
    ocpcDeepBid: [
        'ocpcBid', 'assistTransTypes', 'suggestDeepBid', 'deepTransTypeStatus', 'ocpcBidType',
        'deepTransTypeMode',
    ],
    ocpcConversions: ['equipmentType', 'cvSources', 'pcConversion', 'wiseConversion'],
    transAsset: ['assetType', 'transAssetInfo'],
    liftBudget: ['ocpcBidType', 'ocpcBid', 'sharedBudget'],
    consumptionProgress: ['cost'],
    relatedProducts: ['structuredContentIdStrs', 'structuredContentIds'],
};

export const needReplaceFields = {};

export const relatedDownloadFields = {};

export const needReplaceDownloadFields = {
    projectAgentUrl: [],
};
