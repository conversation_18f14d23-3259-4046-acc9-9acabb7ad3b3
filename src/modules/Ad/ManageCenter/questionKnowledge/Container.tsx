import {SWRConfig} from 'swr';
import {useColumns} from '@/hooks/tableList/columns';
import {useMaterialListConfiguration} from '@/hooks/tableList/configuration';
import {MaterialList} from '@/interface/tableList';
import {FC_QUESTION_KNOWLEDGE_LIST_REPORT_TYPE} from '@/dicts/reportType';
import {useGlobalSwrCacheContext} from '@/dbProvider';
import {tableFieldsMap} from './tableList/tableConfig';
import FcQuestionKnowledgeTableList from './tableList';

interface FcQuestionKnowledgeListContainerProps {
    initialFilters?: MaterialList.FilterItem[];
}
export function FcQuestionKnowledgeListContainer({initialFilters}: FcQuestionKnowledgeListContainerProps) {
    const [columnConfiguration, modConfiguration, control_, updateConfiguration] = useMaterialListConfiguration(
        FC_QUESTION_KNOWLEDGE_LIST_REPORT_TYPE,
        {withNewCategory: true}
    );

    const {
        filters,
        columns,
        handleColumnAction,
        filterMethods,
        getFilterContentByField,
        getFilterDropdownByField,
    } = useColumns({columnConfiguration, tableFieldsMap, extraConfig: {initialFilters, enableParamsToQuery: true}});

    const {dbProvider} = useGlobalSwrCacheContext();
    return (
        <>
            <SWRConfig value={{provider: dbProvider}}>
                <FcQuestionKnowledgeTableList
                    reportType={FC_QUESTION_KNOWLEDGE_LIST_REPORT_TYPE}
                    modConfiguration={modConfiguration}
                    filters={filters}
                    columns={columns}
                    handleColumnAction={handleColumnAction}
                    updateConfiguration={updateConfiguration}
                    columnConfiguration={columnConfiguration}
                    filterMethods={filterMethods}
                    getFilterContentByField={getFilterContentByField}
                    getFilterDropdownByField={getFilterDropdownByField}
                />
            </SWRConfig>
        </>
    );
}
