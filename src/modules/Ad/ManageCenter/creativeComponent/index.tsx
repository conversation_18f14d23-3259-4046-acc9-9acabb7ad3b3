import {Button, Tabs} from '@baidu/one-ui';
import {memo, useCallback, useMemo} from 'react';
import queryString from 'query-string';
import Tip from 'commonLibs/Tips';
import {useFilteredIdTypeByQuery, useURLQuery} from '@/hooks/tableList/query';
import {formatString} from '@/utils/string';
import {getFcUrl} from '@/utils/format/url';
import {getEnvPort, getUserId} from '@/utils';
import {appendQuery} from '@/utils/route';
import {PageType} from '@/dicts/pageType';
import ShieldContainer from '@/modules/Ad/ManageCenter/creativeList/components/ShieldContainer';
import {useControl} from '@/hooks/externalControl';
import HoverTip from '@/components/common/Tip/HoverTip';
import {FcIdType} from '@/dicts/idType';
import SingletonIframe from '@/components/common/iframe/SingletonIframe';
import {CREATIVE_COMP_IFRAME_DISABLE_LIST} from '@/components/common/iframe/config';
import {useAdRoute} from '../../routes';
import {getCreativeComOptions} from './config';
import './style.global.less';

function CreativeComponent() {
    const [query, {setQuery}] = useURLQuery();
    const {
        creativeLevel = 'xst',
        creativeSubLevel = 'phone',
    } = query as {creativeLevel: string, creativeSubLevel?: string};
    const {idType, id} = useFilteredIdTypeByQuery();
    // 如果url上存在面包屑信息 则展示商品样式列表
    // MRD: 商品样式本次新增至轻舸计划层级下钻选项中，点击后同时定位至创意组件（简单理解为账户层级的创意组件和dca计划的创意组件内容完全不一样）
    const isShowCommodityCreativity = idType === FcIdType.CAMPAIGN_LEVEL && id;

    const options = useMemo(() => getCreativeComOptions(), []);
    const [ControlledShield, {openShield}] = useControl(ShieldContainer);

    const subLevelOptions = useMemo(
        () => {
            const option = options.find(item => item.key === creativeLevel);
            if (option && 'children' in option) {
                return option.children;
            }
            return [];
        },
        [creativeLevel, options]
    );

    const url = useMemo(
        () => {
            const iframeUrl = options.find(item => item.key === creativeLevel)?.iframeUrl
                || subLevelOptions.find(item => item.key === creativeSubLevel)?.iframeUrl;
            const iframeUrlWithqg = getFcUrl(iframeUrl);
            const url = formatString(iframeUrlWithqg, {userId: getUserId(), port: getEnvPort()});
            const {search, pathname, protocol, host} = new URL(url);
            return appendQuery(
                protocol + '//' + host + pathname,
                {
                    ...queryString.parse(search),
                    in: 'iframe',
                    'host_app': 'qingge',
                }
            );
        },
        [creativeLevel, creativeSubLevel, options, subLevelOptions]
    );
    const commodityCreativityUrl = useMemo(
        () => appendQuery(
            getFcUrl('/fc/managecenter/qingge/commodityCreativity/user/${userId}/mt/1/campaign/${id}', {id: id}),
            {in: 'iframe', 'host_app': 'qingge'}
        ),
        [id]
    );
    const {linkTo} = useAdRoute();
    const linkToAnalyse = useCallback(() => {
        linkTo(PageType.Tools_StyleAnalyse);
    }, [linkTo]);

    const buttonProps = {
        className: 'style-shield-button',
        type: 'normal' as const,
        onClick: openShield,
    };

    const controlledShieldProps = {
        url: 'creativeshieldUrl',
    };

    return (
        <>
            {
                isShowCommodityCreativity
                    ? (
                        <div className="qingge-managecenter-creative-component">
                            <Tabs
                                className="creative-component-type-tabs"
                                activeKey="commodity"
                            >
                                <Tabs.TabPane tab="商品样式" key="commodity" />
                            </Tabs>
                            <iframe
                                className="creative-component-iframe"
                                src={commodityCreativityUrl}
                                width="100%"
                                frameBorder="0"
                            />
                        </div>
                    )

                    : (
                        <div className="qingge-managecenter-creative-component">
                            <Button className="style-analyse-button" onClick={linkToAnalyse}>
                                广告识别 <Tip keyName="styleAnalyse" className="style-recognize-tip" />
                            </Button>
                            {creativeLevel === 'text' && (
                                <Button {...buttonProps}>
                                    自动网址屏蔽
                                    <HoverTip content="该屏蔽仅作用于文字类组件自动生成“全网知识内容”智能能力。添加Url屏蔽后，内容将于隔天生效。" />
                                </Button>
                            )}
                            {
                                CREATIVE_COMP_IFRAME_DISABLE_LIST.includes(creativeLevel)
                                    ? (
                                        <iframe
                                            className="creative-component-iframe"
                                            src={url}
                                            width="100%"
                                            frameBorder="0"
                                        />
                                    )
                                    : (
                                        <SingletonIframe
                                            iframePath={url}
                                            autoShow
                                            style={{
                                                width: '100%',
                                                height: '100vh',
                                            }}
                                        />
                                    )
                            }
                            {ControlledShield && <ControlledShield {...controlledShieldProps} />}
                        </div>
                    )
            }

        </>
    );
}
export default memo(CreativeComponent);
