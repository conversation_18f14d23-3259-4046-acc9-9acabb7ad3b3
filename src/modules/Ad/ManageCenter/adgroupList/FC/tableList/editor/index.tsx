import {lazy} from 'react';
import {IEditorConfig} from '@/components/common/materialList/EditorContainer';
import {FcAdgroupType} from '@/interface/fcThreeAdForm/adgroup';
import {AdLinkType} from '@/dicts/adUrlType';

export const editorConfig: IEditorConfig = {
    relatedProducts: {
        title: '选择产品', // 操作名称
        width: 900, // 如果需要自定义宽度
        inline: lazy(() => import('./StructuredCategoryEditor')),
        batch: lazy(() => import('./BatchStructuredCategoryEditor')),
    },
    // 法律行业的选择产品
    structuredLawProduct: {
        title: '选择产品',
        width: 900,
        inline: lazy(() => import('./StructuredLawProduct')),
        batch: lazy(() => import('./BatchStructuredLawProduct')),
    },
    storeName: {
        title: '修改店铺信息',
        width: 800,
        inline: lazy(() => import('manageCenter/editor/InlineStoreMobile')),
    },
    adgroupAutoTargetingStatus: {
        width: 800,
        title: '自动定向',
        batch: lazy(() => import('./BatchSwitchStatus')),
        defaultInitialValue: {
            adgroupAutoTargetingStatus: true,
            tip: '选择各异',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            adgroupAutoTargetingStatus: data.adgroupAutoTargetingStatus,
            tip: '',
        }),
    },
    creativeTextOptimizationStatus: {
        width: 800,
        title: '自动文案优化',
        batch: lazy(() => import('./BatchSwitchStatus')),
        defaultInitialValue: {
            creativeTextOptimizationStatus: true,
            tip: '选择各异',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            creativeTextOptimizationStatus: data.creativeTextOptimizationStatus,
            tip: '',
        }),
    },
    segmentRecommendStatus: {
        width: 800,
        title: '自动图片优化',
        batch: lazy(() => import('./BatchSwitchStatus')),
        defaultInitialValue: {
            segmentRecommendStatus: true,
            tip: '选择各异',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            segmentRecommendStatus: data.segmentRecommendStatus,
            tip: '',
        }),
    },
    appShopDirectStatus: {
        title: '修改应用商店直投',
        width: 800,
        batch: lazy(() => import('./BatchSwitchStatus')),
        defaultInitialValue: {
            appShopDirectStatus: true,
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            appShopDirectStatus: !!data.appShopDirectStatus,
            tip: '',
        }),
    },
    newMobileFinalUrl: {
        width: 800,
        title: '移动端链接',
        batch: lazy(() => import('./BatchFinalUrlEditor')),
        defaultInitialValue: {
            newMobileFinalUrl: '',
            tip: '选择各异',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            newMobileFinalUrl: data.newMobileFinalUrl,
            tip: '',
        }),
    },
    relateAgent: {
        width: 800,
        title: '关联商家智能体',
        batch: lazy(() => import('./RelateAgent')),
        transformInitialValue: (data: FcAdgroupType) => {
            const agentUserId = data?.adgroupAgentInfo?.userId;
            return ({
                agentUserId,
                agentUrl: data?.adgroupAgentUrl,
            });
        },
    },
    newPcFinalUrl: {
        width: 800,
        title: '计算机端链接',
        batch: lazy(() => import('./BatchFinalUrlEditor')),
        defaultInitialValue: {
            newPcFinalUrl: '',
            tip: '选择各异',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            newPcFinalUrl: data.newPcFinalUrl,
            tip: '',
        }),
    },
    maxPrice: {
        width: 800,
        title: '单元点击出价',
        batch: lazy(() => import('./BatchMaxPrice')),
        defaultInitialValue: {
            maxPrice: undefined,
            tip: '选择各异',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            maxPrice: data.maxPrice,
            tip: '',
        }),
    },
    negativeWords: {
        title: '修改否定关键词',
        inline: lazy(() => import('./negative/index')),
        batch: lazy(() => import('./negative/BatchNegativeWords')),
        width: 830,
    },
    anchorName: {
        width: 640,
        title: '修改主播名称',
        batch: lazy(() => import('./BatchAnchorName')),
        defaultInitialValue: {
            anchorId: undefined,
            tip: '选择各异',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            anchorId: data.anchorId,
            tip: '',
        }),
    },
    newAdgroup: {
        width: 640,
        title: '新建单元',
        batch: lazy(() => import('./CampaignSelector')),
        isCustomTitle: true,
    },
    adgroupUrlType: {
        title: '修改广告链接类型',
        inline: lazy(() => import('./urlType/InlineUrlTypeEditor')),
        batch: lazy(() => import('./urlType/BatchUrlTypeEditor')),
        width: 640,
        defaultInitialValue: {
            adgroupUrlType: AdLinkType.LANDING_PAGE,
            newMobileFinalUrl: '',
            newPcFinalUrl: '',
            agentUrl: undefined,
            agentParam: '',
            tip: '选择各异',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            adgroupUrlType: data.adgroupUrlType,
            newMobileFinalUrl: data.newMobileFinalUrl,
            newPcFinalUrl: data.newPcFinalUrl,
            agentUrl: data.adgroupAgentUrl,
            agentParam: data.adgroupAgentParam,
            tip: '',
        }),
    },
    productSetName: {
        width: 640,
        title: '编辑投放商品组',
        inline: lazy(() => import('./ProductSet')),
    },
    nativeContentInfos: {
        width: 700,
        title: '编辑笔记内容',
        inline: lazy(() => import('./NativeContentInfos')),
    },
    commodityProductSet: {
        width: 1200,
        title: '编辑投放商品组',
        inline: lazy(() => import('./CommodityProductSet')),
    },
    adgroupAgentParam: {
        width: 640,
        title: '设置智能体监控后缀',
        batch: lazy(() => import('./BatchAgentParamEditor')),
        defaultInitialValue: {
            adgroupAgentParam: '',
        },
        transformInitialValue: (data: FcAdgroupType) => ({
            adgroupAgentParam: data.adgroupAgentParam || '',
        }),
    },
    detail: {
        title: ({targetName}: {targetName: string}) => (<span>{targetName}</span>),
        inline: lazy(() => import('./detail')),
        width: 1080,
        isCustomTitle: true,
    },
};
