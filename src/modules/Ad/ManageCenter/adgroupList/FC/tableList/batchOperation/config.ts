import {getDefaultProductCategoryType} from 'commonLibs/components/selectCard/product/config';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import {
    isFcAgentUrlUser,
    isFcLiveUser,
    isDirectStatusBatchEditUser,
} from '@/utils/getFlag';
import {RowSelectionMethodsProps} from '@/hooks/selection';
import {FC_MARKET_TARGET, isLiveSubMarket} from '@/dicts/marketingTarget';
import {EditorContainerControlledMethods} from '@/components/common/materialList/EditorContainer';
import {sendMonitor} from '@/utils/logger';

interface AdgroupBatchOptionsParams {
    getSelectedInfo: RowSelectionMethodsProps['getSelectedInfo'];
    getAdgroupById: (id: string) => any;
}
// eslint-disable-next-line complexity
export const getAdgroupBatchOptions = ({
    getSelectedInfo,
    getAdgroupById,
}: AdgroupBatchOptionsParams) => {
    const {isCheckAll, selectedIds} = getSelectedInfo();
    const disabledUrlTypeByRotating =
        !isCheckAll
        && selectedIds.every(id => getAdgroupById(id)?.rotatingCycStatus);
    const disabledBecauseLive =
        !isCheckAll
        && selectedIds.every(id =>
            isLiveSubMarket(getAdgroupById(id)?.subMarketingTargetId)
        );
    const isNotEveryCPQL = !isCheckAll
        && selectedIds.some(id => getAdgroupById(id)?.marketingTargetId !== FC_MARKET_TARGET.CPQL);
    const productCategroyType = getDefaultProductCategoryType();
    return [
        {
            label: '设置',
            value: 'SETTING',
            groupChildren: [
                {
                    label: '自动定向',
                    value: 'adgroupAutoTargetingStatus',
                },
                {
                    label: '自动文案优化',
                    value: 'creativeTextOptimizationStatus',
                },
                {
                    label: '自动图片优化',
                    value: 'segmentRecommendStatus',
                },
                {
                    label: '单元点击出价',
                    value: 'maxPrice',
                },
                ...(isFcLiveUser()
                    ? [
                        {
                            label: '主播名称',
                            value: 'anchorName',
                        },
                    ]
                    : []),
                {
                    label: '否定关键词',
                    value: 'negativeWords',
                },
                ...(isDirectStatusBatchEditUser()
                    ? [
                        {
                            label: '应用商店直投',
                            value: 'appShopDirectStatus',
                        },
                    ]
                    : []
                ),
                ...(productCategroyType ? [{
                    label: '关联产品',
                    value: productCategroyType === productCategoryTypeEnum.LAW
                        ? 'structuredLawProduct'
                        : 'relatedProducts',
                    disabled: isNotEveryCPQL,
                    tip: isNotEveryCPQL ? '仅销售线索下的单元才可批量设置关联产品' : '',
                }] : []),
            ],
        },
        {
            label: '广告链接',
            value: 'OPERATION',
            groupChildren: [
                ...(isFcAgentUrlUser()
                    ? [
                        {
                            label: '广告链接类型',
                            value: 'adgroupUrlType',
                            disabled:
                                  disabledUrlTypeByRotating
                                  || disabledBecauseLive,
                            tip: disabledUrlTypeByRotating
                                ? '智能体轮值生效中的单元，不支持修改广告链接类型'
                                : disabledBecauseLive
                                    ? '直播场景不支持修改广告链接'
                                    : '',
                        },
                    ]
                    : []),
                {
                    label: '移动端链接',
                    value: 'newMobileFinalUrl',
                    disabled: disabledBecauseLive,
                    tip: disabledBecauseLive
                        ? '直播场景不支持修改移动端链接'
                        : '',
                },
                {
                    label: '计算机端链接',
                    value: 'newPcFinalUrl',
                    disabled: disabledBecauseLive,
                    tip: disabledBecauseLive
                        ? '直播场景不支持修改PC端链接'
                        : '',
                },
                ...(isFcAgentUrlUser()
                    ? [
                        {
                            label: '关联商家智能体',
                            value: 'relateAgent',
                            disabled: disabledBecauseLive,
                            tip: disabledBecauseLive
                                ? '直播场景不支持关联商家智能体'
                                : '',
                        },
                    ]
                    : []),
                ...(isFcAgentUrlUser()
                    ? [
                        {
                            label: '智能体监控后缀',
                            value: 'adgroupAgentParam',
                            disabled: disabledBecauseLive,
                            tip: disabledBecauseLive
                                ? '直播场景不支持修改智能体监控后缀'
                                : '',
                        },
                    ]
                    : []),
            ],
        },
    ];
};

export const getBatchOpenEditorMap = (
    openEditor: EditorContainerControlledMethods['openEditor']
): {[key: string]: () => void} => {
    return {
        relatedProducts: () => {
            openEditor('batch', 'relatedProducts', undefined, {
                containerType: 'drawer',
                width: 800,
                hideDefaultFooter: true,
                hideDefaultTitle: true,
                type: 'basic'
            });
            sendMonitor('click', {
                type: getDefaultProductCategoryType(),
                level: 'adgroup_list',
                source: 'struct_product',
                item: 'batch_entry'
            });
        },
    };
};
