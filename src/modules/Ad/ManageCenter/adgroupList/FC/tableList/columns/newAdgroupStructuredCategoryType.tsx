import {IconEdit} from 'dls-icons-react';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import {withSumCell} from '@/utils/handleSummary';
import {createCustomRender} from '@/utils/render';
import {FcAdgroupType, StructuredCategoryStatusEnum} from '@/interface/fcThreeAdForm/adgroup';
import {MaterialList} from '@/interface/tableList';
import {FC_MARKET_TARGET} from '@/dicts/marketingTarget';
import {getDefaultProductCategoryType} from '@/components/ChatCards/Product/config';
import {RELATED_PRODUCT_TYPE} from '@/dicts/relatedProductsType';
import {sendMonitor} from '@/utils/logger';

interface SegmentRecommendStatusColumnProps {
    record: FcAdgroupType;
    trigger: any;
    configs: MaterialList.ColumnItem;
}

function AdgroupStructuredCategoryType({
    record,
    trigger,
    configs,
}: SegmentRecommendStatusColumnProps) {
    const {
        adgroupId,
        relatedProducts,
        productCategoryType: campaignProductCategoryType,
        marketingTargetId,
    } = record;

    const productCategoryType = campaignProductCategoryType || getDefaultProductCategoryType();

    const isLawIndustry = productCategoryType === productCategoryTypeEnum.LAW;
    const field = isLawIndustry ? 'structuredLawProduct' : configs.columnName;


    const onIconClick = () => {
        trigger(
            'openEditor', 'inline', field, adgroupId,
            {hideDefaultFooter: !isLawIndustry, hideDefaultTitle: !isLawIndustry}
        );
        sendMonitor('click', {
            type: productCategoryType,
            level: 'adgroup_list',
            source: 'struct_product',
            item: 'inline_entry'
        });
    };

    let text = '-';
    if (relatedProducts === RELATED_PRODUCT_TYPE.SET) {
        text = '已设置';
    }
    else if (relatedProducts === RELATED_PRODUCT_TYPE.NO_SET) {
        text = '未设置';
    }

    return (
        <div className="column-cell-flex">
            {text}
            {
                marketingTargetId !== FC_MARKET_TARGET.CPQL ? null : (
                    <IconEdit className="inline-operation-icon" onClick={onIconClick} />
                )
            }
        </div>
    );
}

export default {
    render: createCustomRender((configs, {trigger}) => withSumCell(
        (text, record) => {
            return (
                <AdgroupStructuredCategoryType
                    record={record as FcAdgroupType}
                    trigger={trigger}
                    configs={configs}
                />
            );
        }
    )),
    filters: {
        filterType: 'enum',
        options: [{
            value: RELATED_PRODUCT_TYPE.SET,
            label: '已设置',
        }, {
            value: RELATED_PRODUCT_TYPE.NO_SET,
            label: '未设置',
        }],
    },
    config: {
        align: 'left',
    },
};
