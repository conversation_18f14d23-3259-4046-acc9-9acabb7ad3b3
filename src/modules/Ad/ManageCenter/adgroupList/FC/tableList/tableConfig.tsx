import {identity} from 'lodash-es';
import {withSumCell} from '@/utils/handleSummary';
import {createCustomRender} from '@/utils/render';
import adgroupName from './columns/adgroupName';
import status from './columns/status';
import maxPrice from './columns/maxPrice';
import adgroupAutoTargetingStatus from './columns/newAdgroupAutoTargetingStatus';
import creativeTextOptimizationStatus from './columns/newAdgroupCreativeTextOptimizationStatus';
import segmentRecommendStatus from './columns/newAdgroupSegmentRecommendStatus';
import newPcFinalUrl from './columns/newPcFinalUrl';
import relatedProducts from './columns/newAdgroupStructuredCategoryType';
import newMobileFinalUrl from './columns/newMobileFinalUrl';
import appShopDirectStatus from './columns/appShopDirectStatus';
import adgroupAppBinds from './columns/adgroupAppBinds';
import negativeWords from './columns/negativeWords';
import projectName from './columns/projectName';
import operations from './columns/operations';
import campaignName from './columns/campaignName';
import anchorName from './columns/anchorName';
import adgroupUrlType from './columns/adLinkType';
import {AgentCell} from './columns/agentCell';
import productSetName from './columns/productSetName';
import storeName from './columns/storeName';
import nativeContentInfos from './columns/nativeContentInfos';
import bjhUserInfo from './columns/bjhUserInfo';
import adgroupAgentParam from './columns/adgroupAgentParams';

export const tableFieldsMap = {
    adgroupName,
    anchorName,
    adgroupId: {
        render: withSumCell(identity),
        filters: {
            filterType: 'string',
            operatorOptions: [
                {value: 'in', label: '包含'},
            ],
            defaultOperatorValue: 'like',
        },
        config: {
            width: 96,
            minWidth: 76,
            align: 'left',
        },
    },
    campaignId: {
        render: withSumCell(identity),
        filters: {
            filterType: 'string',
            operatorOptions: [
                {value: 'in', label: '包含'},
            ],
            defaultOperatorValue: 'like',
        },
        config: {
            align: 'left',
        },
    },
    campaignName,
    maxPrice,
    status,
    adgroupAutoTargetingStatus,
    creativeTextOptimizationStatus,
    segmentRecommendStatus,
    newPcFinalUrl,
    relatedProducts,
    newMobileFinalUrl,
    appShopDirectStatus,
    adgroupAppBinds,
    negativeWords,
    projectName,
    operations,
    adgroupUrlType,
    adgroupAgentUrl: {
        render: createCustomRender((configs, {trigger}) => withSumCell((value: unknown, record: any) => (
            <AgentCell
                inlineSaveAdgroup={(...args: any[]) => trigger('inlineSaveAdgroup', ...args)}
                record={record}
            />
        ))),
    },
    productSetName,
    storeName,
    nativeContentInfos,
    bjhUserInfo,
    adgroupAgentParam,
};

export const relatedFields = {
    campaignName: ['campaignId'],
    adgroupName: [
        'campaignId', 'adgroupId', 'marketingTargetId', 'subMarketingTargetId',
        'businessPointId', 'adType', 'projectModeType',
        'maxPrice', 'campaignBidType', 'productCategoryType', 'projectOcpcBidType', 'marketingTargetId',
    ],
    status: ['pause'],
    deviceBidSet: [
        'shopType', 'adType', 'campaignBidType', 'priceRatio', 'bidPrefer', 'pcPriceRatio',
        'campaignPcPriceRatio', 'campaignPriceRatio',
    ],
    productSetName: ['productSetId', 'adType', 'catalogId'],
    maxPrice: ['adType', 'campaignBidType', 'projectOcpcBidType'],
    bidPrefer: ['adType', 'campaignBidType'],
    negativeWords: ['exactNegativeWords', 'negativeKeywordCountConfig'],
    brandInfo: ['marketingTargetId'],
    mixFinalPcFinalUrl: ['marketingTargetId', 'shopType', 'shadowPcFinalUrl',
        'pcTrackParam', 'pcTrackTemplate', 'campaignPcPriceRatio', 'bidPrefer', 'pcPriceRatio',
        'newPcFinalUrl', 'newShadowPcFinalUrl'],
    mixFinalMobileUrl: [
        'marketingTargetId', 'shopType', 'shadowMobileFinalUrl',
        'mobileTrackParam', 'mobileTrackTemplate', 'campaignPriceRatio',
        'bidPrefer', 'priceRatio',
        'promotionScene', 'campaignTransTypes',
        'newMobileFinalUrl', 'newShadowMobileFinalUrl',
    ],
    newPcFinalUrl: ['marketingTargetId', 'shopType', 'shadowPcFinalUrl',
        'pcTrackParam', 'pcTrackTemplate', 'campaignPcPriceRatio', 'bidPrefer', 'pcPriceRatio',
        'newPcFinalUrl', 'newShadowPcFinalUrl'],
    newMobileFinalUrl: [
        'marketingTargetId', 'shopType', 'shadowMobileFinalUrl',
        'mobileTrackParam', 'mobileTrackTemplate', 'campaignPriceRatio',
        'bidPrefer', 'priceRatio',
        'promotionScene', 'campaignTransTypes',
        'newMobileFinalUrl', 'newShadowMobileFinalUrl',
    ],
    pcTrackParam: ['marketingTargetId', 'shopType', 'shadowPcTrackParam',
        'campaignBidType', 'pcFinalUrl', 'pcTrackTemplate'],
    mobileTrackParam: ['marketingTargetId', 'shopType', 'shadowMobileTrackParam',
        'campaignBidType', 'mobileFinalUrl', 'mobileTrackTemplate'],
    pcTrackTemplate: ['marketingTargetId', 'shopType', 'shadowPcTrackTemplate',
        'campaignBidType', 'pcFinalUrl', 'pcTrackParam'],
    mobileTrackTemplate: ['marketingTargetId', 'shopType', 'shadowMobileTrackTemplate',
        'campaignBidType', 'mobileFinalUrl', 'mobileTrackParam'],
    storeName: ['promotionTypes'],
    relatedProducts: [
        'productCategoryType',
        'structuredContentIds',
        'structuredContentIdStrs',
        'newMobileFinalUrl',
    ],
    anchorName: ['anchorLogo', 'anchorId'],
    newAdgroupAutoTargetingStatus: ['adgroupAutoTargetingStatus'],
    newAdgroupSegmentRecommendStatus: ['segmentRecommendStatus'],
    newAdgroupCreativeTextOptimizationStatus: ['creativeTextOptimizationStatus'],
    projectName: ['projectId'],
    agentUrl: ['adgroupAgentParam', 'adgroupAgentInfo', 'projectAgentUrl'],
    adgroupUrlType: ['rotatingCycStatus', 'rotatingCycStatusDetail'],
    adgroupAgentParam: ['adgroupUrlType', 'adgroupAgentInfo'],
};


export const needReplaceFields = {};

export const supportedFilterEntryFields = [
    'adgroupName', 'status', 'cost', 'impression', 'click', 'cpc', 'ctr', 'maxPrice', 'campaignName',
    'adgroupAutoTargetingStatus', 'topPageViews', 'topPClicks', 'topPay',
];

export const relatedDownloadFields = {};
export const needReplaceDownloadFields = {
    adgroupAgentUrl: [],
    adgroupUrlType: [],
    adgroupAgentParam: [],
    adgroupAgentInfo: [],
};
