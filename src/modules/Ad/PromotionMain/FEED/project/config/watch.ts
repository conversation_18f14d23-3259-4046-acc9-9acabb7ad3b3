/* eslint-disable max-statements, complexity */
import {WatchConfig} from '@baidu/react-formulator';
import {crowdIdentifyValue} from 'feedCommonLibs/config/bid/ocpcBidRatio';
import {
    AppSubType, BidModeEnum, FeedAIMaxEduLevel, FeedAIMaxTradeVersionType,
    FeedAppDownloadType, FeedProjectFormData, feedProjectModeType, FeedSubjectType,
    MiniProgramType, OpenUrlEnum, SaleType, SmartControlEnum,
} from '@/interface/aixProject/feedProject';
import {AutoIdeaSwitchEnum, LiftSwitchEnum, ReopenAutoIdeaSwitchEnum, UseProjectBudgetEnum} from '@/dicts/project';
import {
    isFeedProjectMainflowDefaultCrowdUser, isFeedShortPlayIndustryAimaxUser,
} from '@/utils/getFlag';
import {FeedOcpcBidRatioTypeEnum} from '@/dicts/ocpcBidRatio';
import {
    getEducationIndustry, getMedecalIndustry, getShortPlayIndustry, getBusinessIndustry,
} from '@/components/List/ProjectList/AiAgent/util';
import {ScheduleModel} from '@/config/feed/liftBudget';
import {eduCrowdCoefConfigs} from '@/components/ChatCards/FeedAiMax/config';
import {checkIsMdeicalMustBindCatalog} from '@/config/feed/product';
import {
    BJHType, checkIsBjhShortPlaySimplifiedScene,
} from '@/components/Project/FeedEditor/BJHType/config';
import {UsePublicEnum} from '@/interface/feedEditor/creative';
import {TRANS_TYPE_ENUM} from '@/config/feed/trans';
import {DEEP_OPT_TYPE, OPTIMIZE_DEEP_TRANS} from '@/config/feed/bid';
import {checkSubjectType} from '@/config/feed/campaign';
import {spyxTrade, getAutoOpenTradeList} from '@/config/feed/trade';
import globalData from '@/utils/globalData';
import {
    isBJHShortPlayAiMaxCommonUser, isBJHShortPlayAIMaxUser, isFeedAppUpdateUser,
    isFeedBjhVideoUser, isFeedSpyxTrade
} from '@/utils/getFeedFlag';
import {isBjhRoi} from '../utils';
import {options as transTypeRoiOptions} from '../transTypePreview';

export const PayToWatchTransForm = 32;

// 目前只有短剧行业以及aimax权限用户且在百家号短剧场景下才会默认设置aimax
function checkIsDefautSetAiMax(formData: Partial<FeedProjectFormData>) {
    return checkIsBjhShortPlaySimplifiedScene(formData)
    && formData.bidMode === BidModeEnum.TRANS_COST
    && getShortPlayIndustry()
    && isFeedShortPlayIndustryAimaxUser();
}

function getInitialProjectModeType(formData: Partial<FeedProjectFormData>) {
    return checkIsDefautSetAiMax(formData) ? feedProjectModeType.AIMAX : feedProjectModeType.CUSTOM;
}

function getInitialTransferData(formData: Partial<FeedProjectFormData>) {
    return checkIsBjhShortPlaySimplifiedScene(formData) ? TRANS_TYPE_ENUM.PAY_TO_WATCH : undefined;
}

function getInitialEnableOptimizeDeepTransData(formData: Partial<FeedProjectFormData>) {
    const transType = formData.transTypePreview || formData.transType;
    return checkIsBjhShortPlaySimplifiedScene(formData)
    && transType === TRANS_TYPE_ENUM.PAY_TO_WATCH
        ? OPTIMIZE_DEEP_TRANS.YES
        : OPTIMIZE_DEEP_TRANS.NO;
}

function getInitialDeepOptTypeData(formData: Partial<FeedProjectFormData>) {
    const transType = formData.transTypePreview || formData.transType;
    return checkIsBjhShortPlaySimplifiedScene(formData)
    && transType === TRANS_TYPE_ENUM.PAY_TO_WATCH
    && formData.enableOptimizeDeepTrans === OPTIMIZE_DEEP_TRANS.YES
        ? DEEP_OPT_TYPE.USE_ROI_TYPE
        : undefined;
}

function getInitialTransFromData(formData: Partial<FeedProjectFormData>) {
    const transType = formData.transTypePreview || formData.transType;
    return checkIsBjhShortPlaySimplifiedScene(formData)
    // 正常来讲transFrom需要从transAssetsOptions中通过appTransId查找所得
    // 但是百家号短剧简化场景不需要选取appTransId，所以后端要求直接默认传值便可
    && transType === TRANS_TYPE_ENUM.PAY_TO_WATCH
        ? PayToWatchTransForm
        : undefined;

}

const initialAIMaxInitial = (formData: Partial<FeedProjectFormData>) => {
    formData.aiMaxTradeVersionType = FeedAIMaxTradeVersionType.OFF;
    formData.aiMaxTradeVersionLevel = FeedAIMaxEduLevel.NoUse;
    if (formData.useLiftBudget && formData.lift?.scheduleModel !== ScheduleModel.JUST) {
        formData.useLiftBudget = false;
    }
    formData.smartControlSwitch = false;
    formData.smartControl = SmartControlEnum.noop;
    formData.liftSwitch = false;
    formData.refinedBidSwitch = false;
    formData.ocpcBidRatioType = FeedOcpcBidRatioTypeEnum.noUse;
    formData.ocpcBidRatioSubType = undefined;
    formData.crowdCoefConfigs = {};
    formData.cycOcpcBidRatio = {};
    formData.customAgeRangeConfig = [];
    formData.specifiedAgeRangeConfig = [];
    formData.regionConfig = {};
    formData.autoIdeaSwitch = AutoIdeaSwitchEnum.OFF;
    formData.reopenAutoIdeaSwitch = ReopenAutoIdeaSwitchEnum.OFF;
    if (checkIsDefautSetAiMax(formData) && formData.projectModeType === feedProjectModeType.AIMAX) {
        formData.aiMaxTradeVersionType = FeedAIMaxTradeVersionType.SHORT_PLAY;
        formData.aiMaxTradeVersionLevel = FeedAIMaxEduLevel.Aggressive;
        formData.liftSwitch = LiftSwitchEnum.ON;
        if (formData?.bjhType !== BJHType.video) {
            formData.autoIdeaSwitch = AutoIdeaSwitchEnum.ON;
        }
        formData.smartControl = SmartControlEnum.conservative;
    }
};

const initialBudgetInitial = (formData: Partial<FeedProjectFormData>, flag: boolean) => {
    if (flag) {
        formData.isUseProjectBudget = UseProjectBudgetEnum.ON;
        formData.budget = 0;
    } else {
        formData.isUseProjectBudget = UseProjectBudgetEnum.OFF;
        formData.budget = undefined;
    }
    formData.useImmediateLiftBudget = false;
};

export const Watch$$GUIProjectConfig: WatchConfig<FeedProjectFormData> = {
    subject: (value, formData: Partial<FeedProjectFormData>) => {
        const {isLiveSubject, isProgramSubject, isEBusinessSubject} = checkSubjectType(value);
        if (value === FeedSubjectType.salesLead) {
            formData.saleType = SaleType.default;
        } else {
            formData.saleType = undefined;
        }
        if (value === FeedSubjectType.bjh) {
            formData.bjhType = BJHType.shortPlay;
            if (isBJHShortPlayAIMaxUser()) {
                initialBudgetInitial(formData, true);
            } else {
                initialBudgetInitial(formData, false);
            }
        } else {
            formData.bjhType = undefined;
            initialBudgetInitial(formData, true);
        }
        if (value === FeedSubjectType.android) {
            formData.saleType = SaleType.default;
            formData.appSubType = AppSubType.download;
            formData.appInfo = {
                downloadType: FeedAppDownloadType.download,
                openurl: Number(OpenUrlEnum.on),
            };
        } else if (value === FeedSubjectType.ios) {
            formData.saleType = SaleType.default;
            formData.appSubType = AppSubType.download;
            formData.appInfo = {
                downloadType: undefined,
                openurl: undefined,
            };
        } else if (value === FeedSubjectType.hmos) {
            formData.saleType = SaleType.default;
            formData.appSubType = AppSubType.download;
            formData.appInfo = {
                downloadType: undefined,
                openurl: undefined,
            };
        } else if (isLiveSubject) {
            formData.appSubType = AppSubType.download;
            formData.saleType = SaleType.live;
            formData.appInfo = undefined;
        } else {
            formData.appSubType = undefined;
            formData.appInfo = undefined;
        }
        if (
            (value === FeedSubjectType.salesLead && checkIsMdeicalMustBindCatalog())
            // 电商店铺项目/计划层级引导客户默认启用产品库
            || (value === FeedSubjectType.eBusiness)
        ) {
            formData.useProduct = true;
        } else {
            formData.useProduct = false;
        }
        formData.product = {};
        formData.bidMode = BidModeEnum.TRANS_COST;
        formData.transTypePreview = getInitialTransferData(formData);
        formData.appTransId = undefined;
        formData.selectedTransAssets = undefined;
        formData.transType = getInitialTransferData(formData);
        formData.appUrl = undefined;
        formData.transFrom = getInitialTransFromData(formData);
        formData.deepTransType = undefined;
        formData.ocpcBid = undefined;
        formData.deepOcpcBid = undefined;
        formData.roiType = UsePublicEnum.FALSE;
        formData.roiRatio = undefined;
        formData.deepStayDays = undefined;
        formData.campaignFeedIds = [];
        formData.enableOptimizeDeepTrans = getInitialEnableOptimizeDeepTransData(formData);
        formData.deepOptType = getInitialDeepOptTypeData(formData);
        formData.projectModeType = getInitialProjectModeType({...formData, subject: value});
        initialAIMaxInitial(formData);
        if (isProgramSubject || isEBusinessSubject) {
            formData.isUseProjectBudget = UseProjectBudgetEnum.OFF;
        }
        if (isProgramSubject) {
            formData.miniProgramType = MiniProgramType.wechatGame;
        }
        else {
            formData.miniProgramType = undefined;
        }

        const {tradeId2nd} = globalData.get('feedBasicData') || {};
        const isSpyxTrade = spyxTrade.includes(+tradeId2nd) && isFeedSpyxTrade();
        if (isSpyxTrade) {
            formData.projectModeType = feedProjectModeType.AIMAX;
            formData.smartControlSwitch = true;
            formData.smartControl = SmartControlEnum.aggressive;
        }
    },
    saleType: (value, formData: Partial<FeedProjectFormData>) => {
        formData.appTransId = undefined;
        formData.selectedTransAssets = undefined;
        formData.transType = undefined;
        formData.transFrom = undefined;
        formData.deepTransType = undefined;
        formData.ocpcBid = undefined;
        formData.deepOcpcBid = undefined;
        formData.roiType = UsePublicEnum.FALSE;
        formData.roiRatio = undefined;
        formData.deepStayDays = undefined;
        formData.campaignFeedIds = [];
    },
    appSubType: (value, formData: Partial<FeedProjectFormData>) => {
        if (value === AppSubType.download) {
            formData.subject = FeedSubjectType.android;
            if (formData.subject === FeedSubjectType.android) {
                formData.appInfo = {
                    downloadType: FeedAppDownloadType.download,
                    openurl: Number(OpenUrlEnum.off),
                };
            }
            if (formData.subject === FeedSubjectType.ios) {
                formData.appInfo = {
                    downloadType: undefined,
                    openurl: undefined,
                };
            }
        }
        else if (value === AppSubType.appInvoke) {
            formData.subject = FeedSubjectType.appLink;
            formData.appInfo = undefined;
        }
        else if (value === AppSubType.appAppoint) {
            formData.subject = FeedSubjectType.android;
            formData.appInfo = undefined;
        }
        if (isFeedAppUpdateUser()) {
            formData.saleType = SaleType.default;
        }
        formData.appTransId = undefined;
        formData.selectedTransAssets = undefined;
        formData.transType = undefined;
        formData.transFrom = undefined;
        formData.deepTransType = undefined;
        formData.ocpcBid = undefined;
        formData.deepOcpcBid = undefined;
        formData.roiType = UsePublicEnum.FALSE;
        formData.roiRatio = undefined;
        formData.deepStayDays = undefined;
        formData.campaignFeedIds = [];
    },
    bjhType: (value, formData: Partial<FeedProjectFormData>) => {
        initialAIMaxInitial(formData);
        formData.projectModeType = getInitialProjectModeType({...formData, bjhType: value});
        const bjtTypes = [BJHType.novel];
        if (!isFeedBjhVideoUser()) {
            bjtTypes.push(BJHType.video);
        }
        if ([BJHType.novel].includes(value)) {
            formData.bidMode = BidModeEnum.TRANS_COST;
            formData.roiType = undefined;
            initialBudgetInitial(formData, false);
        } else if (isBJHShortPlayAIMaxUser()) {
            initialBudgetInitial(formData, true);
        }
        formData.transType = getInitialTransferData(formData);
        formData.transTypePreview = getInitialTransferData(formData);
        formData.enableOptimizeDeepTrans = getInitialEnableOptimizeDeepTransData(formData);
        formData.deepOptType = getInitialDeepOptTypeData(formData);
    },
    bidMode: (value, formData: Partial<FeedProjectFormData>) => {
        formData.appTransId = undefined;
        formData.selectedTransAssets = undefined;
        if (isBjhRoi(formData)) {
            formData.transType = TRANS_TYPE_ENUM.LTV;
            formData.transTypePreview = TRANS_TYPE_ENUM.LTV;
            formData.roiType = OPTIMIZE_DEEP_TRANS.YES;
            initialBudgetInitial(formData, false);
        } else {
            formData.transType = undefined;
            formData.roiType = UsePublicEnum.FALSE;
            if (isBJHShortPlayAIMaxUser()) {
                initialBudgetInitial(formData, true);
            }
        }
        formData.transFrom = undefined;
        formData.deepTransType = undefined;
        formData.ocpcBid = undefined;
        formData.deepOcpcBid = undefined;
        formData.roiRatio = undefined;
        formData.deepStayDays = undefined;
        formData.campaignFeedIds = [];
        initialAIMaxInitial(formData);
        formData.projectModeType = getInitialProjectModeType({...formData, bidMode: value});
    },
    transTypePreview: (value, formData: Partial<FeedProjectFormData>) => {
        formData.transType = value;
        if (checkIsBjhShortPlaySimplifiedScene(formData)) {
            formData.bidMode = transTypeRoiOptions.map(({value}) => value).includes(value)
                ? BidModeEnum.ROI
                : BidModeEnum.TRANS_COST;
            formData.appTransId = undefined;
            formData.selectedTransAssets = undefined;
            if (isBjhRoi(formData)) {
                formData.roiType = OPTIMIZE_DEEP_TRANS.YES;
                initialBudgetInitial(formData, false);
            } else {
                formData.roiType = UsePublicEnum.FALSE;
                if (isBJHShortPlayAIMaxUser()) {
                    initialBudgetInitial(formData, true);
                }
            }
            formData.transFrom = getInitialTransFromData(formData);
            formData.deepTransType = undefined;
            formData.ocpcBid = undefined;
            formData.deepOcpcBid = undefined;
            formData.roiRatio = undefined;
            formData.deepStayDays = undefined;
            formData.campaignFeedIds = [];
            formData.projectModeType = getInitialProjectModeType(formData);
            initialAIMaxInitial(formData);
            formData.enableOptimizeDeepTrans = getInitialEnableOptimizeDeepTransData(formData);
            formData.deepOptType = getInitialDeepOptTypeData(formData);
        }
    },
    useProduct: (value, formData: Partial<FeedProjectFormData>) => {
        formData.product = {};
        formData.campaignFeedIds = [];
    },
    'product.productType': (value, formData: Partial<FeedProjectFormData>) => {
        // 推荐关联产品场景，会自动修改关联产品项，此时处于编辑项目，不能清空这些内容
        if (formData.recommendCategory) {
            return;
        }
        formData.campaignFeedIds = [];
        formData.appTransId = undefined;
        formData.selectedTransAssets = undefined;
        formData.transType = undefined;
        formData.transFrom = undefined;
        formData.deepTransType = undefined;
        formData.ocpcBid = undefined;
        formData.deepOcpcBid = undefined;
        formData.roiType = UsePublicEnum.FALSE;
        formData.roiRatio = undefined;
        formData.deepStayDays = undefined;
    },
    projectModeType: (value, formData: Partial<FeedProjectFormData>) => {
        const {subject, bjhType, bidMode, initialData} = formData;
        const {projectModeType: initialProjectModeType} = initialData ?? {};
        if (value === feedProjectModeType.CUSTOM) {
            initialAIMaxInitial(formData);
            formData.autoIdeaSwitch = AutoIdeaSwitchEnum.OFF;
        } else if (value === feedProjectModeType.AIMAX) {
            if (formData.bjhType === BJHType.video) {
                formData.aiMaxTradeVersionType = FeedAIMaxTradeVersionType.OFF;
                formData.autoIdeaSwitch = AutoIdeaSwitchEnum.OFF;
            } else if (getEducationIndustry()) {
                formData.aiMaxTradeVersionType = FeedAIMaxTradeVersionType.EDU;
            } else if (getMedecalIndustry()) {
                formData.aiMaxTradeVersionType = FeedAIMaxTradeVersionType.MED;
            } else if (getBusinessIndustry()) {
                formData.aiMaxTradeVersionType = FeedAIMaxTradeVersionType.BUSINESS;
            } else if (
                getShortPlayIndustry()
                && isFeedShortPlayIndustryAimaxUser()
                && [FeedSubjectType.bjh].includes(subject)
                && bjhType === BJHType.shortPlay
                && bidMode === BidModeEnum.TRANS_COST
            ) {
                formData.aiMaxTradeVersionType = FeedAIMaxTradeVersionType.SHORT_PLAY;
            } else {
                formData.aiMaxTradeVersionType = FeedAIMaxTradeVersionType.OFF;
                formData.autoIdeaSwitch = AutoIdeaSwitchEnum.ON;
            }
        }

        if (getAutoOpenTradeList() && initialProjectModeType === feedProjectModeType.CUSTOM) {
            formData.smartControlSwitch = true;
            formData.smartControl = SmartControlEnum.aggressive;
        }
    },
    aiMaxTradeVersionLevel: (value, formData) => {
        if (value && value !== FeedAIMaxEduLevel.NoUse) {
            formData.liftSwitch = LiftSwitchEnum.ON;
            if (formData?.bjhType !== BJHType.video) {
                formData.autoIdeaSwitch = AutoIdeaSwitchEnum.ON;
            }
            const {
                ocpcBidRatioType,
            } = formData.initialData;

            if (isFeedProjectMainflowDefaultCrowdUser()
                && (!ocpcBidRatioType || ocpcBidRatioType === FeedOcpcBidRatioTypeEnum.noUse)
                && (value !== FeedAIMaxEduLevel.Expansion)
                && formData.subject !== FeedSubjectType.bjh
            ) {
                formData.refinedBidSwitch = true;
                formData.ocpcBidRatioMode = 2;
                formData.ocpcBidRatioType = FeedOcpcBidRatioTypeEnum.crowdQualityAll;
                formData.ocpcBidRatioSubType = crowdIdentifyValue.default;
                formData.crowdCoefConfigs = eduCrowdCoefConfigs[value];
            }

            if (getShortPlayIndustry()) {
                formData.smartControl = SmartControlEnum.noop;
                formData.smartControlSwitch = false;
                if (!isBJHShortPlayAiMaxCommonUser()) {
                    // 短剧行业版不支持智能定向扩量
                    formData.liftSwitch = LiftSwitchEnum.OFF;
                }
            }

            if (getMedecalIndustry() && value === FeedAIMaxEduLevel.Aggressive) {
                formData.useLiftBudget = false;
            }

            if (getMedecalIndustry()) {
                formData.smartControlSwitch = false;
                if (value === FeedAIMaxEduLevel.Aggressive) {
                    formData.useLiftBudget = false;
                    formData.smartControl = SmartControlEnum.aggressive;
                }
                else {
                    formData.smartControl = SmartControlEnum.conservative;
                }
            }
        }
        const {aiMaxTradeVersionType} = formData;
        const isMed = value === FeedAIMaxEduLevel.Aggressive
        && aiMaxTradeVersionType === FeedAIMaxTradeVersionType.MED;
        if (isMed) {
            formData.useImmediateLiftBudget = false;
            formData.isUseProjectBudget = UseProjectBudgetEnum.ON;
            formData.budget = 0;
        }
        if (
            value === FeedAIMaxEduLevel.NoUse
            && aiMaxTradeVersionType === FeedAIMaxTradeVersionType.SHORT_PLAY
            && !isBJHShortPlayAiMaxCommonUser()
        ) {
            formData.autoIdeaSwitch = AutoIdeaSwitchEnum.OFF;
            formData.reopenAutoIdeaSwitch = ReopenAutoIdeaSwitchEnum.OFF;
        }
    },
    useLiftBudget: (value, formData) => {
        if (value && formData.isUseProjectBudget !== UseProjectBudgetEnum.ON) {
            formData.isUseProjectBudget = UseProjectBudgetEnum.ON;
            formData.budget = 0;
        }
    },
};
