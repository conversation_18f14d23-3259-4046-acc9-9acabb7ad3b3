/* eslint-disable complexity */
import React, {useCallback, useState} from 'react';
import {useActionPending} from 'huse';
import {useResource} from 'react-suspense-boundary';
import {createReactiveData, FormProvider, useQuickForm} from '@baidu/react-formulator';
import {Button, Skeleton} from '@baidu/light-ai-react';
import {ProviderConfig, Toast} from '@baidu/one-ui';
import SuspenseBoundary from 'commonLibs/suspenseBoundary';
import {postMessageFunc, PostMessageEventType, PostMessageScene} from 'feedCommonLibs/utils/postMessage';
import {FeedSubjectType} from '@/dicts/subject';
import {useAdRoute} from '@/modules/Ad/routes';
import {PageType} from '@/dicts/pageType';
import Header from '@/components/Header';
import {generateTimestampedString} from '@/utils/date';
import {Instructions} from '@/utils/instructions';
import {PRODUCT} from '@/dicts/campaign';
import {makeLogger} from '@/modules/Ad/ManageCenter/common/utils';
import {levelTypeMapForLogOrSwr} from '@/hooks/request/levelType';
import {useFeedHongYanFlagTemporary} from '@/hooks/feedFlags';
import {formatFeedProjectData2Api} from '@/components/Project/FeedEditor/util';
import {addFeedProject} from '@/api/feedProject';
import {BidModeEnum, FeedAIMaxEduLevel, FeedAIMaxTradeVersionType, SaleType} from '@/interface/aixProject/feedProject';
import {isInFeedIframe} from '@/utils/iframe';
import {BJHType, checkIsBjhShortPlaySimplifiedScene} from '@/components/Project/FeedEditor/BJHType/config';
import {checkIsMdeicalMustBindCatalog} from '@/config/feed/product';
import {AutoIdeaSwitchEnum, LiftSwitchEnum} from '@/dicts/project';
import {SmartControlEnum} from '@/components/List/ProjectList/AiAgent/config';
import {getShortPlayIndustry} from '@/components/List/ProjectList/AiAgent/util';
import {isFeedShortPlayIndustryAimaxUser} from '@/utils/getFlag';
import {TRANS_TYPE_ENUM} from '@/config/feed/trans';
import {useFormulatorLogMethods} from '@/hooks/funnel/formulatorLog';
import {useFormFieldValue} from '@/hooks/form/useFormFieldValue';
import {spyxTrade, getAutoOpenTradeList} from '@/config/feed/trade';
import globalData from '@/utils/globalData';
import {isFeedSpyxTrade} from '@/utils/getFeedFlag';
import {getCurrentTime} from '@/utils/date';
import {sendMonitor} from '@/utils/logger';
import {getSubjectIds} from './subject';
import {initialFormData, ProcessStepsInPortal} from './config/form';
import {PayToWatchTransForm} from './config/watch';
import {FeedProjectContextProvider} from './context';
import './index.global.less';

const FIELD = levelTypeMapForLogOrSwr.manageProjectList;
const sendLog = makeLogger({level: FIELD, source: 'operation_bar', 'extra_params': PRODUCT.FEED});

const pageQuery = {
    aixProduct: PRODUCT.FEED,
};

function spyxMonitor(res: any) {
    const [projectFeedType = {}] = res || [];
    const {tradeId2nd} = globalData.get('feedBasicData') || {};
    const isSpyxTrade = spyxTrade.includes(+tradeId2nd) && isFeedSpyxTrade();
    const time = getCurrentTime();
    if (projectFeedType?.smartControl && (isSpyxTrade || getAutoOpenTradeList())) {
        sendMonitor('click', {
            level: isSpyxTrade ? 'addFeedProjectSpyx' : 'addFeedProjectTrade',
            info: time,
            'extra_params': projectFeedType?.projectFeedId,
        });
    }
}

function ProjectEditor() {
    useFeedHongYanFlagTemporary();
    const {linkTo} = useAdRoute();
    const [Form, {validateFields, setFieldsError, getFieldError}] = useQuickForm();
    const [projectFeedName] = useState(() => `轻舸投放项目_${generateTimestampedString()}`);
    const initialSubject = getSubjectIds({isNew: true})?.[0] || FeedSubjectType.salesLead;
    const initialBjhTypeData = [FeedSubjectType.bjh].includes(initialSubject) ? {bjhType: BJHType.shortPlay} : {};
    const isBjhShortPlaySimplifiedScene =
        checkIsBjhShortPlaySimplifiedScene({subject: initialSubject, bjhType: initialBjhTypeData.bjhType});
    const isBjhShortPlayUser = getShortPlayIndustry() && isFeedShortPlayIndustryAimaxUser();
    const initialOcpcData = isBjhShortPlaySimplifiedScene
        ? {
            ocpc: {
                transType: TRANS_TYPE_ENUM.PAY_TO_WATCH,
                transTypePreview: TRANS_TYPE_ENUM.PAY_TO_WATCH,
                enableOptimizeDeepTransForBjh: true,
                transFrom: PayToWatchTransForm,
            },
            bidMode: BidModeEnum.TRANS_COST,
        }
        : {};
    const initialAiMaxData = isBjhShortPlayUser && isBjhShortPlaySimplifiedScene
        ? {
            aiMaxTradeVersionType: FeedAIMaxTradeVersionType.SHORT_PLAY,
            aiMaxTradeVersionLevel: FeedAIMaxEduLevel.Aggressive,
            liftSwitch: LiftSwitchEnum.ON,
            autoIdeaSwitch: AutoIdeaSwitchEnum.ON,
            smartControl: SmartControlEnum.CONSERVATIVE,
        }
        : {};
    const [[{config: formConfig, initialData, verificationData}, medicCatalogData]] = useResource(
        initialFormData,
        {
            feedProjectType: {
                isCreate: true,
                subject: initialSubject,
                ...initialBjhTypeData,
                ...([FeedSubjectType.salesLead].includes(initialSubject) ? {
                    saleType: SaleType.default,
                    useProductNew: checkIsMdeicalMustBindCatalog(),
                } : {}),
                ...([FeedSubjectType.salesLead].includes(initialSubject) ? {saleType: SaleType.default} : {}),
                ...initialAiMaxData,
                ...initialOcpcData,
                projectFeedName,
                campaignFeedIds: [],
                aiTag: 1,
            },
        }
    );

    const [reactiveData] = useState(() => createReactiveData(initialData));

    const subject = useFormFieldValue(reactiveData, 'subject');
    const {
        logOnSubmitClick,
        logOnSubmitSuccess,
        logOnSubmitFail,
        logOnCancelClick,
        logOnAreaClick,
    } = useFormulatorLogMethods({
        formData: reactiveData,
        source: isInFeedIframe ? 'feedNewProjectForm' : 'qinggeFeedNewProjectForm',
        shouldSendChangeLog: true,
        config: {
            commonTrackParams: {
                marketingtargetid: subject,
            },
        },
        getFieldError,
    });

    const onSaveProject = async () => {
        let result = null;

        try {
            logOnSubmitClick();
            result = await validateFields({shouldReuseResults: true});
            logOnSubmitSuccess();
        } catch (error: any) {
            logOnSubmitFail(error);
            const errors = (Object.values(error?._detail || {})?.[0] || []) as string[];
            if (errors[0]) {
                Toast.error({content: errors[0] || '请检查表单是否填写正确'});
            }
            throw error;
        }

        const projectFeedType = formatFeedProjectData2Api({formData: result});
        try {
            const res = await addFeedProject({
                projectFeedTypes: [projectFeedType],
            });
            sendLog({event: 'create'});
            spyxMonitor(res);
            if (isInFeedIframe) {
                postMessageFunc({
                    scene: PostMessageScene.PROJECT,
                    eventType: PostMessageEventType.ON_SAVE_SUCCESS,
                });
            } else {
                linkTo(PageType.ProjectList, {query: pageQuery});
            }
        } catch (error: any) {
            const {errors = []} = error;
            const errorMap = errors.reduce(
                (cur: Record<string, string>, item: Record<string, string>) => ({
                    ...cur,
                    [item.field]: [item.message],
                }),
                {}
            );
            setFieldsError(errorMap);
        }
    };

    const [onSave, pendingCount] = useActionPending(onSaveProject);

    const onCancel = useCallback(() => {
        if (isInFeedIframe) {
            postMessageFunc({
                scene: PostMessageScene.PROJECT,
                eventType: PostMessageEventType.ON_CANCEL,
            });
        } else {
            linkTo(PageType.ProjectList, {query: pageQuery});
        }
        logOnCancelClick();
    }, [linkTo, logOnCancelClick]);

    return (
        <>
            <ProcessStepsInPortal formData={reactiveData} fields={formConfig.fields} />
            <FormProvider value={{inputErrorClassName: 'one-ai-invalid'}}>
                <FeedProjectContextProvider value={{medicCatalogData}}>
                    <div onClick={logOnAreaClick}>
                        <div className="create-project-header">
                            新建项目
                        </div>
                        <Form
                            data={reactiveData}
                            verificationData={verificationData}
                            config={formConfig}
                            className="use-rf-preset-form-ui use-label-top"
                        >
                            {/* <Debug /> */}
                        </Form>
                        <div className="save-footer">
                            <Button variant="primary" onClick={onSave} loading={!!pendingCount}>确认并新建项目</Button>
                            <Button variant="normal" onClick={onCancel}>取消</Button>
                        </div>
                    </div>
                </FeedProjectContextProvider>
            </FormProvider>
        </>
    );
}

function CreateProject({instructions}: {instructions: Instructions}) {
    const {linkTo} = useAdRoute();
    return (
        <>
            {
                !isInFeedIframe && (
                    <Header
                        instructions={instructions}
                        onClose={() => linkTo(PageType.ProjectList, {query: pageQuery})}
                    />
                )
            }
            <ProviderConfig theme="light-ai">
                <div className="create-project-container" id="create-project-container">
                    <SuspenseBoundary
                        pendingFallback={(
                            <div className="project-form-steps-v2">
                                <Skeleton.Paragraph active />
                            </div>
                        )}
                    >
                        <div id="create-project-process-steps" />
                    </SuspenseBoundary>
                    <div className="create-project-form" id="create-project-form">
                        <SuspenseBoundary loading={{tip: '正在初始化您的项目'}}>
                            <ProjectEditor />
                        </SuspenseBoundary>
                    </div>
                </div>
            </ProviderConfig>
        </>
    );
}

export default CreateProject;
