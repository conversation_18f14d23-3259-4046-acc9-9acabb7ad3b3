@import 'src/styles/mixin/promotionMain.global.less';
@import 'src/components/List/ProjectList/AiAgent/index.global.less';

@hidden-bottom-field: aiMaxModeSwitch, liftBudgetSwitch, budgetType, isUseOcpcBidRatio,aiMaxMarketingLevel, autoOrientStatus, autoCreativeStatus, smartControlSwitch, isUseAgentRotate;

.create-project-container {
    width: 100%;
    display: flex;
    gap: 24px;
    flex: 1;
    overflow-y: auto;

    --rf-builtin-group-margin-top: 16px;

    .qg-editor-form-group,
    .fc-new-project-group {
        .qg-editor-form-group-title {
            border-radius: 10px;
            padding: 16px 24px;
        }

        .group-title {
            font-weight: 500;
            font-size: 18px;
            border-radius: 10px;
            padding: 16px 24px;
            color: #0E0F11;
        }
    }

    #create-project-process-steps .steps-v2 {
        height: 100%;
    }

    .project-form-steps {
        width: auto !important;
        padding: 24px;
        position: sticky;
        top: 0;
    }

    .create-project-form {
        position: relative;
        flex: 1;
        overflow: auto;

        --rf-builtin-group-border-radius: 10px;

        .rf-form-item-label-content-text {
            font-weight: 500;
            color: #0E0F11;
            line-height: 20px;
            margin-bottom: 6px;
        }

        .create-project-title {
            font-weight: 500;
            font-size: 20px;
            padding: 16px 24px;
            color: #0E0F11;
            background-color: #fff;
            border-radius: 10px;
            margin-top: 24px;
        }

        .market-target-group {
            border-radius: 10px;
            padding-bottom: 24px;

            &.market-target-group-collapsed {
                padding-bottom: 1px;

                .rf-form-item-builtin-group-fields {
                    display: none;
                }
            }
        }

        #project-info {
            border-radius: 0 0 10px 10px;
            padding-top: 5px;
            transform: translateY(-6px);
            margin-bottom: -6px;

            .rf-form-item-builtin-group-header {
                display: none;
            }
        }
    }

    .ad-main-project-include-campaign-container {
        .bindIds-editor {
            display: flex;

            .selected-campaign-list {
                margin-left: 12px;
            }
        }

        .clue-campaign-text {
            display: flex;
            padding: 8px 12px;
            align-items: center;
            gap: 6px;
            align-self: stretch;
            border-radius: 6px;
            background: rgba(109, 159, 247, 0.07);
            margin-top: 12px;
            color: #545B66;

            .label {
                font-weight: 500;
                color: #0E0F11;
            }
        }

        .unbind-alert {
            margin-top: 8px;
        }

        .campaign-with-auto-product-text {
            color: #545B66;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            margin-top: 12px;
        }
    }

    .fc-aimax-group {
        width: 770px;
        margin-bottom: 24px;

        .one-ai-transfer {
            width: 720px;
        }

        --rf-form-item-label-width: 100px;
        --rf-form-item-margin-bottom: 16px;

        .process-classes(@i) when (@i > 0) {
            @class: extract(@hidden-bottom-field, @i);
            .process-classes(@i - 1);
            [class*="rf-form-item-@{class}_"] {
                padding-bottom: 0;

                .rf-form-item-messages {
                    display: none;
                }
            }
        }
        .process-classes(length(@hidden-bottom-field));
    }

    .deep-trans-type-mode {
        display: flex;
        align-items: flex-start;
        position: relative;
        padding-bottom: 24px;
        flex-direction: column;

        .deep-trans-type-mode-title {
            position: relative;
            display: flex;
            align-items: center;
            font-size: 14px;
            width: 140px;
            font-weight: 500;
            color: #0E0F11;
            line-height: 20px;
            margin-bottom: 6px;
        }

        .deep-trans-type-mode-body {
            display: inline-block;

            .deep-trans-type-mode-desc {
                color: #848b99;
                margin: 0 0 8px 0;
            }
        }
    }

    .ocpc-bid-multi-switch {
        position: absolute;
        top: 8px;
        left: 530px;
    }

    .multi-bid-card {
        margin-top: 4px;

        .trans-type-multi-bid-content {
            .trans-type-multi-bid-edit {
                margin-top: 8px;
            }

            .error-message {
                font-size: 14px;
                display: inline-block;
                color: #cc1800;
                margin-top: 4px;
            }
        }
    }

    .new-campaign-account-detail-container.without-price-factor {
        margin-top: 0;
    }

    .save-footer {
        padding: 16px 24px;
        margin-top: 24px;
        margin-bottom: 24px;
        background-color: #fff;
        border-radius: 10px;

        button + button {
            margin-left: 16px;
        }
    }

    .fc-aimax-group-bg {
        width: 792px;
        border-radius: 10px;
        border: 1px solid rgba(102, 146, 222, 0.14902);
        display: inline-block;

        .rf-form-item {
            &:first-child {
                padding-bottom: 0;
            }

            .rf-form-item-input-component-container {
                .aimax-mode-switch {
                    width: 790px;
                }

                .ai-max-marketing-container {
                    width: 790px;
                }

                // .rf-form-item-messages {
                //     display: none;
                // }
            }
        }
    }

    .fc-aimax-group-new {
        position: relative;
        cursor: pointer;
        display: flex;
        padding: 15px;
        overflow: hidden;
        border: 1px solid rgba(102, 146, 222, 0.14902);
        border-left: 0 none;
        border-right: 0 none;

        &:hover {
            background: rgba(246, 247, 250, 1);
        }

        --rf-form-item-label-width: 100px;
        --rf-form-item-margin-bottom: 16px;

        .process-classes(@i) when (@i > 0) {
            @class: extract(@hidden-bottom-field, @i);
            .process-classes(@i - 1);
            [class*="rf-form-item-@{class}_"] {
                padding-bottom: 0;

                .rf-form-item-messages {
                    display: none;
                }
            }
        }
        .process-classes(length(@hidden-bottom-field));
    }

    .fc-aimax-group-new-active {
        border-bottom: 1px solid rgba(102, 146, 222, 0.14902);
    }

    .aimax-div-group {
        margin-top: -1px;
        cursor: pointer;
        background-color: #fff;
        border-radius: 10px;

        .use-rf-preset-form-ui {
            .rf-form-item {
                .rf-form-item-messages {
                    display: none;
                }
            }
        }

        .aimax-div-group-title {
            position: relative;
            line-height: 20px;
            font-size: 14px;
            font-weight: 600;
            padding-bottom: 6px;

            .aimax-div-group-title-line {
                position: absolute;
                left: -15px;
                top: 2px;
                height: 15px;
                width: 4px;
                border-radius: 2px;
                background: rgba(58, 91, 253, 1);
            }

            .aimax-div-group-title-index {
                color: rgba(58, 91, 253, 1);
            }

            .aimax-div-group-title-tag {
                display: inline-block;
                font-size: 12px;
                font-weight: normal;
                color: rgba(58, 91, 253, 1);
                background: rgba(191, 214, 255, 0.6);
                border-radius: 2px;
                padding: 1px 7px;
                margin-left: 10px;
            }
        }

        .aimax-div-group-desc {
            min-height: 52px;
            font-size: 14px;
            line-height: 26px;
            color: rgba(132, 139, 153, 1);
        }

        .aimax-div-group-content {
            flex: 1;
            cursor: pointer;
        }

        .aimax-div-group-switch {
            display: flex;
            padding-left: 40px;
            width: 150px;

            .aimax-div-group-switch-percent {
                padding-right: 6px;

                span {
                    color: rgba(58, 91, 253, 1);
                }
            }
        }

        .aimax-div-icon {
            position: absolute;
            right: 25px;
            top: 70px;
            font-size: 16px;
            color: #848b99;
            font-weight: 600;
            cursor: pointer;
        }
    }
}

.lite-project-form-entry {
    background-image: url('https://fc-feed.bj.bcebos.com/aix%2Flite-project-entry-bg.png');
    max-width: 790px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    border-radius: 10px;
    margin-top: 16px;

    .lite-project-form-entry-title {
        display: flex;
        align-items: center;
        gap: 4px;

        .text {
            font-family: FZPinShangHeiS-B-GB;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }
    }
}
