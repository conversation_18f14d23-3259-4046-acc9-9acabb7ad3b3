import {PostMessageEventType, PostMessageScene} from 'commonLibs/utils/postMessage';
import {useCallback, useEffect} from 'react';
import {useURLQuery} from '@/hooks/tableList/query';
import {getUserId} from '@/utils';
import SingletonIframe from '@/components/common/iframe/SingletonIframe';
import {PageType} from '@/dicts/pageType';
import {PRODUCT} from '@/dicts/campaign';
import {PLATFORM_ENUM} from '@/dicts';
import {useAdRoute} from '@/modules/Ad/routes';
import Header from '@/components/Header';
import {Instructions} from '@/utils/instructions';
import {FcAdType} from '@/dicts/fcCampaign';
import {FC_MARKET_TARGET} from '@/dicts/marketingTarget';
import {sendMainLog} from '../util';

const pageQuery = {
    productLine: JSON.stringify([PLATFORM_ENUM.FC, PRODUCT.FC]),
};

function getFcAdgroupIframePath({marketingTargetId, adType, campaignId}: {
    marketingTargetId: FC_MARKET_TARGET;
    adType?: FcAdType;
    campaignId?: string;
}) {
    const userId = getUserId();
    if (adType && +adType === FcAdType.DCA) {
        return `/fc/managecenter/new/ws_campaign/${campaignId}/ws_adgroup/separate_create/user/${userId}`;
    }
    if (+marketingTargetId === FC_MARKET_TARGET.APP) {
        return `/fc/managecenter/new/ws_campaign/${campaignId}/ws_adgroup/create/user/${userId}/mt/1`;
    }
    if (+marketingTargetId === FC_MARKET_TARGET.COMMODITY) {
        return `/fc/manage/newCommodity/user/${userId}/plan/${campaignId}/unit`;
    }
    if (+marketingTargetId === FC_MARKET_TARGET.SHOP) {
        // eslint-disable-next-line max-len
        return `/fc/managecenter/new/ws_campaign/${campaignId}/ws_adgroup/create/user/${userId}/mt/${FC_MARKET_TARGET.SHOP}`;
    }
    // eslint-disable-next-line max-len
    return `/fc/managecenter/new/campaign/${campaignId}/adgroup/create/user/${getUserId()}?marketingTargetId=${marketingTargetId}`;
}

function NewAdgroup({instructions}: {instructions: Instructions}) {
    const [{campaignId, marketingTargetId, preLevelDataName, adType}] = useURLQuery<{
        campaignId?: string; marketingTargetId?: FC_MARKET_TARGET; preLevelDataName?: string; adType?: FcAdType;
    }>();
    const {linkTo} = useAdRoute();

    const backToList = useCallback(() => {
        linkTo(PageType.AdgroupList, {
            query: {
                ...pageQuery,
                ...((campaignId && preLevelDataName) ? {campaignId, preLevelDataName} : {}),
            },
        });
    }, [campaignId, linkTo, preLevelDataName]);

    useEffect(() => {
        const receiveMessage = (event: {
            data: {
                scene: typeof PostMessageScene;
                eventType: typeof PostMessageEventType;
                data?: {behavior: string};
            };
        }) => {
            const {scene, eventType, data} = event.data;
            if ([PostMessageScene.ADD_ADGROUP, PostMessageScene.ADD_CREATIVE].includes(scene)
                && data?.behavior === PostMessageEventType.ON_GO_BACK) {
                backToList();
            }
            sendMainLog({eventType, scene});
        };
        window.addEventListener('message', receiveMessage);
        return () => window.removeEventListener('message', receiveMessage);
    }, [linkTo, campaignId, backToList]);

    const iframePath = getFcAdgroupIframePath({marketingTargetId, adType, campaignId});

    return (
        <>
            <Header
                instructions={instructions}
                onClose={backToList}
            />
            <SingletonIframe
                iframePath={iframePath}
                style={{
                    height: 'calc(100vh - 68px)',
                }}
            />
        </>

    );
}
export default NewAdgroup;
