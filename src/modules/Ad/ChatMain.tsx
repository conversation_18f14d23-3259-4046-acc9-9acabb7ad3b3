import classNames from 'classnames';
import {Instructions} from '@/utils/instructions';
import AiChat from '@/components/AiChat';
import {InitialPayloadState} from '@/components/AiChat/interface';
import {GlobalContext} from '@/hooks/contexts/globalContext';
import <PERSON><PERSON>Footer from '@/components/CpuFooter';
import {Activities} from './Activities';
import './index.global.less';


export default function ChatMain({
    initialPrompt, initialPayload, globalSceneData, instructions,
}: {
    initialPrompt: string;
    initialPayload?: InitialPayloadState;
    instructions: Instructions;
    globalSceneData: any;
}) {

    const classnames = classNames({
        'aix-ad-main': true,
    });
    const data = {...globalSceneData, instructions};

    return (
        <div className={classnames}>
            {/* context */}
            <GlobalContext.Provider value={data}>
                <Activities globalData={globalSceneData} instructions={instructions} />
            </GlobalContext.Provider>
            <div className="chat-area">
                <AiChat
                    initialPrompt={initialPrompt}
                    initialPayload={initialPayload}
                    instructions={instructions}
                />
                <CpuFooter />
            </div>
        </div>
    );
}
