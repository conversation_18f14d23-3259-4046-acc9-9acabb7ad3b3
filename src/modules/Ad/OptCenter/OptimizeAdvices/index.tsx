import {lazy, useCallback, useState, useImperativeHandle, forwardRef, Ref, useMemo, useEffect} from 'react';
import {Drawer, Radio, ProviderConfig} from '@baidu/one-ui';
import queryString from 'query-string';
import {useSearchParamState} from '@huse/router';
import {useControl} from '@/hooks/externalControl';
import {useGlobalProductContext} from '@/hooks/productLine';
import {getFcUrl, getFeedUrl} from '@/utils/format/url';
import AdRoutes, {useAdRoute} from '@/modules/Ad/routes';
import {appendQuery} from '@/utils/route';
import {getEnvPort, getUserId} from '@/utils';
import {formatString} from '@/utils/string';
import {modifyQuery} from '@/utils/route';
import {ADVICE_KEY} from '@/dicts/optCenter';
import {Instructions, useRegisterInstructions} from '@/utils/instructions';
import {ASSIST_INSTRUCTION_KEY_MAP} from '@/modules/Ad/config';
import {PLATFORM_ENUM, PLATFORM_NAME_MAP, isHasFeedAuth} from '@/dicts';
import {OptimizeAdviceSource} from '@/dicts/optimizeAdvice';
import {sendMonitor} from '@/utils/logger';
import {PageType} from '@/dicts/pageType';
import {getDiagnosisMonitorParams} from '@/modules/Ad/OptCenter/AdvertiseDiagnosis/util/monitor';
import {sendScreenRecord} from '@/hooks';
import {useURLHash} from '@/hooks/url/useHash';
import './index.global.less';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;
const OptimizeAdvice = lazy(() => import('commonApps/optimizeCenter/dashboardCards'));

const getFeedOptimizeUrl = () => {
    const optUrl = getFeedUrl('/nirvana/main.html?userid=${userId}#/feed/index~module=manage&page=optcenterList');

    const query = queryString.parse(location.search);
    const tabFromQuery = query.tab;
    const adviceKey = query.adviceKey;
    const source = query.source;
    const adviceType = query.adviceType;

    const optUrlWithQinggeSign = appendQuery(optUrl, {
        in: 'iframe', 'host_app': 'qingge',
        ...(tabFromQuery ? {tab: tabFromQuery} : {}),
        ...(adviceType ? {adviceType} : {}),
        ...(adviceKey ? {adviceKey} : {}),
        ...(source ? {source} : {})
    });

    // 注：这里必须经过modifyQuery 函数对URL进行decode 处理：
    // 因为前面appendQuery会把query encode, 而feed优化中心页面URL比较特殊，它包括hash串 #/feed/index~module=manage。
    // 该hash 串不能进行url encode，feed平台识别不了这个路径，从而进行重定向，最终达不到预期url。
    return modifyQuery(optUrlWithQinggeSign, (query: Record<string, any>) => query, true);
};
interface OptCenterDetailControlMethods {
    open: (params: string | {path?: string, title?: string, finalUrl?: string}) => void;
    close: () => void;
}

function OptCenterDetailIframeByDrawer(
    props: {
        instructions?: Instructions;
    },
    ref: Ref<OptCenterDetailControlMethods>
) {
    const {instructions} = props;
    const [optCenterDetailUrl, setOptCenterDetailUrl] = useState('');
    const [iframeTitle, setIframeTitle] = useState('优化建议详情');

    const close = useCallback(() => {
        instructions?.[ASSIST_INSTRUCTION_KEY_MAP.AfterCloseOptCenterDrawer]?.();
        setOptCenterDetailUrl('');
    }, [instructions]);

    const open: OptCenterDetailControlMethods['open'] = useCallback(
        params => {
            const {path = '', title, finalUrl} =
                typeof params === 'string' ? {path: params, title: '优化建议详情', finalUrl: ''} : params;

            const url = finalUrl ? finalUrl : formatString(getFcUrl(path), {
                userId: getUserId(),
                port: getEnvPort(),
            });
            const sourceFromProps = new URLSearchParams(url.split('?')[1]).get('source') || '';
            const {source: sourceFromUrl = ''} = queryString.parse(location.search);
            setOptCenterDetailUrl(appendQuery(url, {
                in: 'iframe', 'host_app': 'qingge',
                source: [sourceFromProps, sourceFromUrl, OptimizeAdviceSource.OPT_CENTER].find(Boolean),
            }));
            setIframeTitle(title || '优化建议详情');
        },
        []
    );

    useImperativeHandle(ref, () => ({open, close}));

    return (
        <Drawer
            visible={!!optCenterDetailUrl}
            width={1200}
            onClose={close}
            destroyOnClose
            title={iframeTitle}
        >
            <iframe
                src={optCenterDetailUrl}
                frameBorder="0"
                width="100%"
                height="100%"
            />
        </Drawer>
    );
}

const OptCenterDetailDrawer = forwardRef(OptCenterDetailIframeByDrawer);

const noDetailPageAdviceKeys: string[] = [
    ADVICE_KEY.aiMax,
    ADVICE_KEY.autoRules,
    ADVICE_KEY.fcKeywordRanking,
    ADVICE_KEY.modFeedBudgetForConversion,
    ADVICE_KEY.modAccountBudgetFeed,
    ADVICE_KEY.modCampaignBudgetFeed,
];

export function OptCenterDetailIframeDrawer({instructions}: {instructions: Instructions}) {
    const [
        OptCenterDetail,
        {open: launchOptCenterDetailInIframe},
    ] = useControl<OptCenterDetailControlMethods, Record<string, any>>(OptCenterDetailDrawer);
    const userId = getUserId();
    const [sourceParam, setSourceParam] = useSearchParamState('source');
    useEffect(
        () => {
            const query = queryString.parse(location.search);
            const {version, ...pureQuery} = query;

            const advicePath = AdRoutes.getPathByName?.(PageType.OptimizeAdvices);

            if (
                query.adviceKey && location.pathname === advicePath
                && !noDetailPageAdviceKeys.includes(query.adviceKey)
            ) {
                const url = version === 'fuyao'
                    ? appendQuery(`/fc/manage/tools/user/${userId}/optimizeAdviceDetail`, {adviceKey: query.adviceKey})
                    : appendQuery(`/fc/managecenter/optimizeAdviceDetail/user/${userId}`, {...pureQuery});
                launchOptCenterDetailInIframe(url);
            }
        },
        []
    );

    useEffect(() => {

        // iframe 中触发的事件
        // window.parent.postMessage({
        //     type: 'openOptCenterDetail',
        //     url: optDetailURL
        // }, '*');
        // 监听 iframe 中触发的事件

        const listener: (event: MessageEvent) => void = event => {
            const {data} = event;
            if (
                data.type === 'openOptCenterDetail'
            ) {
                launchOptCenterDetailInIframe({path: data.url});
            }
        };
        window.addEventListener('message', listener);
        return () => {
            window.removeEventListener('message', listener);
        };
    }, []);

    const [, {updateHash, removeHash}] = useURLHash();
    useRegisterInstructions(instructions, [
        [ASSIST_INSTRUCTION_KEY_MAP.LaunchSugUrl, (params: any) => {
            const {
                link,
                linkType,
                iframeTitle,
                anchor,
                adviceType,
                diagnosisLog,
            } = params;


            if (anchor && location.pathname.includes('/ad/optCenter')) {
                updateHash(anchor, {location});
                // 点击锚点埋点
                diagnosisLog && diagnosisLog({
                    target: anchor,
                    item: adviceType,
                    ...getDiagnosisMonitorParams('diagnosisConsumptionDropMarkClick'),
                });

                setTimeout(() => removeHash({location}), 100);
                return; // 这里不走后续的逻辑
            }


            if (linkType === 'newPage') {
                window.open(params.link);
            }
            else if (linkType === 'iframe') {
                launchOptCenterDetailInIframe({finalUrl: link, title: iframeTitle});
            }

            // 非埋点的，算采纳点击埋点
            params.adviceType && diagnosisLog && diagnosisLog({
                ...getDiagnosisMonitorParams('diagnosisConsumptionDropAdopt'),
                target: params.text, // link 或者 sug 里的文本内容
                item: params.adviceType, // 优化建议类型
            });
        }],
        [ASSIST_INSTRUCTION_KEY_MAP.LaunchOptCenterDrawer, (url: string) => {
            launchOptCenterDetailInIframe(url);
        }],
        [ASSIST_INSTRUCTION_KEY_MAP.AfterCloseOptCenterDrawer, () => {
            // 从首页跳转来url会带上source=OptimizeAdviceSource.OVERVIEW并自动打开详情。关闭详情后，source应该置为OptimizeAdviceSource.OPT_CENTER
            const advicePath = AdRoutes.getPathByName?.(PageType.OptimizeAdvices);
            if (
                location.pathname === advicePath
                && Number(sourceParam) === OptimizeAdviceSource.OVERVIEW
            ) {
                setSourceParam(`${OptimizeAdviceSource.OPT_CENTER}`);
            }
        }],
    ]);

    return <OptCenterDetail instructions={instructions} />;
}


export default function OptimizeAdvices({instructions}: {instructions: Instructions}) {
    const [
        OptCenterDetail,
        {open: launchOptCenterDetailInIframe},
    ] = useControl<OptCenterDetailControlMethods, Record<string, any>>(OptCenterDetailDrawer);

    const {linkToChat: linkToChat_, linkTo} = useAdRoute();

    const linkToChat = useCallback(
        async (role: 'user', prompt: string, info: Record<string, any>) => {
            await linkToChat_([role, prompt, info]);
        },
        [linkToChat_]
    );

    const launchAccountSetting = useCallback(
        () => {
            instructions?.[ASSIST_INSTRUCTION_KEY_MAP.LaunchAccountSettingEditor]?.();
        },
        []
    );

    const userId = getUserId();
    const linkToQinggeNewCampaign = useCallback(
        async () => {
            await linkTo(PageType.NewCampaign, {
                query: userId ? {userId} : {},
            });
        },
        [linkTo]
    );

    const linkToQinggeNewCreative = useCallback(
        async () => {
            await linkTo(PageType.NewCreative, {
                query: userId ? {userId} : {},
            });
        },
        [linkTo]
    );

    const optMethods = useMemo(
        () => ({
            launchOptCenterDetailInIframe, linkToChat,
            launchAccountSetting, linkToQinggeNewCampaign,
            linkToQinggeNewCreative,
        }),
        [
            launchOptCenterDetailInIframe, linkToChat, launchAccountSetting,
            linkToQinggeNewCampaign, linkToQinggeNewCreative,
        ]
    );

    const {product: platform} = useGlobalProductContext();

    useEffect(() => {
        sendScreenRecord('optimize_advice_load');
    }, []);

    return (
        <ProviderConfig theme="light-ai">
            {
                platform === PLATFORM_ENUM.FC && (
                    <OptimizeAdvice
                        className="optimize-advices-in-qingge"
                        optMethods={optMethods}
                        source={OptimizeAdviceSource.OPT_CENTER}
                    />
                )
            }
            {
                platform === PLATFORM_ENUM.FEED && (
                    <iframe
                        className="feed-optimize-advices-in-qingge"
                        src={getFeedOptimizeUrl()}
                        width="100%"
                        height="100%"
                    />
                )
            }
            <OptCenterDetail
                instructions={instructions}
            />
        </ProviderConfig>
    );
}
