import {Button} from '@baidu/one-ui';
import {IllustrationBlank} from 'dls-illustrations-react';
import {useCallback} from 'react';
import {PLATFORM_ENUM} from '@/dicts';
import {useGlobalProductContext} from '@/hooks/productLine';
import './index.global.less';

export enum NotSupportedType {
    DiagnosisDashboard = 'diagnosisDashboard',
    AdvertiseDiagnosis = 'advertiseDiagnosis',
    IndustryInsights = 'industryInsights',
}

const NotSupportedMap = {
    [NotSupportedType.DiagnosisDashboard]: '诊断概览',
    [NotSupportedType.AdvertiseDiagnosis]: '问题排查',
    [NotSupportedType.IndustryInsights]: '行业洞察',
};

interface NotSupportedProps {
    type: NotSupportedType;
}

const compCls = 'not-supported';

export default function NotSupported(props: NotSupportedProps) {
    const {type} = props;
    const {setProduct} = useGlobalProductContext();
    const onClick = useCallback(() => {
        setProduct(PLATFORM_ENUM.FC);
    }, []);

    return (
        <div className={compCls}>
            <IllustrationBlank />
            <div className={`${compCls}-label`}>信息流暂不支持{NotSupportedMap[type]}功能</div>
            <div className={`${compCls}-desc`}>将在近期开放使用，敬请期待!</div>
            <Button theme="light-d20" type="strong" onClick={onClick}>返回搜索推广</Button>
        </div>
    );
}
