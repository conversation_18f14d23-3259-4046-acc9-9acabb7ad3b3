import {CSSProperties, Suspense, useEffect, useState} from 'react';
import {ProviderConfig, Tabs} from '@baidu/one-ui';
import {PageTips} from '@baidu/one-ui-pro';

import {Instructions} from '@/utils/instructions';
import {PLATFORM_ENUM} from '@/dicts';
import {useGlobalProductContext} from '@/hooks/productLine';
import {useURLQuery} from '@/hooks/tableList/query';
import NotSupported, {NotSupportedType} from '../common/NotSupported';
import {DIAGNOSIS_TABS_ENUM, diagnosisTabsOptions} from './tabsContent';
import './index.global.less';

const TabPane = Tabs.TabPane;

const {Loading} = PageTips;

export default function AdvertiseDiagnosis({instructions}: {instructions: Instructions}) {

    const [{tab}, {setQuery}] = useURLQuery<{
        tab?: string;
    }>();
    const [activeKey, setActiveKey] = useState<DIAGNOSIS_TABS_ENUM>(
        tab as DIAGNOSIS_TABS_ENUM || diagnosisTabsOptions[0].key
    );

    useEffect(() => {
        tab && setActiveKey(tab as DIAGNOSIS_TABS_ENUM);
    }, [tab]);

    const {product} = useGlobalProductContext();
    if (product === PLATFORM_ENUM.FEED) {
        return <NotSupported type={NotSupportedType.AdvertiseDiagnosis} />;
    }

    return (
        <ProviderConfig theme="light-ai">
            <Tabs
                activeKey={activeKey}
                onChange={key => {
                    setActiveKey(key as DIAGNOSIS_TABS_ENUM);
                    setQuery({tab: key});
                }}
                style={{'--dls-tab-menu-padding': '0px'} as CSSProperties}
                className="advertise-diagnosis-tabs"
                hideSpace
                size="medium"
            >
                {
                    diagnosisTabsOptions
                        .filter(item => (item.getIsVisible ? item.getIsVisible() : true))
                        .map(item => (
                            <TabPane
                                tab={
                                    <>
                                        {item.label}
                                        {item.badgeType && (
                                            <span className="badge-new">
                                                new
                                            </span>
                                        )}
                                    </>
                                }
                                key={item.key}
                            />
                        ))
                }
            </Tabs>

            <div className="advertise-diagnosis-container">
                {
                    diagnosisTabsOptions.map(item => (
                        <div key={item.key}>
                            {
                                activeKey === item.key && (
                                    <Suspense fallback={<Loading />}>
                                        <item.component instructions={instructions} />
                                    </Suspense>
                                )
                            }
                        </div>
                    ))
                }
            </div>
        </ProviderConfig>
    );
}
