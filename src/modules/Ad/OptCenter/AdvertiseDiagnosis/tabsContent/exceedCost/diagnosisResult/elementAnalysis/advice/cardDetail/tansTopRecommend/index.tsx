
import {useResource} from 'react-suspense-boundary';
import dayjs from 'dayjs';
import {DetailBoundary} from '@/modules/Ad/OptCenter/AdvertiseDiagnosis/components/cardDetailItem';
import {getTransTypeInsight} from '@/api/diagnosis/exceedCostDetail';
import GradientTag from '@/components/common/RichTextRenderer/replacer/GradientTag';
import {transNameMapByValue} from '@/config/transType';
import {useExceedCostContext} from '../../../../../context';
import './style.global.less';
export function TransTopRecommendMain() {

    const {
        selectValue,
        projectInfo,
    } = useExceedCostContext();

    const timeWindow = dayjs(selectValue.diagnosisTime[0]).isSame(dayjs(), 'day') ? 1 : 7;

    const [data] = useResource(getTransTypeInsight, {timeWindow});


    const selectedTransNames: string[] = [...(projectInfo.transTypes || []), ...(projectInfo.assistTransTypes || [])]
        .map(transType => transNameMapByValue[transType]);

    const filteredTransTypes = data.tradeTopTransTypes
        ?.filter(item => item.transTypeName !== '一句话咨询')
        ?.slice(0, 6) || [];

    return (
        <div className="exceed-cost-trans-top-recommend">
            {filteredTransTypes.length && (
                <div className="trans-top-recommend-list">
                    {filteredTransTypes.map((item, index) => (
                        <div className="trans-top-recommend-item" key={item.transTypeName}>
                            <div className="trans-top-recommend-name">
                                <span className="trans-top-recommend-name-text">{item.transTypeName}</span>
                                {
                                    index <= 2 && !selectedTransNames.includes(item.transTypeName) && (
                                        <GradientTag
                                            className="trans-top-recommend-tag"
                                            level="important"
                                            size="small"
                                        >
                                            推荐
                                        </GradientTag>
                                    )
                                }
                                {
                                    selectedTransNames.includes(item.transTypeName) && (
                                        <GradientTag
                                            className="trans-top-recommend-tag"
                                            level="light"
                                            size="small"
                                        >
                                            使用中
                                        </GradientTag>
                                    )
                                }
                            </div>
                            <div className="trans-top-recommend-ratio baidu-number">
                                {(item.payRatio * 100).toFixed(2)}%
                            </div>
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
}


export function TransTopRecommend() {
    return (
        <DetailBoundary>
            <TransTopRecommendMain />
        </DetailBoundary>
    );
}
