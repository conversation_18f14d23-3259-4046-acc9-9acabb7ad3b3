import {useResource} from 'react-suspense-boundary';
import dayjs from 'dayjs';
import {useCallback, useEffect, useMemo, useRef} from 'react';
import {useAsyncTaskBanner} from 'commonLibs/hooks/asyncTask';
import {addPerf} from '@/utils/logger/weirwood';
import {CreativeOptimizeFeConfig, getExceedCostDetailBatch} from '@/api/diagnosis/exceedCost';
import {ExceedCostAdviceName} from '@/api/diagnosis/exceedCost';
import {
    batchUpdateFcCampaigns,
    batchUpdateSchedule,
    fetchFcMaterialCampaignList,
    updateFcCampaign,
} from '@/api/manage/campaign';
import {FC_CAMPAIGN_LIST_REPORT_TYPE} from '@/dicts/reportType';
import {useKeyOrientedArray} from '@/hooks/collection/array';
import {CampaignItemOfFC} from '@/interface/campaign';
import {AIX_ERROR, createError} from '@/utils/error';
import {toOneUIPaginationProps, useTablePagination} from '@/hooks/pagination';
import {useRequestedMaterials, useRowSelection} from '@/hooks/selection';
import {toOneUIRowSelectionProps} from '@/utils/selection';
import {FcIdType} from '@/dicts/idType';
import {getUserId} from '@/utils';
import {useDrawerEditorContext} from '@/modules/Ad/ChatDrawer';
import {getCrowdInfo, updateCampaignCrowdWithThunder} from '@/api/diagnosis/exceedCostDetail';
import {useExceedCostContext} from '../../context';
import {genClickEvent} from '../../../../util/action';
import {preExceedCostDetailParams} from './advice/util';

export function useExceedCostAdviceDetail({adviceNames}: {adviceNames: ExceedCostAdviceName[]}) {
    const {selectValue, analysisData, diagnosisScene} = useExceedCostContext();
    const [metrics, {pending}] = useResource(
        getExceedCostDetailBatch<ExceedCostAdviceName[]>,
        {
            adviceTypeList: adviceNames,
            diagnosisScene,
            ...preExceedCostDetailParams(analysisData, selectValue),
        });

    useEffect(() => {
        if (!pending && metrics && adviceNames.includes('OVER_CHARGE_INCREASE_BID' as ExceedCostAdviceName)) {
            // 记录超成本出价数据获取完成时间
            addPerf('exceed_cost_bid_data_ready');
        }
    }, [pending, metrics, adviceNames]);

    return {
        // @ts-ignore
        projectId: selectValue.project.projectId,
        ...metrics,
    };
}


export function useClickEvent(
    adviceType: ExceedCostAdviceName | CreativeOptimizeFeConfig,
    params?: Record<string, any>
) {
    const {instructions, diagnosisLog} = useExceedCostContext();
    return genClickEvent(adviceType, {instructions, params, diagnosisLog});
}


function getCampaignRowKey(row: CampaignItemOfFC) {
    return row.campaignId;
}

type CommonDataFields = 'impression' | 'click' | 'cost';
const FIELDS: Array<keyof CampaignItemOfFC | CommonDataFields> = [
    'campaignName', 'campaignId', 'marketingTargetId', 'campaignBidType', 'equipmentType', 'projectId',
    'campaignOcpcBidType', 'impression', 'click', 'cost', 'campaignType',
    'schedule', 'schedulePriceFactors', 'scheduleTemplateId',
    'regionTarget', 'regionPriceFactor', 'regionTargetType', 'geoLocationStatus',
    'regionType', 'regionCustomType', 'regionPackageId', 'regionArea', 'storeDistance', 'regionStore',
    'adType', 'storePageInfos',

    'crowdAixExcludeAiTypes', 'crowdAixExcludConvertedTypes', 'crowdAixTargetBaseTypes',
];


const startTime = dayjs().subtract(7, 'day').format('YYYY-MM-DD');
const endTime = dayjs().subtract(1, 'day').format('YYYY-MM-DD');


async function getCampaignList({
    campaignIds,
    needCrowdInfo = false,
}: {
    campaignIds: number[];
    needCrowdInfo: boolean;
}) {

    const [data, rowsWithCrowdInfo] = await Promise.all([
        fetchFcMaterialCampaignList({
            fields: FIELDS,
            // 固定最近七天， 今天不算，从昨天开始
            timeRange: {
                startDate: startTime,
                endDate: endTime,
            },
            filters: [
                {
                    column: 'campaignId',
                    operator: 'in',
                    values: campaignIds,
                },
            ],
            reportType: FC_CAMPAIGN_LIST_REPORT_TYPE,
        }),
        // 获取人群信息只有thunder接口有
        needCrowdInfo ? getCrowdInfo({campaignIds}) : Promise.resolve([]),
    ]);

    data.rows = data.rows.map(row => {
        const crowdInfo = rowsWithCrowdInfo.find(item => item.campaignId === row.campaignId) || {};
        return {
            ...row,
            ...crowdInfo,
        };
    });
    return data;
}


export function useAdviceDetailPlanList(
    campaignIds: number[],
    needCrowdInfo = false,
) {

    const [{
        rows: rawRows,
    }, {
        refresh,
        pending,
    }] = useResource(
        getCampaignList,
        {
            campaignIds,
            needCrowdInfo,
        }
    );

    const [allRows, {
        updateByKey: updateCampaignById,
        getItemByKey: getCampaignById,
        updateItems: updateCampaigns,
        set: setCampaigns,
    }] = useKeyOrientedArray(rawRows, {getKey: getCampaignRowKey});

    const [pagination, {setPageNo, setPageSize}] = useTablePagination();
    const totalCount = allRows.length;
    // 处理分页
    const rows = useMemo(() => {
        return allRows.slice((pagination.pageNo - 1) * pagination.pageSize, pagination.pageNo * pagination.pageSize);
    }, [allRows, pagination.pageNo, pagination.pageSize]);

    const allIds = useMemo(() => allRows.map(getCampaignRowKey), [allRows]);

    const listIds = useMemo(() => rows.map(getCampaignRowKey), [rows]);
    const [selection, selectionOperations] = useRowSelection({ids: listIds, totalCount});

    const {
        resetRowSelection,
        onSelectChange,
        selectAll,
        getSelectedInfo,
    } = selectionOperations;


    useEffect(() => {
        setPageNo(1);
    }, [pagination.pageSize, setPageNo]);


    const [asyncTasks, asyncTaskMethods] = useAsyncTaskBanner({levelIdType: FcIdType.CAMPAIGN_LEVEL});
    const {startQueryAsyncTask} = asyncTaskMethods;

    // to one-ui props
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {
                selection,
                onSelectChange,
                selectAll,
                showSelectAll: true,
            },
            rows,
            {getId: getCampaignRowKey, multiPageSelection: true}
        ),
        [selection, onSelectChange, selectAll, rows],
    );
    const paginationProps = useMemo(
        () => toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount),
        [pagination, setPageNo, setPageSize, totalCount],
    );
    // 创建行内编辑方法的工厂函数
    const createInlineSaveFactory = useCallback(
        (updateApi: (...args: any[]) => Promise<CampaignItemOfFC[]>, modifyResponse?: (data: any) => any) => {
            return async (
                campaignId: number, values: Partial<CampaignItemOfFC>, commonExtraParams?: Record<string, any>
            ) => {
                let data = null;
                try {
                    [data] = await updateApi(campaignId, values, commonExtraParams);
                }
                catch (error) {
                    throw createError(error);
                }

                const finalData = modifyResponse ? modifyResponse(data) : data;
                updateCampaignById(campaignId, finalData);
                return data;
            };
        },
        [updateCampaignById]
    );

    // 行内编辑方法
    const inlineSaveCampaign = useMemo(
        () => createInlineSaveFactory(updateFcCampaign),
        [createInlineSaveFactory]
    );


    const inlineSaveCampaignCrowd = useMemo(
        () => createInlineSaveFactory(updateCampaignCrowdWithThunder),
        [createInlineSaveFactory]
    );

    // 时段做下特殊处理
    const inlineSaveCampaignSchedule = useMemo(
        () => createInlineSaveFactory(updateFcCampaign, data => ({
            // @ts-ignore
            schedule: [], // 后端在全部时段时段时不返回 schedule，所以需要给个默认, 用于更新表格
            ...data,
        })),
        [createInlineSaveFactory]
    );


    // * 这里比较恶心， 因为 onModifyAll 会更新 selection 从而导致 getSelectedInfo 发生变化
    // * 而 onBatchClick 依赖 getSelectedInfo， 所以需要用 ref 来存储 getSelectedInfo
    const getSelectedInfoRef = useRef(getSelectedInfo);

    useEffect(() => {
        getSelectedInfoRef.current = getSelectedInfo;
    }, [getSelectedInfo]);


    const batchSaveCampaigns = useCallback(
        async (values: any) => {
            let data;
            const selectionInfo = getSelectedInfoRef.current();
            try {
                data = await batchUpdateFcCampaigns(
                    {
                        filters: [
                        ],
                        timeRange: {
                            startTime,
                            endTime,
                        },
                        selectionInfo,
                        defaultFieldFilters: [
                            {
                                field: 'campaignId',
                                op: 'in',
                                values: allIds,
                            },
                        ],
                        pageLevel: {idType: FcIdType.USER_LEVEL, levelId: Number(getUserId())},
                    },
                    values
                );
            }
            catch (error) {
                const batchSaveError = createError(error) as AIX_ERROR;
                batchSaveError.materialName = '方案';
                if (batchSaveError._normalized?.type === 'partial') {
                    refresh();
                }
                throw batchSaveError;
            }
            if (data.isAsyncTask) {
                startQueryAsyncTask(true);
                return;
            }
            updateCampaigns(data.dataList);

            return data;
        },
        [updateCampaigns, startQueryAsyncTask, getSelectedInfoRef, refresh, allIds]
    );


    const separateUpdateSchedule = useCallback(
        async (values: any) => {
            let data = null;
            try {
                data = await batchUpdateSchedule(values);
            }
            catch (error) {
                const batchSaveError = createError(error) as AIX_ERROR;
                batchSaveError.materialName = '方案';
                if (batchSaveError._normalized?.type === 'partial') {
                    resetRowSelection();
                    refresh();
                }
                throw batchSaveError;
            }
            resetRowSelection();
            refresh();
            return data;
        },
        [refresh, resetRowSelection]
    );

    const {getMaterialByKeyFromRequestedMaterials} = useRequestedMaterials({rows, key: 'campaignId'});

    return [
        {
            rows,
            totalCount,
            pagination,
            selection,
            rowSelectionProps,
            paginationProps,
            asyncTasks,
            pending,
            getSelectedInfoRef,
        },
        {
            refresh,
            inlineSaveCampaign,
            inlineSaveCampaignSchedule,
            inlineSaveCampaignCrowd,
            batchSaveCampaigns,
            selectionOperations,
            separateUpdateSchedule,
            getCampaignByDataMap: getMaterialByKeyFromRequestedMaterials,
        },
    ] as const;
}


export function useAdviceDetailPlanListEditor() {
    const {openEditor, closeEditor} = useDrawerEditorContext();
    useEffect(() => {
        return () => {
            closeEditor();
        };
    }, [closeEditor]);

    return {
        openEditor,
        closeEditor,
    };
}
