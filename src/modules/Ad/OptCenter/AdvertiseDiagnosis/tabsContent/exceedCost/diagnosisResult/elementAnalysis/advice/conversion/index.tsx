import {Icon<PERSON>ullseye, IconLine<PERSON><PERSON>} from 'dls-icons-react';
import {DiagnosisCardDetailItem} from '@/modules/Ad/OptCenter/AdvertiseDiagnosis/components/cardDetailItem';
import {ExceedCostAdviceName} from '@/api/diagnosis/exceedCost';
import {DiagnosisCardDetail} from '../../../../../../components/cardDetail';
import {ExceedCostCompProps} from '../../config';
import {useClickEvent, useExceedCostAdviceDetail} from '../../hook';
import {TransBarChart} from '../cardDetail/transBarChart';
import {useExceedCostContext} from '../../../../context';
import {TransTopRecommend} from '../cardDetail/tansTopRecommend';
import './style.global.less';

export default function ExceedCostConversion({adviceNames}: ExceedCostCompProps) {

    const metrics = useExceedCostAdviceDetail({adviceNames});
    const {
        selectValue,
    } = useExceedCostContext();

    const addTransTypeClickEvent = useClickEvent(ExceedCostAdviceName.ADD_TRANS_TYPE, {
        projectId: selectValue.project.projectId,
    });

    const tip = `统计周期：${selectValue.diagnosisTime[0]}至${selectValue.diagnosisTime[1]}`;

    return (
        <DiagnosisCardDetail
            title="新增同行业常用目标转化类型"
            description="建议您参考同行业新增目标转化类型进行投放，以助您可以在竞争中抢占先机，实现投放效果的最大化"
        >
            <div className="exceed-cost-conversion-container">
                <DiagnosisCardDetailItem
                    title="项目各转化目标转化量分布"
                    tip={tip}
                    className="exceed-cost-conversion-item"
                    Icon={IconLineChart}
                    content={<TransBarChart metrics={metrics} />}
                />
                <DiagnosisCardDetailItem
                    title="行业常用转化目标推荐"
                    tip="为您披露同行业目标转化量TOP转化类型"
                    className="exceed-cost-conversion-item"
                    Icon={IconBullseye}
                    onActionClick={addTransTypeClickEvent}
                    actionBtnText="新增转化目标"
                    actionPosition="top"
                    content={<TransTopRecommend />}
                />
            </div>
        </DiagnosisCardDetail>
    );
}
