import {Skeleton} from '@baidu/light-ai-react';
import {useCallback, useEffect, useRef, useState} from 'react';
import {RichTextRenderer} from '@/components/common/RichTextRenderer';
import aiChat from '@/utils/aiChat';
import {ModuleType} from '@/dicts';
import {pollDeepThinkResult} from '@/api/diagnosis/exceedCost';
import {addPerf} from '@/utils/logger/weirwood';
import {useExceedCostContext} from '../context';

export default function ExceedCostConclusion({title}: {title?: string}) {
    const {instructions, analysisData} = useExceedCostContext();
    const conclusionText = useConclusionText();

    useEffect(() => {
        // 只在初始基础分析完成时记录，不记录深度思考的追加内容
        if (conclusionText === analysisData.text) {
            addPerf('exceed_cost_diagnosis_conclusion_ready');
        }
    }, [conclusionText, analysisData.text]);

    return (
        <div className="exceed-cost-conclusion">
            {
                !title && (
                    <div className="exceed-cost-title">
                        <img className="exceed-cost-title-icon" src="https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png" />
                        <span>诊断结论与建议</span>
                    </div>
                )
            }
            <div className="exceed-cost-conclusion-content">
                {title && <div className="exceed-cost-conclusion-content-title">{title}</div>}
                <RichTextRenderer
                    moduleType={ModuleType.Ad}
                    aiChat={aiChat}
                    instructions={instructions}
                    content={conclusionText}
                />
            </div>
        </div>
    );
}

function useConclusionText() {
    const {analysisData, selectValue} = useExceedCostContext();
    const isDeepThink = analysisData.isDeepThink;
    const [conclusionText, setConclusionText] = useState(analysisData.text);

    const updateConclusionText = useCallback((text: string) => {
        setConclusionText(`${analysisData.text}<br />${text}`);
    }, [analysisData.text]);

    useEffect(() => {
        if (isDeepThink) {
            const cleanup = pollDeepThinkResult(
                {
                    projectId: selectValue.project.projectId,
                    processId: analysisData.processId,
                },
                updateConclusionText,
                // 记录深度思考轮询完成时间
                () => addPerf('exceed_cost_deep_think_complete'),
            );
            // 返回清理函数，轮询完成时记录性能
            return () => {
                cleanup();
            };
        }
    }, [isDeepThink, analysisData, selectValue, updateConclusionText]);

    return conclusionText;
}


export function ExceedCostConclusionLoading({title}: {title?: string}) {
    return (
        <div className="exceed-cost-conclusion">
            {
                !title && (
                    <div className="exceed-cost-title">
                        <img className="exceed-cost-title-icon" src="https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png" />
                        <span>诊断结论与建议</span>
                    </div>
                )
            }
            <div className="exceed-cost-conclusion-content">
                <div className="exceed-cost-conclusion-content-loading-title">
                    正在生成诊断结论与建议…
                </div>
                <Skeleton.Paragraph rows={3} active />
            </div>
        </div>
    );
}
