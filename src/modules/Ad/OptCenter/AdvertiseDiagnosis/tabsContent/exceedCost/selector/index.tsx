
import {
    useQuickForm, FormConfig, FormProvider, useReactiveData,
    useFieldValue, useFormErrors, useFieldsWatch,
} from '@baidu/react-formulator';

import {Alert, AlertProps, Button} from '@baidu/one-ui';
import {useCallback, useEffect, useMemo} from 'react';

import {useURLQuery} from '@/hooks/tableList/query';
import {DiagnosisProjectItem} from '@/api/diagnosis/consumeFluctuation';
import {DEFAULT_EXCEED_COST_DATE, getExceedCostReadyTimeRange} from '@/api/diagnosis/exceedCost';
import {SelectorFormData} from '../types';

import {DiagnosisDateRangePicker} from './dateRangePicker';
import {
    validateDiagnosisRange,
    getComparedDateRange,
    getDiagnosisFormDataFromQuery,
    DiagnosisConsumeFluctuationQuery,
} from './util';
import {ProjectSelect} from './projectSelect';
import './style.global.less';


const formConfig: FormConfig<SelectorFormData> = {
    fields: [
        {
            field: 'project',
            label: '选择项目',
            use: [ProjectSelect, {width: 240}],
            componentProps: ['diagnosisTime', 'baseTime'],
            validators(value: DiagnosisProjectItem) {

                if (value && value.cost === 0) {
                    return '查询日期项目消费为0';
                }

                return typeof value === 'undefined' ? '请选择项目' : undefined;
            },
        },
        {
            field: 'diagnosisTime',
            label: '查询日期',
            use: [DiagnosisDateRangePicker],
            componentProps: ['readyTimeRange'],
            validators(value: [string, string], formData: SelectorFormData) {
                return validateDiagnosisRange(value, formData);
            },
        },
    ],
    watch: {
        diagnosisTime(value, formData) {
            // 诊断日期变化时，自动更新基准日期
            // 例如诊断日期为 14～15 则基准日期为 12～13
            const [baseStart, baseEnd] = getComparedDateRange(value);
            formData.baseTime = [baseStart, baseEnd];
        },
    },
};

type DiagnosisDateSelectorFormData = Pick<SelectorFormData, 'diagnosisTime' | 'baseTime' | 'readyTimeRange'>;

export const diagnosisDateSelectorFormConfig: FormConfig<DiagnosisDateSelectorFormData> = {
    fields: [
        {
            field: 'diagnosisTime',
            label: '问题日期',
            use: [DiagnosisDateRangePicker],
            validators(value: [string, string], formData: DiagnosisDateSelectorFormData) {
                return validateDiagnosisRange(value, {
                    readyTimeRange: formData.readyTimeRange,
                });
            },
        },
    ],
    watch: {
        diagnosisTime(value, formData) {
            const [baseStart, baseEnd] = getComparedDateRange(value);
            formData.baseTime = [baseStart, baseEnd];
        },
    },
};


const getInitialValue = () => {
    return {
        diagnosisTime: DEFAULT_EXCEED_COST_DATE.diagnosisTime,
        baseTime: DEFAULT_EXCEED_COST_DATE.baseTime,
        project: undefined,
        overChargedProjects: [],
        projectListPending: false,
        readyTimeRange: getExceedCostReadyTimeRange(),
    } as SelectorFormData;
};

const ctx = {
    inputErrorClassName: 'one-ai-invalid',
};

interface ExceedCostSelectorProps {
    onSelectChange: (value: SelectorFormData, target?: string) => void;
    isAutoSubmit?: boolean;
    onClickSearch: () => void;
    isSubmitLoading?: boolean;
}
export default function ExceedCostSelector({
    onSelectChange,
    isAutoSubmit = true,
    onClickSearch,
    isSubmitLoading = false,
}: ExceedCostSelectorProps) {
    const [Form, {validateFields}] = useQuickForm<SelectorFormData>();
    const [query, {setQueryWithReplace}] = useURLQuery<DiagnosisConsumeFluctuationQuery>();

    const defaultFormData = useMemo(() => {
        const queryData = getDiagnosisFormDataFromQuery(query);
        return {
            ...getInitialValue(),
            ...queryData,
        };
    }, []);

    const [formData] = useReactiveData(defaultFormData);
    const [verificationData] = useReactiveData({});

    const onSubmit = useCallback(
        async (target?: string) => {
            let res = {} as SelectorFormData;
            try {
                res = await validateFields();

                // 当项目和时间改变时，同步更新URL参数
                if (res.project && res.diagnosisTime && res.baseTime) {
                    setQueryWithReplace({
                        projectId: res.project.projectId,
                        projectName: res.project.projectName,
                        diagnosisStartTime: res.diagnosisTime[0],
                        diagnosisEndTime: res.diagnosisTime[1],
                        baseStartTime: res.baseTime[0],
                        baseEndTime: res.baseTime[1],
                    });
                }

                onSelectChange(res, target);
            } catch (e) {
                console.error(e);
            }
        },
        [validateFields, onSelectChange, setQueryWithReplace]
    );

    useFieldsWatch(
        formData,
        ['diagnosisTime', 'baseTime', 'project'],
        () => {
            if (formData.project && isAutoSubmit) {
                onSubmit('选项变化时查询');
            }
        },
    );

    useEffect(
        () => {
            if (formData.project && isAutoSubmit) {
                onSubmit('初始进入页面有代入的项目自动查询');
            }
        },
        []
    );

    return (
        <FormProvider value={ctx}>
            <Form
                className="use-rf-preset-form-ui use-horizontal over-cost-selector-form"
                config={formConfig}
                data={formData}
                verificationData={verificationData}
            >
                <Button
                    type="primary"
                    className="over-cost-selector-btn"
                    onClick={onClickSearch}
                    loading={isSubmitLoading}
                >
                    查询
                </Button>
            </Form>
            <AlertInfo formData={formData} verificationData={verificationData} />
        </FormProvider>
    );
}


function AlertInfo({formData, verificationData}: {formData: any, verificationData: any}) {

    const [overChargedProjects] = useFieldValue(formData, 'overChargedProjects');

    const errors = useFormErrors(verificationData);

    const hasErrors = Object.values(errors).some(error => error && error.length > 0);

    const alertProps: AlertProps = {
        type: 'warning',
        content: (
            <span>
                <span>问题日期环比基准日期，</span>
                <span>
                    账户下共有{overChargedProjects.length}个项目存在超成本。
                    <span>（项目：{overChargedProjects.map((i: any) => i.projectName).join('、')}）</span>
                </span>
            </span>
        ),
        showIcon: true,
        style: hasErrors ? {marginTop: '0', marginBottom: '24px'} : {marginTop: '-12px', marginBottom: '24px'},
    };

    return (
        overChargedProjects.length > 0 ? <Alert {...alertProps} /> : null
    );
}
