import {useResource} from 'react-suspense-boundary';
import {useMemo, useEffect} from 'react';
import {DiagnosisAdviceTypeEnum, getAdviceTypeDetailBatch} from '@/api/diagnosis/consumeFluctuation';
import {
    ProjectSuggestBid,
    ProjectSuggestLineChart,
} from '@/modules/Ad/OptCenter/AdvertiseDiagnosis/components/projectSuggestBid';
import {addPerf} from '@/utils/logger/weirwood';
import {TabsComponentProps} from '../config';
import {useConsumeFluctuationContext} from '../../../context';
import {prePareParams} from './util';


export default function ConsumeFluctuationBid({tabs}: TabsComponentProps) {

    const advices = useMemo(() => {
        return tabs.flatMap(i => i.advicesGroup.flatMap(j => j.advices));
    }, [tabs]);

    const {selectValue, analysisData, diagnosisLog} = useConsumeFluctuationContext();

    const [{improveBidMetric}, {pending}] = useResource(
        getAdviceTypeDetailBatch<DiagnosisAdviceTypeEnum[]>,
        {
            adviceTypeList: advices,
            ...prePareParams(analysisData, selectValue),
        });
    useEffect(() => {
        if (!pending && improveBidMetric) {
            // 记录优化举措详解模块-出价位的详细数据加载完成时间
            addPerf('consume_fluctuation_element_analysis_ready');
        }
    }, [pending, improveBidMetric]);

    const {
        suggestBidInfo,
        improveBidInfo,
    } = improveBidMetric;

    const projectId = selectValue.project.projectId;

    return (

        <div className="consume-fluctuation-block-container">
            <div className="consume-fluctuation-desc">项目出价较低导致竞争力下降，建议参考出价及转化量涨幅预估曲线，提升出价以提升消费。</div>
            <div className="consume-fluctuation-block-container-main-bid">
                <ProjectSuggestBid
                    diagnosisLog={diagnosisLog}
                    suggestBidInfo={suggestBidInfo}
                    projectId={projectId}
                    adviceName={DiagnosisAdviceTypeEnum.INCREASE_BID}
                />
                <ProjectSuggestLineChart
                    improveBidInfo={improveBidInfo}
                />
            </div>
        </div>

    );
}
