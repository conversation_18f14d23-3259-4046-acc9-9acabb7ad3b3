import {useEffect} from 'react';
import {Skeleton} from '@baidu/light-ai-react';
import {RichTextRenderer} from '@/components/common/RichTextRenderer';
import aiChat from '@/utils/aiChat';
import {ModuleType} from '@/dicts';
import {addPerf} from '@/utils/logger/weirwood';
import {useConsumeFluctuationContext} from '../context';

export default function ConsumeFluctuationConclusion({title}: {title?: string}) {
    const {analysisData, instructions} = useConsumeFluctuationContext();

    useEffect(() => {
        if (analysisData?.text) {
            // 记录诊断结论与建议及辅助分析模块数据准备完成时间（使用相同的analysisData数据源）
            addPerf('consume_fluctuation_diagnosis_conclusion_ready');
        }
    }, [analysisData?.text]);

    return (
        <div className="consume-fluctuation-conclusion">
            {
                !title && (
                    <div className="consume-fluctuation-title">
                        <img className="consume-fluctuation-title-icon" src="https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png" />
                        <span>诊断结论与建议</span>
                    </div>
                )
            }
            <div className="consume-fluctuation-conclusion-content">
                {title && <div className="consume-fluctuation-conclusion-content-title">{title}</div>}
                <RichTextRenderer
                    moduleType={ModuleType.Ad}
                    aiChat={aiChat}
                    instructions={instructions}
                    content={analysisData.text}
                />
            </div>
        </div>
    );
}

export function ConsumeFluctuationConclusionLoading({title}: {title?: string}) {
    return (
        <div className="consume-fluctuation-conclusion">
            {
                !title && (
                    <div className="consume-fluctuation-title">
                        <img className="consume-fluctuation-title-icon" src="https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png" />
                        <span>诊断结论与建议</span>
                    </div>
                )
            }
            <div className="consume-fluctuation-conclusion-content">
                <div className="consume-fluctuation-conclusion-content-loading-title">
                    正在生成诊断结论与建议…
                </div>
                <Skeleton.Paragraph rows={3} active />
            </div>
        </div>
    );
}
