import {Carousel, Loading} from '@baidu/one-ui';
import {useElementSize} from 'huse';
import {Boundary, useResource} from 'react-suspense-boundary';
import dayjs from 'dayjs';
import {useMemo, useEffect} from 'react';
import {ReportCard} from '@/components/HomePage/AccountOverview';
import {fetchProjectCompareReport} from '@/api/diagnosis/consumeFluctuation';
import {formatPercentage, toFixedTenThousand} from '@/utils/number';
import {addPerf} from '@/utils/logger/weirwood';
import {useConsumeFluctuationContext} from '../../context';
import './index.global.less';

const cardGap = 12;
const cardSumPadding = 12;
const cardWidth = 182;


export const indicatorKeys = [
    {
        label: '消费',
        field: 'cost',
        trans: (value: number) => toFixedTenThousand(value, 2, 0),
        unit: '元',
    },
    {
        label: '展现',
        field: 'impression',
        trans: (value: number) => toFixedTenThousand(value, 2, 0),
        unit: '次',
    },
    {
        label: '点击',
        field: 'click',
        trans: (value: number) => toFixedTenThousand(value, 2, 0),
        unit: '次',
    },
    {
        label: '目标转化量',
        field: 'ocpcConversions',
        trans: (value: number) => toFixedTenThousand(value, 2, 0),
        unit: '个',
    },
    {
        label: '目标转化率',
        field: 'ocpcConversionsCVR',
        trans: (value: number) => formatPercentage(value, {defaultValue: 0}),
    },
    {
        label: '深度转化量',
        field: 'deepOCPCConversions',
        trans: (value: number) => toFixedTenThousand(value, 2, 0),
        unit: '个',
    },
    {
        label: '一键起量消费',
        field: 'liftBudgetPay',
        trans: (value: number) => toFixedTenThousand(value, 2, 0),
        unit: '元',
    },
    {
        label: '一键起量目标转化量',
        field: 'liftBudgetOcpcConversions',
        trans: (value: number) => toFixedTenThousand(value, 2, 0),
        unit: '个',
    },
] as const;


function CoreData() {

    const {selectValue} = useConsumeFluctuationContext();

    const [data, {pending}] = useResource(
        fetchProjectCompareReport,
        {
            projectId: selectValue.project?.projectId,
            diagnosisTime: selectValue.diagnosisTime,
            baseTime: selectValue.baseTime,
        },
    );

    useEffect(() => {
        if (data) {
            // 记录核心数据模块API数据加载完成时间
            addPerf('consume_fluctuation_core_data_ready');
        }
    }, [data]);

    const days = dayjs(selectValue.diagnosisTime[0]).diff(selectValue.baseTime[0], 'day');

    const [ref, size] = useElementSize();

    const {slidesToShow, reportCardWidth} = useMemo(() => {
        let reportCardWidth = cardWidth;
        const slidesToShow = Math.min((size?.width - cardSumPadding) / (cardWidth + cardGap), indicatorKeys.length);
        if (slidesToShow === indicatorKeys.length) {
            reportCardWidth = (Math.floor((size?.width - cardSumPadding * 2) / indicatorKeys.length) - cardGap);
        }

        return {reportCardWidth, slidesToShow};
    }, [size?.width]);

    return (
        <div className="core-data-container" ref={ref}>
            <div className="consume-fluctuation-title">核心数据</div>

            <div className="core-data-content">
                <Carousel
                    slidesToScroll={1}
                    showButton
                    slidesToShow={slidesToShow}
                    width={size?.width - cardSumPadding}
                    sliderMode="hide"
                    nextButtonProps={{className: 'core-data-next-button'}}
                    prevButtonProps={{className: 'core-data-prev-button'}}
                >
                    {indicatorKeys.map(indicatorConfig => {
                        const {field} = indicatorConfig;
                        return (
                            <ReportCard
                                {...indicatorConfig}
                                key={field}
                                days={days}
                                data={data}
                                startDate={selectValue.diagnosisTime[0]}
                                endDate={selectValue.diagnosisTime[1]}
                                width={reportCardWidth}
                                isCustomizableDate
                                baseStartTime={selectValue.baseTime[0]}
                                lineStyle={{height: '56px'}}
                                height={176}
                            />
                        );
                    })}
                </Carousel>
            </div>
        </div>
    );
}

interface CoreDataProps {}

export default (props: CoreDataProps) => {
    return (
        <Boundary pendingFallback={<Loading />}>
            <CoreData {...props} />
        </Boundary>
    );
};
