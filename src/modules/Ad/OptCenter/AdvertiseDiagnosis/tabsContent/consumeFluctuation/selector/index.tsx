/* eslint-disable max-len */
import {
    useQuickForm, FormConfig, FormProvider, useReactiveData,
    useFieldValue, useFormErrors,
    useFieldsWatch,
} from '@baidu/react-formulator';
// import {Tag, Typography} from '@baidu/light-ai-react';
import {Alert, AlertProps} from '@baidu/one-ui';
import {useCallback, useEffect, useMemo} from 'react';
import {useURLQuery} from '@/hooks/tableList/query';
import {DEFAULT_DIAGNOSIS_DATE, DEFAULT_DIAGNOSIS_READY_TIME_RANGE} from '@/api/diagnosis/consumeFluctuation';
import SaveButton from '@/components/common/Button/SaveButton';
import {SelectorFormData} from '../types';
import {BaseDateRangePicker, DiagnosisDateRangePicker} from './dateRangePicker';
import {
    validateBaseRange,
    validateDiagnosisRange,
    getComparedDateRange,
    getDiagnosisFormDataFromQuery,
    DiagnosisConsumeFluctuationQuery,
} from './util';
import {ProjectSelect} from './projectSelect';
import './style.global.less';


const formConfig: FormConfig<SelectorFormData> = {
    fields: [
        {
            field: 'project',
            label: '选择项目',
            use: [ProjectSelect, {width: 256}],
            componentProps: ['diagnosisTime', 'baseTime'],
            validators(value: number) {
                return typeof value === 'undefined' ? '请选择项目' : undefined;
            },
        },
        {
            field: 'diagnosisTime',
            label: '问题日期',
            use: [DiagnosisDateRangePicker],
            validators(value: [string, string], formData: SelectorFormData) {
                return validateDiagnosisRange(value, formData);
            },
        },
        {
            field: 'baseTime',
            label: '基准日期',
            use: [BaseDateRangePicker],
            componentProps: ['diagnosisTime'],
            validators(value: [string, string], formData: SelectorFormData) {
                return validateBaseRange(value, formData);
            },
        },
    ],
    watch: {
        diagnosisTime(value, formData) {
            // 诊断日期变化时，自动更新基准日期
            // 例如诊断日期为 14～15 则基准日期为 12～13
            const [baseStart, baseEnd] = getComparedDateRange(value);
            formData.baseTime = [baseStart, baseEnd];
        },
    },
};

type DiagnosisDateSelectorFormData = Pick<SelectorFormData, 'diagnosisTime' | 'baseTime' | 'readyTimeRange'>;

export const diagnosisDateSelectorFormConfig: FormConfig<DiagnosisDateSelectorFormData> = {
    fields: [
        {
            field: 'diagnosisTime',
            label: '问题日期',
            use: [DiagnosisDateRangePicker],
            validators(value: [string, string], formData: DiagnosisDateSelectorFormData) {
                return validateDiagnosisRange(value, {
                    readyTimeRange: formData.readyTimeRange,
                });
            },
        },
        {
            field: 'baseTime',
            label: '基准日期',
            use: [BaseDateRangePicker],
            componentProps: ['diagnosisTime'],
            validators(value: [string, string], formData: DiagnosisDateSelectorFormData) {
                return validateBaseRange(value, formData);
            },
        },
    ],
    watch: {
        diagnosisTime(value, formData) {
            const [baseStart, baseEnd] = getComparedDateRange(value);
            formData.baseTime = [baseStart, baseEnd];
        },
    },
};

const initialValue: SelectorFormData = {
    diagnosisTime: DEFAULT_DIAGNOSIS_DATE.diagnosisTime,
    baseTime: DEFAULT_DIAGNOSIS_DATE.baseTime,
    project: undefined,
    fluctuationDroppedProjects: [],
    projectListPending: false,
    readyTimeRange: DEFAULT_DIAGNOSIS_READY_TIME_RANGE,
};

const ctx = {inputErrorClassName: 'one-ai-invalid'};

interface ConsumeFluctuationSelectorProps {
    onSelectChange: (value: SelectorFormData, target?: string) => void;
    isAutoSubmit?: boolean;
    onClickSearch: () => void;
    setHasFluctuationDrop: (value: boolean) => void;
    isSubmitLoading?: boolean;
}

export default function ConsumeFluctuationSelector({
    onSelectChange,
    isAutoSubmit = true,
    onClickSearch,
    setHasFluctuationDrop,
    isSubmitLoading = false,
}: ConsumeFluctuationSelectorProps) {
    const [Form, {validateFields}] = useQuickForm<SelectorFormData>();
    const [query, {setQueryWithReplace}] = useURLQuery<DiagnosisConsumeFluctuationQuery>();


    const defaultFormData = useMemo(
        () => ({
            ...initialValue,
            ...getDiagnosisFormDataFromQuery(query),
        }),
        []
    );

    const [formData] = useReactiveData(defaultFormData);
    const [verificationData] = useReactiveData({});

    const onSubmit = useCallback(async (target?: string) => {
        let res = {} as SelectorFormData;
        try {
            res = await validateFields();
        } catch (e) {
            console.error(e);
        }

        // 当项目和时间改变时，同步更新URL参数
        if (res.project && res.diagnosisTime && res.baseTime) {
            setQueryWithReplace({
                projectId: res.project.projectId,
                projectName: res.project.projectName,
                diagnosisStartTime: res.diagnosisTime[0],
                diagnosisEndTime: res.diagnosisTime[1],
                baseStartTime: res.baseTime[0],
                baseEndTime: res.baseTime[1],
            });
        }
        return onSelectChange(res, target);
    }, [validateFields, onSelectChange, setQueryWithReplace]);

    useFieldsWatch(
        formData,
        ['diagnosisTime', 'baseTime', 'project'],
        () => {
            if (formData.project && isAutoSubmit) {
                onSubmit('选项变化时查询');
            }
        },
    );

    const formContext = useMemo(() => ({
        ...ctx,
        setHasFluctuationDrop,
    }), [setHasFluctuationDrop]);
    useEffect(
        () => {
            if (formData.project && isAutoSubmit) {
                onSubmit('初始进入页面有代入的项目自动查询');
            }
        },
        []
    );

    return (
        <FormProvider value={formContext}>
            <Form
                className="use-rf-preset-form-ui use-horizontal consume-fluctuation-selector-form"
                config={formConfig}
                data={formData}
                verificationData={verificationData}
            >
                <SaveButton
                    type="primary"
                    className="consume-fluctuation-selector-btn"
                    onClick={onClickSearch}
                    loading={isSubmitLoading}
                >
                    查询
                </SaveButton>
            </Form>
            <AlertInfo formData={formData} verificationData={verificationData} />
        </FormProvider>
    );
}


function AlertInfo({formData, verificationData}: {formData: any, verificationData: any}) {

    const [fluctuationDroppedProjects] = useFieldValue(formData, 'fluctuationDroppedProjects');

    const errors = useFormErrors(verificationData);

    const hasErrors = Object.values(errors).some(error => error && error.length > 0);

    const alertProps: AlertProps = {
        type: 'warning',
        content: (
            <span>
                <span>问题日期环比基准日期，</span>
                <span>
                    账户下共有{fluctuationDroppedProjects.length}个项目存在波动。
                    <span>
                        （项目：{fluctuationDroppedProjects.map((i: any) => i.projectName).join('、')}）
                    </span>
                </span>
            </span>
            // <Typography.Paragraph density="loose">
            //     问题日期环比基准日期，账户下共有以下{fluctuationDroppedProjects.length}个项目存在波动。
            //     <Tag.Group
            //         variant="text-bubble"
            //         ellipsis={{length: 3}}
            //         options={fluctuationDroppedProjects.map(item => ({
            //             value: item.projectId,
            //             label: (
            //                 <span
            //                     onClick={() => {
            //                         formData.project = fluctuationDroppedProjects.find(i => i.projectId === item.projectId);
            //                     }}
            //                 >
            //                     {item.projectName}
            //                 </span>
            //             ),
            //         }))}
            //     />
            // </Typography.Paragraph>
        ),
        showIcon: true,
        style: hasErrors ? {marginTop: '0'} : {marginTop: '-12px'},
    };
    return (
        fluctuationDroppedProjects.length > 0 ? <Alert {...alertProps} /> : null
    );
}
