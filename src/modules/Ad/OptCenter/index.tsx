import {Suspense} from 'react';
import {partial} from 'lodash-es';
import {useLocalStorage} from 'huse';
import {Boundary} from 'react-suspense-boundary';
import {Switch, Route} from 'react-router-dom';
import {VerticalNav} from '@baidu/one-ui-pro';
import {IconShieldWaveSolid, IconTaskListSolid, IconBookBookmarkSolid, IconBullseyeHitSolid} from 'dls-icons-react';
import {AdModuleType, PageType} from '@/dicts/pageType';
import AdRoutes, {useAdRoute, RoutesType} from '@/modules/Ad/routes';
import {Instructions} from '@/utils/instructions';
import {useProgressiveContentWidth} from '@/hooks/style';
import {useGlobalProductContext} from '@/hooks/productLine';
import {renderError, recoverMethods} from '../hooks/routes';
import {ImportByEnum} from '../ToolsCenter/config';
import {EMBED_PAGES} from './config';
import NotSupported from './common/NotSupported';
import './index.global.less';

const OptCenterRoutes = AdRoutes.getRoutesByPrefixs?.(['OptCenter']);

const OptCenter = ({instructions}: {instructions: Instructions}) => {
    const {adPageTitle, isCustomShowPageTitle} = useAdRoute();
    const {product} = useGlobalProductContext();
    useProgressiveContentWidth();
    return (
        <div className="aix-opt-center">
            {adPageTitle && !isCustomShowPageTitle && <div className="page-title">{adPageTitle}</div>}
            <Switch>
                {
                    OptCenterRoutes.map(route => {
                        const Component = route.component;
                        const key = route.name;
                        const platform = route.platform;

                        if (platform && platform !== product) {
                            return (
                                <Route key={key} path={route.fullPath}>
                                    <Suspense fallback={null}>
                                        <NotSupported type={key} key={key} />
                                    </Suspense>
                                </Route>
                            );
                        }
                        const {
                            platforms, importBy, name, category,
                        } = EMBED_PAGES.find(option => option.key === key) || {};
                        const props: Record<string, any> = {};
                        if (importBy === ImportByEnum.iframe) {
                            props.platforms = platforms;
                            props.name = name;
                            props.category = category;
                            props.hasSubTitle = route.hasSubTitle;
                        }
                        return (
                            <Route key={key} path={route.fullPath}>
                                <Suspense fallback={null}>
                                    <Component {...route.props} instructions={instructions} {...props} />
                                </Suspense>
                            </Route>
                        );
                    })
                }
            </Switch>
        </div>
    );
};

export default function OptCenterContainer(props: {instructions: Instructions}) {
    const {instructions} = props;
    const {linkTo, adPageType} = useAdRoute();
    const [collapsed, onCollapseChange] = useLocalStorage('OptCenterNav_collapsed', false);

    const navProps = {
        className: 'aix-adModule-vertical-nav',
        type: 'ghost',
        options: [
            {
                key: PageType.DiagnosisDashboard,
                label: '诊断概览',
                icon: <IconShieldWaveSolid />,
            },
            {
                key: PageType.IndustryInsights,
                label: '行业洞察',
                icon: <IconBullseyeHitSolid />,
            },
            {
                key: PageType.AdvertiseDiagnosis,
                label: '问题排查',
                icon: <IconTaskListSolid />,
            },
            {
                key: PageType.OptimizeAdvices,
                label: '优化建议',
                icon: <IconBookBookmarkSolid />,
            },
        ],
        menuProps: {
            mode: 'inline',
            onClick: (e: {key: string}) => linkTo(e.key),
            selectedKeys: [AdModuleType.OptCenter, adPageType],
        },
        collapsable: true,
        collapsed,
        onCollapseChange,
        theme: 'light-ai',
    };

    return (
        <Boundary renderError={partial(renderError, {instructions, methods: [recoverMethods.refresh]})}>
            <div className="qingge-opt-center-container">
                <VerticalNav {...navProps} />
                <OptCenter instructions={instructions} />
            </div>
        </Boundary>
    );
}
