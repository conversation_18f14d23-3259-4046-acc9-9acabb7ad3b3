import {isNil, partial} from 'lodash-es';
import queryString from 'query-string';
import {useHistory, useLocation} from 'react-router-dom';
import React, {createContext, useContext, useCallback, lazy, useState, ReactNode} from 'react';
import {AdModuleType, PageName, PageType} from '@/dicts/pageType';
import {appendQuery} from '@/utils/route';
import {getUserId} from '@/utils';
import aiChat, {PushMessageParams} from '@/utils/aiChat';
import {GrayIconTip} from '@/components/common/Tip/HoverTip';
import {PLATFORM_ENUM} from '@/dicts';
import {isFeedBusinessPointReportUser} from '@/utils/getFeedFlag';
import {RouteQuickLinkConfig} from '@/components/FlashLink/types';
import {isLawIndustryUser, isFcCplUser} from '@/utils/getFlag';
import {CreativeType} from './ManageCenter/creativeList/AixsegmentsList/config';

const ManageCenter = lazy(() => import('@/modules/Ad/ManageCenter'));
const DataCenter = lazy(() => import('@/modules/Ad/DataCenter'));
const OptCenter = lazy(() => import('@/modules/Ad/OptCenter'));
const AiReportSdk = lazy(() => import('@/components/AiReportSDK/index')
    .then(module => ({default: module.AiReportSDK})));
const PinReport = lazy(() => import('@/modules/Ad/DataCenter/customReport/pin'));
const TempleteReport = lazy(() => import('@/modules/Ad/DataCenter/customReport/templete'));

export interface IRoute {
    name: string;
    path: string;
    type?: 'redirect';
    tags?: string[];
    component?: React.ComponentType<any> | React.FC<any> | React.LazyExoticComponent<any>;
    props?: object;
    children?: IRoute[];
    fullPath?: string;
    label?: string | ReactNode;
    platform?: PLATFORM_ENUM;
    isCustomShowPageTitle?: boolean;
    backToPageType?: PageType; // 主流程结束需要返回的页面
    hasSubTitle?: boolean; // 是否有二级tabs子标题 默认为false
    quickLink?: RouteQuickLinkConfig; // FlashLink 快速跳转配置
}
export type RoutesType = IRoute[] & {
    getRoutesByPrefixs?: (prefixs: string[]) => RoutesType;
    getPathByName?: (name: string) => string | null;
    getItemByRouteFullPath?: (path: string) => IRoute | null;
    getRoutesByPlatform: (platform: PLATFORM_ENUM) => RoutesType;
};

enum TargetTypeEnum {
    _blank = '_blank',
    _self = '_self',
}

/* eslint-disable max-len */
const routes: IRoute[] = [
    {
        name: AdModuleType.Overview,
        path: `/${AdModuleType.Overview}`,
        label: '概览',
        component: lazy(() => import('@/components/HomePage')),
    },
    {
        name: 'ManageCenter',
        path: `/${AdModuleType.ManageCenter}/*`,
        component: ManageCenter,
        children: [
            {
                name: PageType.ProjectList,
                path: '/projectList',
                component: lazy(() => import('@/modules/Ad/ManageCenter/projectList')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.ProjectList],
                    keywords: ['ocpc', '投放包', 'ocpc出价策略', '出价', '转化目标', '项目预算', '放量', '起量', '增强模式', '目标ROI', '深度转化', '浅层转化', '转化成本'],
                },
            },
            {
                name: PageType.CampaignList,
                path: '/campaignList',
                component: lazy(() => import('@/modules/Ad/ManageCenter/campaignList')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CampaignList],
                    keywords: ['方案', '计划', 'campaign', 'plan', '方案管理', '计划列表', '方案列表', '地域', '时段', '设备', '预算', '否词包'],
                    order: 1,
                },
            },
            {
                name: PageType.AdgroupList,
                path: '/adgroupList',
                component: lazy(() => import('@/modules/Ad/ManageCenter/adgroupList')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.AdgroupList],
                    keywords: ['adgroup', 'unit', '单元列表', '自动定向', '自动图片优化', '自动文案优化', '落地页', '网址', '否词绑定'],
                    order: 3,
                },
            },
            {
                name: PageType.CreativeTextList,
                path: '/creativeList/creativeTextList',
                component: lazy(() => import('@/modules/Ad/ManageCenter/creativeList/creativeTextList')),
                props: {
                    currentTab: PageType.CreativeTextList,
                },
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CreativeTextList],
                    keywords: ['creative', 'idea', '创意列表', '创意文案', '标题', '描述', '程序化', '自动文案屏蔽'],
                    order: 4,
                },
            },
            {
                name: PageType.PictureList,
                path: '/creativeList/pictureList',
                component: lazy(() => import('@/modules/Ad/ManageCenter/creativeList/SegmentList')),
                props: {
                    currentTab: CreativeType.IMAGE,
                },
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.PictureList],
                    keywords: ['图片', '配图', '图片列表', '图片比例', '自动图片管理', '图片主题', '图片绑定'],
                    order: 6,
                },
            },
            {
                name: PageType.VideoList,
                path: '/creativeList/videoList',
                component: lazy(() => import('@/modules/Ad/ManageCenter/creativeList/SegmentList')),
                props: {
                    currentTab: CreativeType.VIDEO,
                },
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.VideoList],
                    keywords: ['视频', '视频列表', '视频比例', '精彩片段', '视频绑定'],
                    order: 8,
                },
            },
            {
                name: PageType.DynamicContentBlock,
                path: '/creativeList/dynamicContentBlock',
                label: '动态内容屏蔽',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ManageCenter/creativeList/DynamicContentBlockList')),
                props: {
                    currentTab: PageType.DynamicContentBlock,
                },
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.DynamicContentBlock],
                },
            },
            {
                name: PageType.CreativeComponent,
                path: '/creativeComponent',
                label: '创意组件',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ManageCenter/creativeComponent')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CreativeComponent],
                },
            },
            {
                name: PageType.MarketPointList,
                path: '/marketPointList',
                label: '营销要点',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ManageCenter/marketPointList')),
                isCustomShowPageTitle: true,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.MarketPointList],
                    keywords: ['提示词'],
                },
            },
            // 商品相关
            {
                name: 'commodity',
                path: '/commodity/*',
                label: '',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ManageCenter/commodity')),
                children: [
                    PageType.CommodityGroupList,
                    PageType.CommodityTemplate,
                    PageType.CommodityCreative,
                    PageType.CommodityCreativeComponent,
                    PageType.CommodityGroupListInAdgroup,
                    PageType.CommodityTemplateInAdgroup,
                    PageType.CommodityCreativeInAdgroup,
                    PageType.CommodityCreativeComponentInAdgroup,
                ].map((pageType: PageType) => ({
                    name: pageType,
                    path: `/${pageType}`,
                    platform: PLATFORM_ENUM.FC,
                    component: lazy(() => import('@/modules/Ad/ManageCenter/commodity')),
                })),
            },
            {
                name: PageType.QuestionKnowledgeList,
                path: '/questionKnowledgeList',
                label: '问答知识',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ManageCenter/questionKnowledge')),
                isCustomShowPageTitle: true,
            },
            {
                name: PageType.PremiumCrowdsList,
                path: '/premiumCrowdsList',
                label: '定向人群',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ManageCenter/crowds/premiumcrowds')),
                isCustomShowPageTitle: true,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.PremiumCrowdsList],
                },
            },
            {
                name: PageType.ExcludeCrowdsList,
                path: '/excludeCrowdsList',
                label: '排除人群',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ManageCenter/crowds/excludecrowds')),
                isCustomShowPageTitle: true,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.ExcludeCrowdsList],
                },
            },
            {
                name: 'orient',
                path: '',
                label: '',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ManageCenter/keywordContainer')),
                children: [
                    {
                        name: PageType.KeywordList,
                        path: '/keywordList',
                        label: '关键词',
                        component: lazy(() => import('@/modules/Ad/ManageCenter/keywordList')),
                        isCustomShowPageTitle: true,
                        quickLink: {
                            enabled: true,
                            title: PageName[PageType.KeywordList],
                            keywords: ['keyword', '关键词列表', '出价', '匹配模式', '落地页', '网址', '应用调起网址', 'deeplink', 'ulink'],
                            order: 2,
                        },
                    },
                    {
                        name: PageType.QueryWordList,
                        path: '/queryWordList',
                        label: '搜索词',
                        component: lazy(() => import('@/modules/Ad/ManageCenter/queryWordList')),
                        isCustomShowPageTitle: true,
                        quickLink: {
                            enabled: true,
                            title: PageName[PageType.QueryWordList],
                            keywords: ['query', '触发模式', '搜索词数据'],
                            order: 5,
                        },
                    },
                    {
                        name: PageType.NegativeWordList,
                        path: '/negativeWordList',
                        label: '否定关键词',
                        component: lazy(() => import('@/modules/Ad/ManageCenter/negativeWordList')),
                        isCustomShowPageTitle: true,
                        quickLink: {
                            enabled: true,
                            title: PageName[PageType.NegativeWordList],
                            keywords: ['否词', '精确否词', '短语否词', '添加否词'],
                            order: 7,
                        },
                    },
                ],
            },
            {
                name: 'ManageCenterFallback',
                type: 'redirect',
                path: '/projectList',
            },
        ],
    },
    {
        name: 'DataCenter',
        path: `/${AdModuleType.DataCenter}/*`,
        component: DataCenter,
        children: [
            {
                name: PageType.ReportWanHuaTong + 1,
                path: '/dataKaleidoscope1',
                component: () => <PinReport reportIndex={1} />,
            },
            {
                name: PageType.ReportWanHuaTong + 2,
                path: '/dataKaleidoscope2',
                component: () => <PinReport reportIndex={2} />,
            },
            {
                name: PageType.ReportWanHuaTong + 3,
                path: '/dataKaleidoscope3',
                component: () => <PinReport reportIndex={3} />,
            },
            {
                name: PageType.ReportWanHuaTong + 4,
                path: '/dataKaleidoscope4',
                component: () => <PinReport reportIndex={4} />,
            },
            {
                name: PageType.ReportWanHuaTong + 5,
                path: '/dataKaleidoscope5',
                component: () => <PinReport reportIndex={5} />,
            },
            {
                name: PageType.ReportWanHuaTong,
                path: '/dataKaleidoscope',
                component: lazy(() => import('@/modules/Ad/DataCenter/customReport/entry')),
            },
            {
                name: PageType.GUIReport,
                path: '/GUIReport',
                component: AiReportSdk,
            },
            {
                name: PageType.AccountReport,
                path: '/promotionReport/account',
                component: lazy(() => import('@/modules/Ad/DataCenter/accountReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.AccountReport],
                    keywords: ['账户数据', '账户消费数据', '数据报告', '账户点击数据', '账户展现数据', '账户转化数据'],
                    order: 12,
                },
            },
            {
                name: PageType.ProjectReport,
                path: '/promotionReport/project',
                component: lazy(() => import('@/modules/Ad/DataCenter/projectReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.ProjectReport],
                    keywords: ['项目数据', '项目消费数据', '项目点击数据', '项目展现数据', '项目转化数据', '转化量', '深度转化量', '转化率'],
                    order: 15,
                },
            },
            {
                name: PageType.CampaignReport,
                path: '/promotionReport/campaign',
                component: lazy(() => import('@/modules/Ad/DataCenter/campaignReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CampaignReport],
                    keywords: ['计划报告', '计划数据', '计划消费数据', '计划点击数据', '计划展现数据', '计划转化数据', '计划转化量', '计划转化率'],
                    order: 14,
                },
            },
            {
                name: PageType.AdgroupReport,
                path: '/promotionReport/adgroup',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/DataCenter/adgroupReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.AdgroupReport],
                    keywords: ['单元报告', '单元数据', '单元消费数据', '单元点击数据', '单元展现数据', '单元转化数据', '单元转化量', '单元转化率'],
                },
            },
            {
                name: PageType.AdgroupReport,
                path: '/promotionReport/adgroup',
                platform: PLATFORM_ENUM.FEED,
                component: () => <TempleteReport type={PageType.AdgroupReport} supportPlatformSelect />,
            },
            {
                name: PageType.KeywordReport,
                platform: PLATFORM_ENUM.FC,
                path: '/targetingReport/keyword',
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.KeywordReport],
                    keywords: ['关键词报告', '关键词数据', '关键词消费数据', '关键词点击数据', '关键词展现数据', '关键词转化数据', '关键词点击价格'],
                    order: 13,
                },
                component: lazy(() => import('@/modules/Ad/DataCenter/keywordReport')),
            },
            {
                name: PageType.QueryWordReport,
                path: '/targetingReport/queryWord',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/DataCenter/queryWordReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.QueryWordReport],
                    keywords: ['搜索词报告', '搜索词数据', '触发模式', '搜索词点击数据', '搜索词展现数据', '搜索词点击率', '搜索词点击价格'],
                    order: 11,
                },
            },
            {
                name: PageType.TargetReport,
                path: '/targetingReport/region',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/DataCenter/targetReport')),
            },
            {
                name: PageType.MarketingPointReport,
                path: '/targetingReport/marketingPoint',
                platform: PLATFORM_ENUM.FC,
                component: AiReportSdk,
            },
            {
                name: PageType.AcgGoodsReport,
                path: '/targetingReport/acgGoods',
                platform: PLATFORM_ENUM.FC,
                component: AiReportSdk,
            },
            {
                name: PageType.SplitRegionReport,
                path: '/targetingReport/splitRegion',
                platform: PLATFORM_ENUM.FC,
                component: AiReportSdk,
            },
            {
                name: PageType.NoteReport,
                path: '/targetingReport/NoteReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.NoteReport} />,
            },
            {
                name: PageType.QuestionKnowledgeReport,
                path: '/targetingReport/questionKnowledgeReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.QuestionKnowledgeReport} />,
            },
            {
                name: PageType.ProgramCreativeReport,
                path: '/creativeAnalysis/programCreative',
                component: () => <TempleteReport type={PageType.ProgramCreativeReport} supportPlatformSelect />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.ProgramCreativeReport],
                    keywords: ['程序化创意报告', '创意报告', '创意数据', '创意消费', '创意点击', '创意点击率'],
                },
            },
            {
                name: PageType.CreativeComponentReport,
                path: '/creativeAnalysis/creativeComponent',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.CreativeComponentReport} />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CreativeComponentReport],
                    keywords: ['创意组件报告', '创意组件数据', '组件报告', '组件消费', '组件点击', '组件点击率'],
                },
            },
            {
                name: PageType.CreativeVideoReport,
                path: '/creativeAnalysis/creativeVideo',
                component: lazy(() => import('@/modules/Ad/DataCenter/creativeVideoReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CreativeVideoReport],
                    keywords: ['视频报告', '视频数据', '视频消费', '视频点击', '视频点击率', '视频展现'],
                },
            },
            {
                name: PageType.CreativeImageReport,
                path: '/creativeAnalysis/creativeImage',
                component: lazy(() => import('@/modules/Ad/DataCenter/creativeImageReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CreativeImageReport],
                    keywords: ['图片报告', '图片数据', '图片消费', '图片点击', '图片点击率', '图片展现'],
                },
            },
            {
                name: PageType.CreativeTitleReport,
                path: '/creativeAnalysis/creativeTitle',
                platform: PLATFORM_ENUM.FC,
                component: AiReportSdk,
            },
            {
                name: PageType.CreativeDescReport,
                path: '/creativeAnalysis/creativeDesc',
                platform: PLATFORM_ENUM.FC,
                component: AiReportSdk,
            },
            {
                name: PageType.CreativeTextReport,
                path: '/creativeAnalysis/creativeText',
                component: () => <TempleteReport type={PageType.CreativeTextReport} supportPlatformSelect />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CreativeTextReport],
                    keywords: ['创意文案报告', '创意数据', '创意消费', '创意点击', '点击率', '创意点击率', '创意展现', '创意标题', '创意描述'],
                },
            },
            {
                name: PageType.AdvanceCreativeReport,
                path: '/creativeAnalysis/advanceCreativeReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.AdvanceCreativeReport} />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.AdvanceCreativeReport],
                    keywords: ['高级样式数据', '样式报告', '创意样式报告'],
                },
            },
            {
                name: PageType.LandPageReport,
                path: '/promotionReport/landPage',
                component: () => <TempleteReport type={PageType.LandPageReport} supportPlatformSelect />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.LandPageReport],
                    order: 16,
                },
            },
            {
                name: PageType.InvalidClickReport,
                path: '/resultAnalysis/invalidClick',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/DataCenter/invalidClickReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.InvalidClickReport],
                    order: 18,
                },
            },
            {
                name: PageType.InvalidClueReport,
                path: '/resultAnalysis/invalidClue',
                component: (props: any) => <TempleteReport type={PageType.InvalidClueReport} />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.InvalidClueReport],
                },
            },
            {
                name: PageType.VisitDetailReport,
                path: '/promotionSpecial/visitDetail',
                component: () => <TempleteReport type={PageType.VisitDetailReport} supportPlatformSelect />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.VisitDetailReport],
                },
            },
            {
                name: PageType.RealtimeReport,
                path: '/promotionSpecial/realtime',
                platform: PLATFORM_ENUM.FC,
                component: AiReportSdk,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.RealtimeReport],
                    keywords: ['实时数据', '实时消费', '实时点击', '实时展现', '实时点击率'],
                    order: 17,
                },
            },
            {
                name: PageType.LiveRoomReport,
                path: '/promotionSpecial/LiveRoomReport',
                component: lazy(() => import('@/modules/Ad/DataCenter/iframeReport')),
                props: {
                    levelName: PageType.LiveRoomReport,
                },
            },
            {
                name: PageType.ShopReport,
                path: '/promotionSpecial/shopReport',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/DataCenter/shopReport')),
                props: {
                    levelName: PageType.ShopReport,
                },
            },
            {
                name: PageType.TargetAudienceReport,
                path: '/promotionSpecial/targetAudienceReport',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/DataCenter/targetAudienceReport')),
                props: {
                    levelName: PageType.TargetAudienceReport,
                },
            },
            {
                name: PageType.AftereffectMeasureReport,
                path: '/promotionSpecial/aftereffectMeasureReport',
                component: lazy(() => import('@/modules/Ad/DataCenter/aftereffectMeasureReport')),
                props: {
                    levelName: PageType.AftereffectMeasureReport,
                },
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.AftereffectMeasureReport],
                    keywords: ['后效数据', '人群画像数据'],
                },
            },
            {
                name: PageType.InsightReport,
                path: '/promotionSpecial/insightReport',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/DataCenter/insightReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.InsightReport],
                    keywords: ['人群画像数据', '分析路径', 'TOP搜索词', '触发模式分类占比'],
                },
            },
            ...(isFcCplUser() ? [{
                name: PageType.CplCampaignReport,
                path: '/promotionSpecial/cplCampaignReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.CplCampaignReport} supportPlatformSelect />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CplCampaignReport],
                },
            }] : []),
            {
                name: PageType.ProgramReport,
                path: '/promotionSpecial/programReport',
                component: lazy(() => import('@/modules/Ad/DataCenter/programReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.ProgramReport],
                },
            },
            {
                name: PageType.StoreReport,
                path: '/promotionSpecial/storeReport',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/DataCenter/storeReport')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.StoreReport],
                    keywords: ['店铺数据', '门店数据', '门店报告'],
                },
            },
            {
                name: PageType.LiftBudgetReport,
                path: '/crowdAnalysis/liftBudget',
                component: (props: any) => <TempleteReport type={PageType.LiftBudgetReport} supportPlatformSelect />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.LiftBudgetReport],
                },
            },
            {
                name: PageType.CrowdReport,
                path: '/crowdAnalysis/crowd',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.CrowdReport} />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.CrowdReport],
                },
            },
            {
                name: PageType.CrowdBidReport,
                path: '/special/crowdBidReport',
                platform: PLATFORM_ENUM.FEED,
                component: () => <TempleteReport type={PageType.CrowdBidReport} supportPlatformSelect />,
            },
            {
                name: PageType.GoodsReport,
                path: '/structuredProduct/goods',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.GoodsReport} supportPlatformSelect />,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.GoodsReport],
                },
            },
            {
                name: PageType.GoodsReport,
                path: '/structuredProduct/goods',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/DataCenter/goodsReport')),
            },
            {
                name: PageType.BusinessPoint,
                path: '/structuredProduct/businessPoint',
                component: () => (
                    <TempleteReport
                        type={PageType.BusinessPoint}
                        platform={isLawIndustryUser() && isFeedBusinessPointReportUser()
                            ? undefined : isLawIndustryUser() ? PLATFORM_ENUM.FC : PLATFORM_ENUM.FEED}
                        disablePlatformSelect={!isFeedBusinessPointReportUser() || !isLawIndustryUser()}
                        supportPlatformSelect
                    />
                ),
            },
            {
                name: PageType.MinmalProjectReport,
                path: '/minmalReport/minmalProjectReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.MinmalProjectReport} />,
            },
            {
                name: PageType.MinmalCreativeImageReport,
                path: '/minmalReport/minmalCreativeImageReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.MinmalCreativeImageReport} />,
            },
            {
                name: PageType.MinmalCreativeTitleReport,
                path: '/minmalReport/minmalCreativeTitleReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.MinmalCreativeTitleReport} />,
            },
            {
                name: PageType.MinmalCreativeDescReport,
                path: '/minmalReport/minmalCreativeDescReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.MinmalCreativeDescReport} />,
            },
            {
                name: PageType.MinmalQueryWordReport,
                path: '/minmalReport/minmalQueryWordReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.MinmalQueryWordReport} />,
            },
            {
                name: PageType.MinmalInvalidClickReport,
                path: '/minmalReport/minmalInvalidClickReport',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/DataCenter/minmalInvalidClickReport')),
            },
            {
                name: PageType.MinmalInvalidClueReport,
                path: '/minmalReport/minmalInvalidClueReport',
                platform: PLATFORM_ENUM.FC,
                component: () => <TempleteReport type={PageType.MinmalInvalidClueReport} />,
            },
            {
                name: PageType.MinmalRealtimeReport,
                path: '/minmalReport/minmalRealtimeReport',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/DataCenter/minmalRealtimeReport')),
            },
            {
                name: PageType.LeagueSpecialReport,
                path: '/minmalReport/leagueSpecialReport',
                platform: PLATFORM_ENUM.FEED,
                component: () => <TempleteReport type={PageType.LeagueSpecialReport} />,
            },
            {
                name: PageType.OcpxCampaignReport,
                path: '/minmalReport/ocpxCampaignReport',
                platform: PLATFORM_ENUM.FEED,
                component: () => <TempleteReport type={PageType.OcpxCampaignReport} />,
            },
            {
                name: PageType.VideoInsightReport,
                path: '/minmalReport/videoInsightReport',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/DataCenter/iframeReport')),
                props: {
                    levelName: PageType.VideoInsightReport,
                },
            },
            {
                name: 'DataCenterFallback',
                type: 'redirect',
                path: '/promotionReport/account',
            },
        ],
    },
    {
        name: 'AssetsCenter',
        path: `/${AdModuleType.AssetsCenter}/*`,
        component: lazy(() => import('@/modules/Ad/ToolsCenter/AssetsCenter')),
        children: [
            {
                name: PageType.Tools_PluginManage,
                path: '/pluginManage',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.AssetsOverview,
                path: '/assetsOverview',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/overview')),
                props: {
                    category: '资产中心',
                },
            },
            {
                name: PageType.Tools_NegativePackets,
                path: '/negativePackets',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                props: {},
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_NegativePackets],
                    keywords: ['否词包'],
                    order: 11,
                },
            },
            {
                name: PageType.Tools_ScheduleTemplates,
                path: '/scheduleTemplates',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                props: {},
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_ScheduleTemplates],
                    description: '统一新增并管理您的时段模版',
                    keywords: ['时段模板'],
                },
            },
            {
                name: PageType.Tools_PriceStrategy,
                path: '/priceStrategy',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_PriceStrategy],
                    description: '统一新增并管理您的优化排名出价策略',
                    keywords: ['CPC出价策略'],
                },
            },
            {
                name: PageType.Tools_SharedBudget,
                path: '/sharedBudget',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_ImageLibrary,
                path: '/imageLibrary',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_ImageLibrary],
                    keywords: ['创意中心'],
                    order: 17,
                },
            },
            {
                name: PageType.Tools_VideoLibrary,
                path: '/videoLibrary',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_VideoLibrary],
                    keywords: ['创意中心'],
                    order: 13,
                },
            },
            {
                name: PageType.Tools_Brands,
                path: '/brands',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_Brands],
                    description: '统一查看并管理您的品牌信息',
                    keywords: ['品牌信息', '品牌名称', '账户品牌'],
                    order: 15,
                },
            },
            {
                name: PageType.Tools_Bjh_Plugin,
                path: '/bjhPlugin',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_EventAsset,
                path: '/eventAsset',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_EventAsset],
                    description: '根据营销场景添加转化事件，监测转化，原转化追踪升级版',
                    keywords: ['事件资产', '转化事件', '转化联调'],
                    order: 12,
                },
            },
            {
                name: PageType.Tools_AppCenter,
                path: '/appCenter',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_AppCenter],
                    description: '统一查看管理您使用和绑定的应用',
                    keywords: ['Android应用', 'iOS应用'],
                    order: 18,
                },
            },
            {
                name: PageType.Tools_Track,
                path: '/track',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_Track],
                    description: '管理转化事件，完成转化事件设置与联调',
                    keywords: ['事件资产', '转化事件', '接入方式', '数据来源', '转化联调'],
                    order: 14,
                },
            },
            {
                name: PageType.Tools_TargetPackage,
                path: '/targetPackage',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_IDMP,
                path: '/idmp',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_FcCrowds,
                path: '/fcCrowds',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/FcCrowds')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_FcCrowds],
                    order: 16,
                },
            },
            {
                name: 'AssetsCenterFallback',
                type: 'redirect',
                path: '/assetsOverview',
            },
        ],
    },
    {
        name: 'ToolsCenter',
        path: `/${AdModuleType.ToolsCenter}/*`,
        component: lazy(() => import('@/modules/Ad/ToolsCenter/ToolsCenter')),
        children: [
            {
                name: PageType.ToolsOverview,
                path: '/toolsOverview',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/overview')),
                props: {
                    category: '工具中心',
                },
            },
            {
                name: PageType.Tools_History,
                path: '/history',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_History],
                    description: '查看历史操作内容和时间',
                    keywords: ['操作日志'],
                    order: 12,
                },
            },
            {
                name: PageType.Tools_TaskRecord,
                path: '/taskRecord',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_TaskRecord],
                    description: '查看历史操作内容和时间',
                    keywords: ['异步任务'],
                    order: 18,
                },
            },
            {
                name: PageType.Tools_ProductBatch,
                path: '/productBatch',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_KR,
                path: '/kr',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                hasSubTitle: true,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_KR],
                    order: 14,
                    description: '输入种子词获取相关关键词',
                    keywords: ['kr', '拓词', '加词', '榜单', '海量拓词', '流量查询', '添词', '新建关键词', '添加关键词'],
                },
            },
            {
                name: PageType.Tools_StyleAnalyse,
                path: '/styleAnalyse',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_StyleAnalyse],
                    description: '快捷查询广告所用的创意样式/组件',
                    keywords: ['样式识别', '点击串'],
                },
            },
            {
                name: PageType.Tools_Pna,
                path: '/pna',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_Pna],
                    description: '提交多样化创意组件',
                    keywords: ['图文样式', '全息', '橱窗', '导航样式', '列表样式'],
                    order: 13,
                },
            },
            {
                name: PageType.Tools_BusinessShield,
                path: '/businessShield',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_BusinessShield],
                    description: '屏蔽恶意点击IP',
                    keywords: ['屏蔽', 'IP屏蔽', '访客屏蔽', '点击屏蔽', '展现屏蔽'],
                    order: 15,
                },
            },
            {
                name: PageType.Tools_Adpreview,
                path: '/adpreview',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_Adpreview],
                    description: '预览广告展现样式，诊断无展现原因',
                    keywords: ['推广实况', '展现诊断', '查询实况'],
                    order: 11,
                },
            },
            {
                name: PageType.Tools_AutoRules,
                path: '/autoRules',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/autoRules')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_AutoRules],
                    description: '设定盯盘规则、自动执行，盯盘更高效',
                    keywords: ['自动规则', '自动执行', '盯盘规则'],
                    order: 17,
                },
            },
            {
                name: PageType.Tools_AutoAdviceRules,
                path: '/autoAdviceRules',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_KeywordPreCheck,
                path: '/keywordPreCheck',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_KeywordPreCheck],
                    description: '参考关键词字面和落地页进行预检',
                },
            },
            {
                name: PageType.Tools_NewDiagnosis,
                path: '/newDiagnosis',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_ClueRfq,
                path: '/clueRfq',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_ClueRfq],
                    keywords: ['线索加油包', 'rfq'],
                },
            },
            {
                name: PageType.Tools_AbnormalFluctuationDiagnose,
                path: '/abnormalFluctuationDiagnose',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_InvalidClueDiagnose,
                path: '/invalidClueDiagnose',
                platform: PLATFORM_ENUM.FC,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_InvalidClueDiagnose],
                    description: '线索有效性核查工具',
                },
            },
            {
                name: PageType.Tools_PaBatchTools,
                path: '/paBatchTools',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_IntentWordManage,
                path: '/intentWordManage',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_MediaManage,
                path: '/mediaManage',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/MediaManageContainer')),
            },
            {
                name: PageType.Tools_ideaWordsBag,
                path: '/ideaWordsBag',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: PageType.Tools_ReviewManage,
                path: '/reviewManage',
                platform: PLATFORM_ENUM.FEED,
                component: lazy(() => import('@/modules/Ad/ToolsCenter/feed/reviewManage')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.Tools_ReviewManage],
                    order: 16,
                },
            },
            {
                name: PageType.Tools_SuperManager,
                path: '/superManager',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
            },
            {
                name: 'ToolsCenterFallback',
                type: 'redirect',
                path: '/toolsOverview',
            },
        ],
    },
    {
        name: 'OptCenter',
        path: `/${AdModuleType.OptCenter}/*`,
        component: OptCenter,
        children: [
            {
                name: PageType.DiagnosisDashboard,
                path: '/diagnosisDashboard',
                label: (
                    <>
                        {PageName[PageType.DiagnosisDashboard]}
                        <GrayIconTip fontSize="16px" content="呈现账户整体消费、平均转化成本基础数据变化情况以及整体问题总览。" placement="top" />
                    </>
                ),
                component: lazy(() => import('@/modules/Ad/OptCenter/DiagnosisDashboard')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.DiagnosisDashboard],
                    keywords: ['账户诊断', '阻塞投放', '潜在问题', '潜力提升', '投放历程分析'],
                },
            },
            {
                name: PageType.IndustryInsights,
                path: '/industryInsights',
                component: lazy(() => import('@/modules/Ad/ToolsCenter/IframeContainer')),
                platform: PLATFORM_ENUM.FC,
                hasSubTitle: true,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.IndustryInsights],
                },
            },
            {
                name: PageType.AdvertiseDiagnosis,
                path: '/advertiseDiagnosis',
                label: PageType.AdvertiseDiagnosis,
                component: lazy(() => import('@/modules/Ad/OptCenter/AdvertiseDiagnosis')),
                isCustomShowPageTitle: true,
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.AdvertiseDiagnosis],
                    keywords: ['问题排查', '消费波动', '超成本', '消费突降'],
                },
            },
            {
                name: PageType.OptimizeAdvices,
                path: '/optimizeAdvices',
                label: '优化建议',
                component: lazy(() => import('@/modules/Ad/OptCenter/OptimizeAdvices')),
                quickLink: {
                    enabled: true,
                    title: PageName[PageType.OptimizeAdvices],
                    keywords: ['优化中心'],
                },
            },
        ],
    },
    // 搜索GUI创编
    {
        name: PageType.CreateProject,
        path: '/promotionMain/FC/createProject',
        label: '创建项目',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/project/createProject')),
        quickLink: {
            enabled: true,
            title: PageName[PageType.CreateProject],
            keywords: ['新建项目'],
        },
    },
    // 搜索原生互动GUI创编
    {
        name: PageType.CreateProjectV2,
        path: '/promotionMain/FC/createProject_v2',
        label: '创建项目',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/project/createProject_v2')),
    },
    {
        name: PageType.EditProject,
        path: '/promotionMain/FC/editProject',
        label: '编辑项目',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/project/editProject')),
    },
    {
        name: PageType.EditProjectV2,
        path: '/promotionMain/FC/editProject_v2',
        label: '编辑项目',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/project/editProject_v2')),
    },
    {
        name: PageType.AiBuildProject,
        path: '/promotionMain/FC/aiBuildProject',
        label: '智能省心投',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/project/aiBuildProject')),
    },
    {
        name: PageType.NewCampaign,
        path: '/promotionMain/FC/createCampaign',
        label: '新建方案',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/campaign')),
        quickLink: {
            enabled: true,
            title: PageName[PageType.NewCampaign],
            keywords: ['创建方案', '新建计划'],
        },
    },
    {
        name: PageType.NewAdgroup,
        path: '/promotionMain/FC/createAdgroup',
        label: '新建单元',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/adgroup')),
    },
    {
        name: PageType.CreateCPLCampaign,
        path: '/promotionMain/FC/createCPLCampaign',
        label: '创建服务直达方案',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/campaign/CPL')),
        quickLink: {
            enabled: true,
            title: PageName[PageType.CreateCPLCampaign],
            keywords: ['服务直达计划'],
        },
    },
    {
        name: PageType.NewCreative,
        path: '/promotionMain/FC/createCreative',
        label: '新建创意',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/creative/separateCreative')),
        quickLink: {
            enabled: true,
            title: PageName[PageType.NewCreative],
        },
    },
    {
        name: PageType.EditCreative,
        path: '/promotionMain/FC/editCreative',
        label: '编辑创意',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/creative/editCreative')),
    },
    {
        name: PageType.NewImage,
        path: '/promotionMain/FC/createImage',
        label: '新建图片',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/image')),
        quickLink: {
            enabled: true,
            title: PageName[PageType.NewImage],
        },
    },
    {
        name: PageType.NewVideo,
        path: '/promotionMain/FC/createVideo',
        label: '新建视频',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FC/video')),
        quickLink: {
            enabled: true,
            title: PageName[PageType.NewVideo],
        },
    },
    // 信息流GUI创编
    {
        name: PageType.CreateFeedProject,
        path: '/promotionMain/FEED/createProject',
        label: '新建项目',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/project/createProject')),
    },
    {
        name: PageType.FastLeague,
        path: '/promotionMain/FEED/createFastLeagueProject',
        label: '新建项目',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/project/fastLeagueProject/new')),
    },
    {
        name: PageType.EditFeedProject,
        path: '/promotionMain/FEED/editProject',
        label: '编辑项目',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/project/editProject')),
    },
    {
        name: PageType.EditFastLeague,
        path: '/promotionMain/FEED/editFastLeagueProject',
        label: '编辑项目',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/project/fastLeagueProject/edit')),
    },
    {
        name: PageType.CreateFeedCampaign,
        path: '/promotionMain/FEED/createCampaign',
        label: '新建方案',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/campaign/new')),
    },
    {
        name: PageType.CreateFeedCPLCampaign,
        path: '/promotionMain/FEED/createCPLCampaign',
        label: '新建服务直达方案',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/campaign/cpl')),
    },
    {
        name: PageType.EditFeedCampaign,
        path: '/promotionMain/FEED/editCampaign',
        label: '编辑方案',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/campaign/edit')),
    },
    {
        name: PageType.CreateFeedAdgroup,
        path: '/promotionMain/FEED/createAdgroup',
        label: '新建单元',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/adgroup/new')),
    },
    {
        name: PageType.EditFeedAdgroup,
        path: '/promotionMain/FEED/editAdgroup',
        label: '编辑单元',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/adgroup/edit')),
    },
    {
        name: PageType.CreateFeedCreative,
        path: '/promotionMain/FEED/createCreative',
        label: '新建创意',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/creative/new')),
    },
    {
        name: PageType.EditFeedCreative,
        path: '/promotionMain/FEED/editCreative',
        label: '编辑创意',
        component: lazy(() => import('@/modules/Ad/PromotionMain/FEED/creative/edit')),
    },
    {
        name: PageType.CreateLiteProject,
        path: '/promotionMain/FC/createLiteProject',
        label: '创建极简投项目',
        component: lazy(() => import('@/modules/Ad/LiteMain/FC/createLiteProject')),
    },
    {
        name: PageType.EditLiteProject,
        path: '/promotionMain/FC/editLiteProject',
        label: '编辑极简投项目',
        component: lazy(() => import('@/modules/Ad/LiteMain/FC/editLiteProject')),
    },
    ...(
        process.env.STAGE === 'ONLINE' ? [] : [
            {
                name: PageType.DEV_RichTextEditor,
                path: '/devRichTextEditor',
                label: '富文本编辑器',
                component: lazy(() => import('@/modules/DEV/RichTextEditor')),
            },
        ]
    ),
];


function patchRoutes(routes: RoutesType) {
    const normalizedRoutes = normalizeRoutes(routes);
    normalizedRoutes.getRoutesByPrefixs = partial(getRoutesByNamePrefixes, normalizedRoutes);
    normalizedRoutes.getPathByName = partial(getPathByName, normalizedRoutes, '/ad');
    normalizedRoutes.getItemByRouteFullPath = partial(getItemByRouteFullPath, normalizedRoutes);
    normalizedRoutes.getRoutesByPlatform = partial(getRoutesByPlatform, normalizedRoutes);
    return normalizedRoutes as Required<RoutesType>;
}

function normalizeRoutes(routes: RoutesType): RoutesType {
    function patch(routes: IRoute[], basePath: string = ''): RoutesType {
        return routes.map(function ff(route) {
            const fullPath = basePath + route.path.replace('/*', '');
            const children = route.children ? patch(route.children, fullPath) : undefined;
            return {
                ...route,
                fullPath,
                ...(children && {children}),
            };
        });
    }
    return patch(routes, '/ad');
}

export function getRoutesByPlatform(routes: RoutesType, platform: PLATFORM_ENUM) {
    const newRoutes = routes.filter(route => {
        return isNil(route.platform) || route.platform === platform;
    });

    return newRoutes.map(route => {
        if (route.children) {
            return {
                ...route,
                children: getRoutesByPlatform(route.children as any, platform),
            };
        }
        return route;
    });
}

function getRoutesByNamePrefixes(routes: RoutesType, prefixes: string[]) {
    let currentRoutes = routes;

    for (const prefix of prefixes) {
        const route = currentRoutes.find(route => route.name === prefix);
        if (route) {
            currentRoutes = route.children || [];
        }
        else {
            currentRoutes = currentRoutes.filter(route => route.fullPath?.startsWith(prefix));
        }
    }

    return currentRoutes;
}

function getPathByName(routes: RoutesType, basePath = '', name?: string): string | null {
    for (const route of routes) {
        const currentPath = basePath + route.path.replace('/*', '');

        if (route.name === name) {
            return currentPath;
        }

        if (route.children) {
            const childPath = getPathByName(route.children, currentPath, name);
            if (childPath) {
                return childPath;
            }
        }
    }

    return null;
}

function getItemByRouteFullPath(routes: RoutesType, path: string): IRoute | null {
    for (const item of routes) {
        if (item.fullPath === path) {
            return item;
        }
        if (item.children) {
            const result = getItemByRouteFullPath(item.children, path);
            if (result) {
                return result;
            }
        }
    }
    return null;
}

const AdRoutes = patchRoutes(routes);
export default AdRoutes;

interface ContextValue {
    adModule: string;
    switchToChat: (message?: PushMessageParams) => void; // 保留路由，并切 LUI
    adPageType?: PageType;
    adPageTitle?: ReactNode;
    isCustomShowPageTitle?: boolean;
    fromGuiPathname: string | null; // 记录切换时gui此刻的路由
    switchToGui: () => void; // 保留路由，并切 GUI
    getLink: (name: string, query_?: object) => string;
    linkTo: (name: string, query_?: object) => void;
    linkToChat: (message?: PushMessageParams) => void;
}
const AdRouteContext = createContext<ContextValue>({} as ContextValue);
export const useAdRoute = () => useContext(AdRouteContext);

export function AdRouteProvider({children}: {children: ReactNode}) {
    const history = useHistory();
    const {pathname} = useLocation();
    const [fromGuiPathname, setFromGuiPathname] = useState<string | null>(null);
    const adModule = pathname.split('/')[2] || AdModuleType.PromptCenter;
    const {
        name: adPageType, label: adPageTitle, isCustomShowPageTitle,
    } = AdRoutes.getItemByRouteFullPath?.(pathname) || {};

    const switchToChat = useCallback(
        async (message?: PushMessageParams) => {
            setFromGuiPathname(pathname);
            message && await aiChat.pushMessage(...message);
        },
        [pathname]
    );
    const switchToGui = useCallback(() => setFromGuiPathname(null), []);

    const getLink = useCallback((name: string, options: {inheritQuery?: boolean, query?: object} = {}) => {
        const {inheritQuery, query: query_} = options;
        const query = {
            userId: getUserId(),
            ...(inheritQuery ? queryString.parse(location.search) : {}),
            ...query_,
        };
        const toUrl = AdRoutes.getPathByName?.(name);
        return appendQuery(toUrl, query);
    }, []);

    const linkTo = useCallback(
        (name: string, {inheritQuery = false, query: query_, target}: {
            inheritQuery?: boolean; query?: object; target?: TargetTypeEnum;
        } = {}) => {
            const query = {
                userId: getUserId(),
                ...(inheritQuery ? queryString.parse(location.search) : {}),
                ...query_,
            };
            const toUrl = AdRoutes.getPathByName?.(name);

            if (toUrl) {
                if (toUrl !== fromGuiPathname) {
                    if (target === TargetTypeEnum._self) {
                        window.open(appendQuery(toUrl, query), '_self');
                    }
                    else if (target === TargetTypeEnum._blank) {
                        window.open(appendQuery(toUrl, query), '_blank');
                    }
                    else {
                        history.push(appendQuery(toUrl, query));
                    }
                }
                switchToGui(); // 命中则切换，不命中也得清理
                return;
            }
            if (name && name !== fromGuiPathname) {
                history.push(appendQuery(name, query));
            }
            switchToGui(); // 命中则切换，不命中也得清理
        },
        [history.push, fromGuiPathname, switchToGui]
    );

    const linkToChat = useCallback(
        async (message?: PushMessageParams) => {
            linkTo('/ad');
            message && await aiChat.pushMessage(...message);
        },
        [linkTo]
    );

    const ctxValue: ContextValue = {
        adModule,
        adPageType: adPageType as PageType,
        adPageTitle,
        fromGuiPathname,
        getLink,
        linkTo,
        linkToChat,
        switchToChat,
        switchToGui,
        isCustomShowPageTitle,
    };

    return (
        <AdRouteContext.Provider value={ctxValue}>
            {children}
        </AdRouteContext.Provider>
    );
}


export function getAdModule() {
    const pathname = window.location.pathname;
    const adModule = pathname.split('/')[2];
    return adModule;
}
