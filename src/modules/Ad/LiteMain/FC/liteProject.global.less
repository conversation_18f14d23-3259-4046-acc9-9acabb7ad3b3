.lite-project-form-container {
    width: 100%;
    min-height: calc(100% - 56px);
    padding: 24px;
    overflow: auto;
    z-index: 1;
    position: relative;
    max-width: 1392px;
    margin: 0 auto;

    .lite-project-form-container-inner {
        min-width: 1280px;
        gap: 16px;
        display: flex;
        flex-direction: column;
    }

    .lite-project-form-title {
        font-weight: 400;
        font-size: 24px;
        color: #0E0F11;
        margin-bottom: 12px;
        font-family: 'FZPinShangHeiS-B-GB';
        line-height: 27px;
        padding-left: 24px;
    }

    .lite-project-form {
        border-radius: 10px;
        padding: 24px;
        background-color: #fff;
        display: flex;
        flex-direction: column;
        gap: 24px;
        position: relative;

        .rf-form-item .rf-form-item-label {
            font-weight: 600;
        }

        .use-rf-preset-form-ui.use-label-top .rf-form-item .rf-form-item-label {
            margin-bottom: 8px;
        }
    }

    .lite-project-form-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 10px;
        padding: 16px 24px;
        background-color: #fff;
    }

    .lite-project-form-footer-left {
        display: flex;
        gap: 12px;
    }
}

.lite-main {
    width: 100%;
    flex: 1;
    background:
        url('https://fc-feed.bj.bcebos.com/aix/lite-form-bg.png?a=1') no-repeat top center / auto 300px,
        linear-gradient(270deg, #ECF3FD 0%, #F0F8FF 100%);
}
