.lite-project-entry {
    border-radius: 10px;
    border: 1px solid #E3EDFF;
    background:
        url('../../../../styles/assets/liteMain/lightning.svg') no-repeat,
        url('https://fc-feed.bj.bcebos.com/aix%2Flite-project-entry-bg-small.png') no-repeat;
    background-size: 50% 64px, auto 100%;
    background-position: top right, 72% -73px;

    // width > 2400 使用 bgs cover 防止背景图不够长变空白
    @media (min-width: 2400px) {
        background-size: 50% 64px, 100% 100%;
    }

    .header {
        padding: 20px 20px 14px;
        display: flex;
        align-items: center;

        .header-title {
            font-family: FZPinShangHeiS-B-GB;
            font-weight: 400;
            font-size: 20px;
            line-height: 1;
            margin-right: 24px;
        }

        .header-tag {
            display: flex;
            align-items: center;
            border-radius: 50px;
            padding: 4px 8px;
            gap: 4px;
            font-weight: 400;
            font-size: 12px;
            line-height: 16px;
            background: #FFFFFF80;
        }

        .header-tag + .header-tag {
            margin-left: 16px;
        }
    }

    .content {
        border-radius: 10px;
        padding: 20px;
        background-color: #fff;

        .lite-project-entry-name {
            font-weight: 500;
            font-size: 16px;
            line-height: 22px;
            color: #0E0F11;
            margin-bottom: 12px;
        }
    }

    .operations {
        display: flex;
        align-items: center;
        gap: 18px;

        .entry-btn {
            height: 48px;
            max-width: 244px;
            width: auto;
            flex: 1;
        }

        .combo-option {
            border-radius: 6px;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            background: #6D9FF712;
            cursor: pointer;

            .title {
                font-weight: 500;
                font-size: 14px;
                line-height: 20px;
                color: #191B1E;
            }

            .field {
                display: flex;
                align-items: center;

                &-name {
                    font-weight: 400;
                    font-size: 12px;
                    line-height: 16px;
                    color: #545B66;
                }

                &-value {
                    font-family: 'Baidu Number';
                    font-weight: 500;
                    font-size: 16px;
                    line-height: 24px;
                    color: #191B1E;
                    margin-left: 2px;
                    margin-right: 2px;
                }

                &-unit {
                    font-weight: 400;
                    color: #545B66;
                }
            }

            &:hover {
                background: #fff;
                outline: 1px solid #3A5BFD;

                .title {
                    color: #3A5BFD;
                }
            }

            &.selected {
                background: #fff;
                outline: 1px solid #3A5BFD;

                .title {
                    color: #3A5BFD;
                }
            }
        }
    }
}
