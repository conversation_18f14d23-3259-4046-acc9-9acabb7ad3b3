/*
 * @file: index
 * @Author: <EMAIL>
 * @Date: 2024-05-14 19:31:43
 */

import {useEffect, useState, useMemo, useCallback, memo} from 'react';
import {Pagination} from '@baidu/light-ai-react';
import {uniq} from 'lodash-es';
import classNames from 'classnames';
import {useRequest} from 'huse';
import {Skeleton} from '@baidu/light-ai-react';
import guid from '@baidu/guid';
import {useABTest} from '@baidu/abtest';
import {useInView} from 'react-intersection-observer';
import {sendOptCenterMonitor} from 'commonLibs/utils/sendHm';
import {
    useAdviceDisplayConfig,
} from 'commonApps/optimizeCenter/utils';
import {ADVICE_KEY} from '@/dicts/optCenter';
import {PLATFORM_ENUM} from '@/dicts';
import {useGlobalProductContext} from '@/hooks/productLine';
import {useRequestBySwr} from '@/hooks/request';
import {fetchSuggestCards, fetchRemainedFcOptCenterCards, metadataForRemainedFcOptCenterCards} from '@/api/homePage';
import {sendMonitor} from '@/utils/logger';
import {OptimizeAdviceSource} from '@/dicts/optimizeAdvice';
import {cardRecommender, ECardGroupKey, useGridColumns, mergeAndSortOptCardsData} from './util';
import {CardConfig, CardKey} from './config';
import './index.global.less';

const DefaultSuggestData: Record<CardKey, any[]> = Object.values(CardKey).reduce((memo, item) => {
    memo[item] = [];
    return memo;
}, {} as Record<CardKey, any[]>);

// 搜索和信息流优化中心是不同的path
const optCenterFcPath = 'AdviceService/queryOutline';
const optCenterFeedPath = 'AdviceService/queryFeedOutline';

const adviceCardKeys = [
    CardKey.KEYWORD_RISKY, CardKey.KEYWORD_SUGGEST, CardKey.ICG_SUGGEST,
    CardKey.TOOL_AUTO_RULE, CardKey.TOOL_AIMAX,
];

// 创建一个包装组件来处理视口检测
const CardWithInView_ = (props: {
    item: {key: CardKey, params: any};
    onClick: () => void;
    linkToOptDrawer: (url: string) => void;
    adviceFeConfig: any;
    pageNo: number;
}) => {
    const {
        item,
        onClick,
        linkToOptDrawer,
        adviceFeConfig,
        pageNo,
    } = props;
    const {ref, inView} = useInView({
        threshold: 0.5, // 当卡片50%进入视口时触发
        triggerOnce: true, // 只触发一次
    });

    useEffect(() => {
        if (inView && /^\/ad\/overview(\/)?$/.test(location.pathname)) {
            console.log(111111,  item.params.adviceKey || item.key);
            sendOptCenterMonitor('真实展现', {
                ...item.params,
                adviceKey: item.params.adviceKey || item.key,
                adviceId: item.params.adviceId || item.key,
                source: OptimizeAdviceSource.OVERVIEW,
                params: {pageNo},
            });
        }
    }, [inView]);

    const Comp = CardConfig[item.key] as any;
    const cardCls = classNames('card-item-container', `card-${item.key}-container`);

    return (
        <div ref={ref} className={cardCls} onClick={onClick}>
            {Comp
                ? (
                    <Comp
                        {...item.params}
                        linkToOptDrawer={linkToOptDrawer}
                        key={item.key}
                        adviceFeConfig={adviceFeConfig}
                    />
                )
                : JSON.stringify(item)
            }
        </div>
    );
};

const CardWithInView = memo(CardWithInView_);

export default function SuggestPrompt({linkToOptDrawer}: {linkToOptDrawer: (url: string) => void}) {
    const columns = useGridColumns();
    const pathname = location.pathname;
    const adviceFeConfig = useAdviceDisplayConfig();
    const {product} = useGlobalProductContext();
    const pageSize = columns * 2; // 每页展示卡片数
    const {data = DefaultSuggestData, pending: firstCardsPending} = useRequest(fetchSuggestCards, {});
    const {data: remainingData = DefaultSuggestData, pending: remainingCardsPending} = useRequestBySwr(
        fetchRemainedFcOptCenterCards,
        {},
        {metadata: metadataForRemainedFcOptCenterCards, noMutateIfHitPreloadCache: true},
    );

    const [pageNo, setPageNo] = useState(1); // 页码
    const [currentGroupLabel, setCurrentGroupLabel] = useState('全部'); // 卡片分类标签

    const {
        pending, displayCards, allLabels, totalPage,
    } = useMemo(
        () => {
            const allCards = mergeAndSortOptCardsData(data, remainingData, adviceFeConfig?.qgMaxPriority);
            const filteredCards = allCards.filter(
                ({groupLabel, params}) => {
                    const flag1 = groupLabel === currentGroupLabel || currentGroupLabel === '全部';
                    // 信息流产品线不展示关键词排名卡片
                    const flag2 = product === PLATFORM_ENUM.FC
                        || ![ADVICE_KEY.fcKeywordRanking].includes(params?.adviceKey);
                    return flag1 && flag2;
                }
            );
            let pending = firstCardsPending;
            // eslint-disable-next-line max-len
            const displayCards = cardRecommender.getDisplayCards(
                filteredCards,
                pageNo === 1,
                pageSize,
                columns,
            );
            // 如果是非首屏 或者 首屏卡优化建议类型过少，则需要首屏和剩余卡片都加载完毕后再展示卡片
            const optcenterCards = displayCards.filter(({groupLabel}) => groupLabel === ECardGroupKey.suggest);
            if (
                pageNo > 1
                || currentGroupLabel !== '全部'
                || optcenterCards.length < columns
            ) {
                pending = firstCardsPending || remainingCardsPending;
            }

            return {
                pending,
                totalPage: Math.ceil(filteredCards.length / pageSize),
                displayCards: pending ? [] : displayCards,
                allLabels: ['全部', ...uniq(allCards.map(({groupLabel}) => groupLabel))],
            };
        },
        [currentGroupLabel, pageNo, pageSize, columns, firstCardsPending, remainingCardsPending,
            data, remainingData, adviceFeConfig?.qgMaxPriority, product],
    );

    const handlePageChange = useCallback(
        (page, pageLocation = 'right') => {
            setPageNo(page);
            sendMonitor('click', {level: 'homepage', field: 'change_card', params: pageLocation});
        },
        [setPageNo]
    );
    const {pending: abtestPending, userstat} = useABTest();

    const cards = useMemo(
        () => {
            return pending ? null : displayCards.map((item: {
                key: CardKey;
                params: any;
            }) => {
                const onClick = () => {
                    if (adviceCardKeys.includes(item.key)) {
                        sendOptCenterMonitor('点击', {
                            adviceKey: item.params.adviceKey,
                            adviceId: item.params.adviceId,
                            source: OptimizeAdviceSource.OVERVIEW,
                            params: {pageNo},
                            userstat: abtestPending ? '' : userstat, // 还没获取到实验分流的时候不上报userstat
                        });
                    }
                    else {
                        sendMonitor('click', {
                            level: 'homepage',
                            field: `card_click_${item.key}`,
                        });
                        sendOptCenterMonitor('点击', {
                            adviceKey: item.key,
                            adviceId: item.key,
                            source: OptimizeAdviceSource.OVERVIEW,
                            params: {pageNo},
                            userstat: abtestPending ? '' : userstat, // 还没获取到实验分流的时候不上报userstat
                        });
                    }
                };
                return (
                    <CardWithInView
                        key={`${item.key}-${guid()}`}
                        item={item}
                        onClick={onClick}
                        linkToOptDrawer={linkToOptDrawer}
                        adviceFeConfig={adviceFeConfig}
                        pageNo={pageNo}
                    />
                );
            });
        },
        [pending, displayCards, pageNo, abtestPending, userstat, linkToOptDrawer, adviceFeConfig, pathname]
    );

    const handleChangeSelectedLabel = useCallback(
        label => {
            setCurrentGroupLabel(label);
            setPageNo(1);
            sendMonitor('click', {
                level: 'homepage',
                field: `card_label_click_${label}`,
            });
        },
        [setCurrentGroupLabel, setPageNo]
    );

    // !!加了setResourceTimingBufferSize(550)之后，资源粒度监控能够采集到html触发的api了，这部分采集逻辑可以下线掉
    // useEffect(
    //     () => {
    //         // 优化中心接口在html预触发的，在weirwood的资源粒度指标监控上看不到，原因未知，所以将这个接口的耗时作为自定义指标手动上报
    //         if (!firstCardsPending) {
    //             addCustomResTimingLogger([
    //                 {
    //                     customPerfName: 'x_qingge_queryOutline_api_duration',
    //                     isMultiple: false,
    //                     // eslint-disable-next-line max-len
    //                     isSendResourceTiming: ({name}) => name.includes(optCenterFcPath),
    //                 },
    //                 {
    //                     customPerfName: 'x_qingge_queryFeedOutline_api_duration',
    //                     isMultiple: false,
    //                     // eslint-disable-next-line max-len
    //                     isSendResourceTiming: ({name}) => name.includes(optCenterFeedPath),
    //                 },
    //             ]);
    //         }
    //     },
    //     [firstCardsPending]
    // );

    // 移除原来的展现监控逻辑，现在使用 CardWithInView 组件中的视口检测

    return (
        <div className="suggets-container" id="suggets-container">
            <div className="suggets-title">
                <div className="title">行动建议</div>
                <div className="label-select-list">
                    {allLabels.map((label: string) => (
                        <span
                            key={label}
                            className={classNames('label-select-list-item', {
                                'label-select-list-item-selected': currentGroupLabel === label,
                            })}
                            onClick={() => handleChangeSelectedLabel(label)}
                        >
                            {label}
                        </span>
                    ))}
                </div>
                {
                    totalPage > 1 && (
                        <Pagination
                            className="suggests-tip"
                            total={totalPage}
                            value={pageNo}
                            onChange={handlePageChange}
                        />
                    )
                }
            </div>
            <div
                className={'suggestions-card-list suggestions-card-list-grid-columns'}
                id="suggests-card-list"
            >
                {
                    pending ? (
                        Array(pageSize).fill(1).map((_, index) => {
                            return (
                                <div style={{width: '100%'}} key={guid()} className="suggets-card-item-loading">
                                    <Skeleton.Image active style={{width: '100%', height: '88px'}} />
                                    <Skeleton.Paragraph active rows={2} className="loading-paragraph" />
                                </div>
                            );
                        })
                    ) : cards
                }
            </div>
            {
                totalPage > 1 && (
                    <ul className="bottom-page-slick-dots">
                        {
                            Array(totalPage).fill(1).map(
                                // eslint-disable-next-line max-len, react/no-array-index-key
                                (_, index) => <li key={`suggest-pager-${index}`} className={pageNo === index + 1 ? 'slick-dot-active' : ''} onClick={() => handlePageChange(index + 1, 'bottom')} />
                            )
                        }
                    </ul>
                )
            }
        </div>
    );
}
