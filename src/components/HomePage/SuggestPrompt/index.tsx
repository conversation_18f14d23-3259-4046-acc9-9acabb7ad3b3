/*
 * @file: index
 * @Author: lian<PERSON>of<PERSON>@baidu.com
 * @Date: 2024-05-14 19:31:43
 */

import {useEffect, useState, useMemo, useCallback} from 'react';
import {Pagination} from '@baidu/light-ai-react';
import {uniq} from 'lodash-es';
import classNames from 'classnames';
import {useRequest} from 'huse';
import {Skeleton} from '@baidu/light-ai-react';
import guid from '@baidu/guid';
import {useABTest} from '@baidu/abtest';
import {sendOptCenterMonitor} from 'commonLibs/utils/sendHm';
import {
    useAdviceDisplayConfig,
} from 'commonApps/optimizeCenter/utils';
import {ADVICE_KEY} from '@/dicts/optCenter';
import {PLATFORM_ENUM} from '@/dicts';
import {useGlobalProductContext} from '@/hooks/productLine';
import {useRequestBySwr} from '@/hooks/request';
import {fetchSuggestCards, fetchRemainedFcOptCenterCards, metadataForRemainedFcOptCenterCards} from '@/api/homePage';
import {sendMonitor} from '@/utils/logger';
import {OptimizeAdviceSource} from '@/dicts/optimizeAdvice';
import {cardRecommender, ECardGroupKey, useGridColumns, mergeAndSortOptCardsData} from './util';
import {CardConfig, CardKey} from './config';
import './index.global.less';

const DefaultSuggestData: Record<CardKey, any[]> = Object.values(CardKey).reduce((memo, item) => {
    memo[item] = [];
    return memo;
}, {} as Record<CardKey, any[]>);

// 搜索和信息流优化中心是不同的path
const optCenterFcPath = 'AdviceService/queryOutline';
const optCenterFeedPath = 'AdviceService/queryFeedOutline';

const adviceCardKeys = [
    CardKey.KEYWORD_RISKY, CardKey.KEYWORD_SUGGEST, CardKey.ICG_SUGGEST,
    CardKey.TOOL_AUTO_RULE, CardKey.TOOL_AIMAX,
];

export default function SuggestPrompt({linkToOptDrawer}: {linkToOptDrawer: (url: string) => void}) {
    const columns = useGridColumns();
    const pathname = location.pathname;
    const adviceFeConfig = useAdviceDisplayConfig();
    const {product} = useGlobalProductContext();
    const pageSize = columns * 2; // 每页展示卡片数
    const {data = DefaultSuggestData, pending: firstCardsPending} = useRequest(fetchSuggestCards, {});
    const {data: remainingData = DefaultSuggestData, pending: remainingCardsPending} = useRequestBySwr(
        fetchRemainedFcOptCenterCards,
        {},
        {metadata: metadataForRemainedFcOptCenterCards, noMutateIfHitPreloadCache: true},
    );

    const [pageNo, setPageNo] = useState(1); // 页码
    const [currentGroupLabel, setCurrentGroupLabel] = useState('全部'); // 卡片分类标签

    const {
        pending, displayCards, allLabels, totalPage,
    } = useMemo(
        () => {
            const allCards = mergeAndSortOptCardsData(data, remainingData, adviceFeConfig?.qgMaxPriority);
            const filteredCards = allCards.filter(
                ({groupLabel, params}) => {
                    const flag1 = groupLabel === currentGroupLabel || currentGroupLabel === '全部';
                    // 信息流产品线不展示关键词排名卡片
                    const flag2 = product === PLATFORM_ENUM.FC
                        || ![ADVICE_KEY.fcKeywordRanking].includes(params?.adviceKey);
                    return flag1 && flag2;
                }
            );
            let pending = firstCardsPending;
            // eslint-disable-next-line max-len
            const displayCards = cardRecommender.getDisplayCards(
                filteredCards,
                pageNo === 1,
                pageSize,
                columns,
            );
            // 如果是非首屏 或者 首屏卡优化建议类型过少，则需要首屏和剩余卡片都加载完毕后再展示卡片
            const optcenterCards = displayCards.filter(({groupLabel}) => groupLabel === ECardGroupKey.suggest);
            if (
                pageNo > 1
                || currentGroupLabel !== '全部'
                || optcenterCards.length < columns
            ) {
                pending = firstCardsPending || remainingCardsPending;
            }

            return {
                pending,
                totalPage: Math.ceil(filteredCards.length / pageSize),
                displayCards: pending ? [] : displayCards,
                allLabels: ['全部', ...uniq(allCards.map(({groupLabel}) => groupLabel))],
            };
        },
        [currentGroupLabel, pageNo, pageSize, columns, firstCardsPending, remainingCardsPending,
            data, remainingData, adviceFeConfig?.qgMaxPriority, product],
    );

    const handlePageChange = useCallback(
        (page, pageLocation = 'right') => {
            setPageNo(page);
            sendMonitor('click', {level: 'homepage', field: 'change_card', params: pageLocation});
        },
        [setPageNo]
    );
    const {pending: abtestPending, userstat} = useABTest();
    const cards = useMemo(
        () => {
            return pending ? null : displayCards.map((item: {
                key: CardKey;
                params: any;
            }) => {
                const Comp = CardConfig[item.key];
                const cardCls = classNames('card-item-container', `card-${item.key}-container`);
                const onClick = () => {
                    if (adviceCardKeys.includes(item.key)) {
                        sendOptCenterMonitor('点击', {
                            adviceKey: item.params.adviceKey,
                            adviceId: item.params.adviceId,
                            source: OptimizeAdviceSource.OVERVIEW,
                            params: {pageNo},
                            userstat: abtestPending ? '' : userstat, // 还没获取到实验分流的时候不上报userstat
                        });
                    }
                    else {
                        sendMonitor('click', {
                            level: 'homepage',
                            field: `card_click_${item.key}`,
                        });
                        sendOptCenterMonitor('点击', {
                            adviceKey: item.key,
                            adviceId: item.key,
                            source: OptimizeAdviceSource.OVERVIEW,
                            params: {pageNo},
                            userstat: abtestPending ? '' : userstat, // 还没获取到实验分流的时候不上报userstat
                        });
                    }
                };
                return (
                    <div className={cardCls} key={`${item.key}-${guid()}`} onClick={onClick}>
                        {Comp
                            ? (
                                <Comp
                                    {...item.params}
                                    linkToOptDrawer={linkToOptDrawer}
                                    key={item.key}
                                    adviceFeConfig={adviceFeConfig}
                                />
                            )
                            : JSON.stringify(item)
                        }
                    </div>
                );
            });
        },
        [pending, displayCards, linkToOptDrawer, pageNo, adviceFeConfig]
    );

    const handleChangeSelectedLabel = useCallback(
        label => {
            setCurrentGroupLabel(label);
            setPageNo(1);
            sendMonitor('click', {
                level: 'homepage',
                field: `card_label_click_${label}`,
            });
        },
        [setCurrentGroupLabel, setPageNo]
    );

    // !!加了setResourceTimingBufferSize(550)之后，资源粒度监控能够采集到html触发的api了，这部分采集逻辑可以下线掉
    // useEffect(
    //     () => {
    //         // 优化中心接口在html预触发的，在weirwood的资源粒度指标监控上看不到，原因未知，所以将这个接口的耗时作为自定义指标手动上报
    //         if (!firstCardsPending) {
    //             addCustomResTimingLogger([
    //                 {
    //                     customPerfName: 'x_qingge_queryOutline_api_duration',
    //                     isMultiple: false,
    //                     // eslint-disable-next-line max-len
    //                     isSendResourceTiming: ({name}) => name.includes(optCenterFcPath),
    //                 },
    //                 {
    //                     customPerfName: 'x_qingge_queryFeedOutline_api_duration',
    //                     isMultiple: false,
    //                     // eslint-disable-next-line max-len
    //                     isSendResourceTiming: ({name}) => name.includes(optCenterFeedPath),
    //                 },
    //             ]);
    //         }
    //     },
    //     [firstCardsPending]
    // );

    useEffect(
        () => {
            if (displayCards && displayCards.length > 0 && /^\/ad\/overview(\/)?$/.test(pathname)) {
                displayCards.forEach((item: any) => {
                    if (item.params.adviceKey && item.params.adviceId) {
                        sendOptCenterMonitor('展现', {
                            ...item.params,
                            source: OptimizeAdviceSource.OVERVIEW,
                            params: {pageNo},
                            userstat: abtestPending ? '' : userstat, // 还没获取到实验分流的时候不上报userstat
                        });
                    }
                    else {
                        sendMonitor('click', {
                            level: 'homepage',
                            field: `card_view_${item.key}`,
                        });
                        sendOptCenterMonitor('展现', {
                            ...item.params,
                            adviceKey: item.key,
                            adviceId: item.key,
                            source: OptimizeAdviceSource.OVERVIEW,
                            params: {pageNo},
                            userstat: abtestPending ? '' : userstat, // 还没获取到实验分流的时候不上报userstat
                        });
                    }
                });
            }
        },
        [displayCards, pathname, abtestPending, userstat]
    );

    return (
        <div className="suggets-container" id="suggets-container">
            <div className="suggets-title">
                <div className="title">行动建议</div>
                <div className="label-select-list">
                    {allLabels.map((label: string) => (
                        <span
                            key={label}
                            className={classNames('label-select-list-item', {
                                'label-select-list-item-selected': currentGroupLabel === label,
                            })}
                            onClick={() => handleChangeSelectedLabel(label)}
                        >
                            {label}
                        </span>
                    ))}
                </div>
                {
                    totalPage > 1 && (
                        <Pagination
                            className="suggests-tip"
                            total={totalPage}
                            value={pageNo}
                            onChange={handlePageChange}
                        />
                    )
                }
            </div>
            <div
                className={'suggestions-card-list suggestions-card-list-grid-columns'}
                id="suggests-card-list"
            >
                {
                    pending ? (
                        Array(pageSize).fill(1).map((_, index) => {
                            return (
                                <div style={{width: '100%'}} key={guid()} className="suggets-card-item-loading">
                                    <Skeleton.Image active style={{width: '100%', height: '88px'}} />
                                    <Skeleton.Paragraph active rows={2} className="loading-paragraph" />
                                </div>
                            );
                        })
                    ) : cards
                }
            </div>
            {
                totalPage > 1 && (
                    <ul className="bottom-page-slick-dots">
                        {
                            Array(totalPage).fill(1).map(
                                // eslint-disable-next-line max-len, react/no-array-index-key
                                (_, index) => <li key={`suggest-pager-${index}`} className={pageNo === index + 1 ? 'slick-dot-active' : ''} onClick={() => handlePageChange(index + 1, 'bottom')} />
                            )
                        }
                    </ul>
                )
            }
        </div>
    );
}
