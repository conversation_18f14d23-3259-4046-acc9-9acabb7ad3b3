import React, {useCallback, useState} from 'react';
import {IconChevronDown} from 'dls-icons-react';
import {ConfigProvider, Button} from '@baidu/one-ui';
import {ABTestProvider} from '@baidu/abtest';
import IndustryCompetitiveness from 'manageCenter/industryCompetitiveness';
import useScrollTo from '@/hooks/scroll/useScroll';
import {useDurationStayByStyle} from '@/hooks/card/useDurationStay';
import {ModuleType} from '@/dicts';
import {sendMonitor} from '@/utils/logger';
import globalData from '@/utils/globalData';
import {getUserId} from '@/utils';
import {Instructions, LaunchAccountSettingEditorProps} from '@/utils/instructions';
import {isFcTaskCenterUser} from '@/utils/getFlag';
import {ASSIST_INSTRUCTION_KEY_MAP} from '@/modules/Ad/config';
import {useGlobalProductContext} from '@/hooks/productLine';
import {BanfeiTaskCenter} from '../EscortIncentiveSDK/EscortIncentiveTasks';
import SuggestPrompt from './SuggestPrompt';
import './style.global.less';
import AccountOverview from './AccountOverview';
import Banner from './Banner';
import IndustryTrends from './IndustryTrends';
import AccountSetting from './AccountSetting';
import {abTestConfig} from './abtConfig';

// 计算滚动高度偏差时允许4px的误差
const heightDeviation = 4;

const HomePage = ({
    instructions,
}: {
    instructions: Instructions;
}) => {
    const openAccountSetting = useCallback((params?: LaunchAccountSettingEditorProps) => {
        instructions[ASSIST_INSTRUCTION_KEY_MAP.LaunchAccountSettingEditor](params);
    }, [instructions]);
    const linkToOptDrawer = useCallback((url: string) => {
        instructions[ASSIST_INSTRUCTION_KEY_MAP.LaunchOptCenterDrawer](url);
    }, [instructions]);

    const containerElement = document.querySelector('.light-ai-slot-main') as HTMLElement;
    const {screenRef} = useDurationStayByStyle({field: ModuleType.Ad, level: 'chat_entry'});

    const {scrollToBottom: scrollToBottom_, backBottomVisible} = useScrollTo({
        target: () => containerElement, backVisibilityHeight: heightDeviation,
    });

    const scrollToBottom = () => {
        sendMonitor('click', {level: 'homepage', field: 'scrollToBottom'});
        scrollToBottom_();
    };
    const userId = getUserId();
    function injectScript() {
        // 获取真实实验值，注入FC的account
        window._hmt.push(['_setAccount', '863779a7a3a15a5b2bff4338a01f002e']);
        window._hmt.push(['_setUserId', userId]);
    }
    const accountTradeId = globalData.get('fcBasicData')?.basicInfo?.accountInfo?.ssgTradeId1st;
    const [banfeiTaskData, setBanfeiTaskData] = useState([]);
    const escortIncentiveTasksCount = !!banfeiTaskData.length;
    const {product} = useGlobalProductContext();
    return (
        <ABTestProvider abTestConfig={abTestConfig} injectScript={injectScript}>
            <div className="ai-chat-new-entry-container" ref={screenRef}>
                <ConfigProvider theme="light-ai">
                    <div className="ai-chat-entry-main">
                        <div className="ai-chat-entry-left">
                            <Banner />
                            <AccountOverview />
                            <SuggestPrompt linkToOptDrawer={linkToOptDrawer} />
                        </div>
                        <div className="ai-chat-entry-right">
                            <AccountSetting openAccountSetting={openAccountSetting} />
                            {isFcTaskCenterUser() && escortIncentiveTasksCount
                                && <BanfeiTaskCenter setBanfeiTaskData={setBanfeiTaskData} product={product} />}
                            <IndustryCompetitiveness accountTradeId={accountTradeId} />
                            <IndustryTrends />
                            {isFcTaskCenterUser() && !escortIncentiveTasksCount
                                && <BanfeiTaskCenter setBanfeiTaskData={setBanfeiTaskData} product={product} />}
                        </div>
                    </div>
                    {backBottomVisible && <Button
                        className="ai-chat-entry-to-bottom-btn"
                        type="basic"
                        size="medium"
                        icon={IconChevronDown}
                        onClick={scrollToBottom}
                    />}
                </ConfigProvider>
            </div>
        </ABTestProvider>
    );
};

export default HomePage;
