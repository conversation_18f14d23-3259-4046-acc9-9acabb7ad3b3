import dayjs from 'dayjs';
import {difference} from 'lodash-es';
import {getOperatorId} from '@/utils';
import {PageType} from '@/dicts/pageType';

const MAX_PAGETYPE_NUM = 4;
const LATEST_DAYS = 15;

const initialPageNameLists = [
    PageType.ProjectList,
    PageType.CampaignList,
    PageType.ReportWanHuaTong,
    PageType.AccountReport,
];

/**
 * @功能：显示by optid维度最近15天pv最高的4个GUI页面
 * @页面范围：除了首页ad和诊断优化，其余都包括
 * @初始化：项目管理、方案管理、我的报告、账户报告
 * @示例：
 * optIdPvMap = {
 *    optId1: {
 *        'date1': {
 *           pageType1: 2,
 *           pageType2: 1,
 *        },
 *        date2: {
 *           pageType4: 3,
 *        },
 *    },
 * }
*/
export const getMaxPvPages = () => {
    const optId = getOperatorId() || '';
    const optIdPvMap = JSON.parse(localStorage.getItem('optIdPvMap') || '{}');
    const datePvMap = optIdPvMap[optId] || {};
    const latestDates = Object.keys(datePvMap).filter(date => dayjs().diff(date, 'day') <= LATEST_DAYS);

    // 获取该optId 15天内的所有页面pv总和
    const totalPageTypePvMap = latestDates.reduce((pre, cur) => {
        const pageTypesMap = datePvMap[cur];
        for (const pageType in pageTypesMap) {
            if (pre[pageType]) {
                pre[pageType] += pageTypesMap[pageType];
            }
            else {
                pre[pageType] = pageTypesMap[pageType];
            }
        }
        return pre;
    }, {});

    // 对该optId 15天内的所有页面pv总和进行排序
    let maxPvPageNameLists = Object.keys(totalPageTypePvMap)
        .sort((a, b) => totalPageTypePvMap[b] - totalPageTypePvMap[a])
        .slice(0, MAX_PAGETYPE_NUM);

    // 如果没有4个页面，则使用初始化页面补全
    const minuslen = MAX_PAGETYPE_NUM - maxPvPageNameLists.length;
    if (minuslen) {
        maxPvPageNameLists = maxPvPageNameLists.concat(
            difference(initialPageNameLists, maxPvPageNameLists).slice(0, minuslen)
        );
    }
    return maxPvPageNameLists;
};
