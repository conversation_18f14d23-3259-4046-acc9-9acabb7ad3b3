import dayjs from 'dayjs';
import {productCategoryTypeEnum} from 'commonLibs/config/enumEntry';
import {FeedOcpcBidRatioTypeEnum, OcpcBidRatioTypeEnum} from '@/dicts/ocpcBidRatio';
import {ProjectBudgetType, AiTagEnum, ProjectSource, FcProjectModeType, FeedProjectType} from '@/dicts/project';
import {PRODUCT} from '@/dicts/campaign';
import globalData from '@/utils/globalData';
import {isFeedAimaxMedical, isFeedAimaxBusiness, isFeedShortPlayIndustryAimaxUser} from '@/utils/getFlag';
import {liftBudgetModeEnum} from '@/components/ChatCards/LiftBudget';
import {TradeEnum} from '@/dicts/tradeInfo';
import {FeedSubjectType} from '@/dicts/subject';
import {BJHType} from '@/components/Project/FeedEditor/BJHType/config';
import {BidModeEnum} from '@/interface/aixProject/feedProject';
import {SmartLiftBudgetEnum, AiMaxMarketingLevelEnum} from './pureConfig';

interface AiAgentOpenParams {
    budgetType: ProjectBudgetType;
    smartLiftBudget: SmartLiftBudgetEnum;
    aiTag?: AiTagEnum;
    aiMaxMarketingLevel: AiMaxMarketingLevelEnum | undefined;
    ocpcBidRatioType?: OcpcBidRatioTypeEnum;
    feedOcpcBidRatioType?: FeedOcpcBidRatioTypeEnum;
    projectSource?: ProjectSource;
    liftBudgetMode?: liftBudgetModeEnum;
    fcProjectModeType?: FcProjectModeType;
    feedProjectType?: FeedProjectType;
    aiMaxTradeVersionLevel?: number;
    smartBidCostControl?: boolean;
    smartTargetingCostControl?: boolean;
    smartInvalidClueControl?: boolean;
    smartBaseAdjustSwitch?: boolean;
    smartBaseControlCostSwitch?: boolean;
    smartLiftBid?: boolean;

}
// eslint-disable-next-line complexity
export function isAiAgentOpen({
    budgetType,
    smartLiftBudget,
    aiTag,
    aiMaxMarketingLevel,
    ocpcBidRatioType,
    feedOcpcBidRatioType,
    aiMaxTradeVersionLevel,
    projectSource,
    liftBudgetMode,
    fcProjectModeType,
    feedProjectType,
    smartBidCostControl,
    smartTargetingCostControl,
    smartInvalidClueControl,
    smartBaseAdjustSwitch,
    smartBaseControlCostSwitch,
    smartLiftBid,
}: AiAgentOpenParams) {
    if (projectSource === PRODUCT.FC) {
        return fcProjectModeType === FcProjectModeType.AIMAX;
    }
    if (projectSource === PRODUCT.FEED && feedProjectType != null) {
        return feedProjectType === FeedProjectType.AIMAX;
    }
    return aiMaxMarketingLevel !== undefined
        || !!aiMaxTradeVersionLevel
        || smartLiftBudget !== SmartLiftBudgetEnum.STOP
        || budgetType === ProjectBudgetType.WEEK
        || liftBudgetMode === liftBudgetModeEnum.appointWeekly
        || liftBudgetMode === liftBudgetModeEnum.appoint
        || aiTag === AiTagEnum.ON
        || smartBidCostControl
        || smartTargetingCostControl
        || smartInvalidClueControl
        || smartBaseAdjustSwitch
        || smartBaseControlCostSwitch
        || smartLiftBid
        || (
            (projectSource === PRODUCT.FC && !!ocpcBidRatioType)
            || (
                projectSource === PRODUCT.FEED
                && feedOcpcBidRatioType
                && feedOcpcBidRatioType !== FeedOcpcBidRatioTypeEnum.noUse
            )
        );
}

// 0-普通版，1-大健康，2-生活服务，3-教育，4-文娱软件 5-网络服务 6-婚恋相亲
// 7-房产家居 8-招商加盟 10-商服 11-法律
// 12 旅校通 13 AI行业
export enum AIMAX_TYPE {
    NORMAL = 0,
    MEDICAL = 1,
    LIFE = 2,
    EDUCATION = 3,
    ENTERTAINMENT = 4,
    NETWORK = 5,
    MARRIAGE = 6,
    HOUSE = 7,
    MERCHANT = 8,
    SHORTPLAY = 9,
    BUSINESS = 10,
    LAW = 11,
    TRAVAL = 12,
    AI = 13,
}

// 各个行业版对应的版本最新版
export const LATEST_AIMAX_VERSION = {
    [AIMAX_TYPE.MEDICAL]: 1,
    [AIMAX_TYPE.LIFE]: 2,
    [AIMAX_TYPE.EDUCATION]: 2,
    [AIMAX_TYPE.ENTERTAINMENT]: 1,
    [AIMAX_TYPE.NETWORK]: 1,
    [AIMAX_TYPE.MARRIAGE]: 1,
    [AIMAX_TYPE.HOUSE]: 1,
    [AIMAX_TYPE.MERCHANT]: 1,
    [AIMAX_TYPE.SHORTPLAY]: 1,
    [AIMAX_TYPE.BUSINESS]: 1,
    [AIMAX_TYPE.LAW]: 1,
    [AIMAX_TYPE.TRAVAL]: 1,
    [AIMAX_TYPE.AI]: 1,
};

// 各个行业对应的名称
export const TYPE_NAME_MAP = {
    [AIMAX_TYPE.NORMAL]: '通用版',
    [AIMAX_TYPE.MEDICAL]: '大健康',
    [AIMAX_TYPE.EDUCATION]: '教育',
    [AIMAX_TYPE.LIFE]: '生活服务',
    [AIMAX_TYPE.ENTERTAINMENT]: '文娱软件',
    [AIMAX_TYPE.NETWORK]: '网络服务',
    [AIMAX_TYPE.MARRIAGE]: '婚恋相亲',
    [AIMAX_TYPE.HOUSE]: '房产家居',
    [AIMAX_TYPE.MERCHANT]: '招商加盟',
    [AIMAX_TYPE.SHORTPLAY]: '短剧',
    [AIMAX_TYPE.BUSINESS]: '商业服务',
    [AIMAX_TYPE.LAW]: '法律服务',
    [AIMAX_TYPE.TRAVAL]: '旅游',
    [AIMAX_TYPE.AI]: 'AI行业',
};

// 各个行业对应的活动标语
export const TYPE_LABEL_MAP = {
    [AIMAX_TYPE.NORMAL]: '',
    [AIMAX_TYPE.MEDICAL]: '· 大健康-节点爆量冲刺',
    [AIMAX_TYPE.EDUCATION]: '· 教育-冲量锦囊',
    [AIMAX_TYPE.LIFE]: '· 生活服务-加速扩量',
    [AIMAX_TYPE.ENTERTAINMENT]: '· 文娱软件-节点冲刺放量',
    [AIMAX_TYPE.NETWORK]: '· 网络服务-节点冲刺放量',
    [AIMAX_TYPE.MARRIAGE]: '· 婚恋相亲-节点冲刺放量',
    [AIMAX_TYPE.HOUSE]: '· 房产家居-节点冲刺放量',
    [AIMAX_TYPE.MERCHANT]: '· 加盟-节点冲刺放量',
    [AIMAX_TYPE.SHORTPLAY]: '· 短剧-冲量锦囊',
    [AIMAX_TYPE.BUSINESS]: '· 商服-节点冲刺放量',
    [AIMAX_TYPE.LAW]: '· 法律服务-节点冲刺放量',
    [AIMAX_TYPE.TRAVAL]: '· 旅游-节点冲刺放量',
    [AIMAX_TYPE.AI]: '· AI-节点冲刺放量',
};

// 各个行业对应的id集合，[[一级行业id1, 一级行业id2, ...], [二级行业id1, 二级行业id2, ...]]
// 医疗医美全行业
const medicalIndustryIdList = [[2001, 2002, 2003, 2004]];

// 教育行业
const educationIndustryIdList = [[2019]];

// 生服行业，2007下除200712其余都为生服
const lifeIndustryIdList = [[2007], ['!200712']];

// 文娱行业
const entertainmentIndustryIdList = [[2008, 2017]];

// 网络行业
const networkIndustryIdList = [[2026]];

// 婚恋行业，2007-200712，
const marriageIndustryIdList = [[2007], [200712]];

// 房产家居行业
const houseIndustryIdList = [[2023], [202301, 202302, 202303, 202304, 202305, 202306, 202307, 202308]];

// 招商加盟行业
const merchantIndustryIdList = [[2030]];

// 短剧行业
const shortIndustryIdList = [[2008], [200812]];

const lawIndustryIdList = [[2006], [200604]];

const businessIndustryIdList = [[2006], ['!200604']];

const travelIndustryIdList = [[2020], [202004]];

const aiIndustryIdList = [[2033]];

// 各个行业对应的id集合
const TYPE_ID_MAP = {
    [AIMAX_TYPE.MEDICAL]: medicalIndustryIdList,
    [AIMAX_TYPE.EDUCATION]: educationIndustryIdList,
    [AIMAX_TYPE.LIFE]: lifeIndustryIdList,
    [AIMAX_TYPE.ENTERTAINMENT]: entertainmentIndustryIdList,
    [AIMAX_TYPE.NETWORK]: networkIndustryIdList,
    [AIMAX_TYPE.MARRIAGE]: marriageIndustryIdList,
    [AIMAX_TYPE.HOUSE]: houseIndustryIdList,
    [AIMAX_TYPE.MERCHANT]: merchantIndustryIdList,
    [AIMAX_TYPE.SHORTPLAY]: shortIndustryIdList,
    [AIMAX_TYPE.BUSINESS]: businessIndustryIdList,
    [AIMAX_TYPE.LAW]: lawIndustryIdList,
    [AIMAX_TYPE.TRAVAL]: travelIndustryIdList,
    [AIMAX_TYPE.AI]: aiIndustryIdList,
};


const medicalIndustryActivityTime = {
    200201: ['2024/09/01', '2024/12/31'],
    200203: ['2024/09/01', '2024/12/31'],
    200204: ['2024/09/01', '2024/12/31'],
    200205: ['2024/09/01', '2024/12/31'],
    200206: ['2024/09/01', '2024/12/31'],
    200211: ['2024/09/01', '2024/12/31'],
    200210: ['2024/09/01', '2024/12/31'],
    200202: ['2024/09/01', '2024/12/31'],
    200207: ['2024/07/01', '2024/08/31'],
    200108: ['2024/06/01', '2024/08/31'],
    200209: ['2024/06/01', '2024/08/31'],
    200109: ['2024/05/01', '2024/10/31'],
    200208: ['2024/05/01', '2024/10/31'],
    200104: ['2024/06/01', '2024/10/31'],
    200112: ['2024/07/01', '2024/10/31'],
    200110: ['2024/05/01', '2024/09/30'],
};

// 通过一级行业trade1来初步判断是哪个aimax行业并返回，不是aimax行业的话就返回通用版
export const getIndustry = () => {
    const {trade1, trade2} = globalData.get('tradeInfo');
    const {projectProduct} = globalData.get('projectStore') || {};
    for (const key in TYPE_ID_MAP) {
        const [trade1List, trade2List = []] = TYPE_ID_MAP[key];
        if (
            projectProduct === PRODUCT.FEED
            && [TradeEnum.YLQX, TradeEnum.BAOJIAN].includes(trade1)
            && isFeedAimaxMedical()
        ) {
            trade1List.push(TradeEnum.YLQX, TradeEnum.BAOJIAN);
        }
        const trade2InList = trade2List.filter(id => !String(id).includes('!'));
        const trade2OutList = trade2List.filter(id => String(id).includes('!'));
        if (trade1List.includes(trade1)
            && (trade2InList.includes(trade2) || trade2InList.length === 0)
            && (!trade2OutList.includes(`!${trade2}`))
        ) {
            return Number(key);
        }
    }
    return AIMAX_TYPE.NORMAL;
};

export const getEducationIndustry = () => {
    const {trade1} = globalData.get('tradeInfo');
    return educationIndustryIdList[0].includes(trade1);
};

export const getMedecalIndustry = () => {
    const {trade1} = globalData.get('tradeInfo');
    return medicalIndustryIdList[0].includes(trade1) && isFeedAimaxMedical();
};

export const getBusinessIndustry = () => {
    const {trade1} = globalData.get('tradeInfo');
    return businessIndustryIdList[0].includes(trade1) && isFeedAimaxBusiness();
};

export const getShortPlayIndustry = () => {
    const {trade1, trade2} = globalData.get('tradeInfo');
    return shortIndustryIdList[0].includes(trade1) && shortIndustryIdList[1].includes(trade2);
};

export const getProjectCardFeedAIMaxIndustry = () => {
    if (getEducationIndustry()) {
        return AIMAX_TYPE.EDUCATION;
    } else if (getMedecalIndustry()) {
        return AIMAX_TYPE.MEDICAL;
    } else if (getShortPlayIndustry()) {
        return AIMAX_TYPE.SHORTPLAY;
    } else if (getBusinessIndustry()) {
        return AIMAX_TYPE.BUSINESS;
    }
    return AIMAX_TYPE.NORMAL;
};

// eslint-disable-next-line complexity
export const getFeedAIMaxIndustry = ({
    subject,
    bjhType,
    bidMode,
}: {
    subject: FeedSubjectType;
    bjhType: BJHType;
    bidMode: BidModeEnum;
}) => {
    // 选择百家号推广目标，且场景为视频时，不能使用行业版，只能使用aimax通用版
    if (subject === FeedSubjectType.bjh && bjhType === BJHType.video) {
        return undefined;
    }
    if (getEducationIndustry()) {
        return AIMAX_TYPE.EDUCATION;
    } else if (getMedecalIndustry()) {
        return AIMAX_TYPE.MEDICAL;
    } else if (getBusinessIndustry()) {
        return AIMAX_TYPE.BUSINESS;
    } else if (
        getShortPlayIndustry()
        && isFeedShortPlayIndustryAimaxUser()
        && [FeedSubjectType.bjh].includes(subject)
        && bjhType === BJHType.shortPlay
        && bidMode === BidModeEnum.TRANS_COST
    ) {
        return AIMAX_TYPE.SHORTPLAY;
    }
    return undefined;
};

// 医疗医美部分冲量行业以及开启时间
// 注：2025年去掉了对时间、二级行业的限制这一特殊处理，直接by医疗 医美行业可见，所以这里直接返回true（限制逻辑还保留以防后续重新限制）
export const getSpecialMedecalActivity = () => {
    return true;
    // const {trade2} = globalData.get('tradeInfo');
    // const currentDate = dayjs();
    // const specialIndustyTimes = medicalIndustryActivityTime[trade2];
    // if (specialIndustyTimes) {
    //     const [startTime, endTime] = specialIndustyTimes;
    //     const isInDateRange = currentDate.isAfter(dayjs(startTime)) && currentDate.isBefore(dayjs(endTime));
    //     return isInDateRange;
    // }
    // return false;
};

// 获取各个行业对应的最终type
export const getIndustryType = () => {
    const industryType = getIndustry();
    if (industryType === AIMAX_TYPE.MEDICAL) {
        return Number(getSpecialMedecalActivity());
    }
    return industryType;
};

// 行业版一键起量任务和通用版一键起量任务需互斥的行业
export const AIMAX_EXCLUSIVE_TRADELIST = [
    AIMAX_TYPE.EDUCATION,
    AIMAX_TYPE.ENTERTAINMENT,
    AIMAX_TYPE.NETWORK,
    AIMAX_TYPE.MARRIAGE,
    AIMAX_TYPE.LIFE,
];

// 旅校通是否关联了产品
export const isLxtConnectedProduct = ({productCategoryType, structuredProductIds}: {
    productCategoryType: number;
    structuredProductIds: number[];
}) => {
    return productCategoryType === productCategoryTypeEnum.LXT && structuredProductIds?.length;
};
