/* eslint-disable complexity */
import AiChat from '@baidu/aichat';
import {get, isFunction} from 'lodash-es';
import stringifyJSON from 'fast-json-stable-stringify';
import {Avatar, OmniInputProps} from '@baidu/light-ai-react';
import {isDev} from '@/utils';
import {sendMonitor, MessageLogger, efficiencyLogger, sendEventByTag, measureCustomPerf} from '@/utils/logger';
// import {DraftLogger} from '@/utils/logger/draft';
import {getFailureMsg} from '@/utils/ajax';
import {
    SceneType,
    ASSIST_INSTRUCTION_KEY_BY_VALUE,
    ASSIST_INSTRUCTION_KEY_MAP,
    ASSIST_INSTRUCTION_VALUE_MAP_BY_KEY,
} from '@/modules/Ad/config';
import aiChatProxy from '@/utils/aiChat';
import globalData from '@/utils/globalData';
// import {clearDraftValue, getLastValidSessionIdInStorage} from '@/utils/draft';
import {sessionConfig} from '@/utils/config';
import cacheData from '@/utils/store/cacheData';
import {chatApi} from '@/api/chat';
import {CampaignResponse} from '@/interface/campaign';
import {record, sendScreenRecord, SingleRecord, RECORD_SEND_BOUNDARY, DEFAULT_RECORD_KEY} from '@/hooks/record';
import PromptPage from '../promptPage';
import {DebounceCancelledError} from '../FlashLink/SearchEngine';
import {processContentAndCards} from './processCards';
import {doPrefetcher, PrefetchedDataTagEnum} from './prefetch';
import {AiChatBot, Role, MessageInfo, ChatResult, EventData, triggerConfig} from './interface';
import {wrapSelectedWeekLabel, weekDayDataSourceMap} from './config/renderConfig';
import {HotKeyCampaignsOverlay} from './HotKeyOverlays/Campaign';
import {makeShortcuts} from './config/shortcuts';

const callback = (scene: SceneType) => {
    sendScreenRecord(`scene_${scene}_switched`);
    // 切换场景时检测录屏是否过大，过大则先发送当前录屏
    if (!isDev && SingleRecord.getEventsSize() > RECORD_SEND_BOUNDARY) {
        record.stop(DEFAULT_RECORD_KEY);
        SingleRecord.initRecord();
    }
};

/**
 * 基础指令， 其他指令依赖于这些指令
 * 例如预览+deeplink 同时出，需要保证预览先执行，因为 deeplink 依赖于预览组件挂载
 */
const BASE_INSTRUCTION = [
    ASSIST_INSTRUCTION_VALUE_MAP_BY_KEY.GetPreviewProgress,
] as const;


function initializeAiChatBot({
    instructions, accountInfo, linkToChat, commandExecutor,
}: AiChatBot) {
    const messageLogger = new MessageLogger();
    // const draftLogger = new DraftLogger();

    // FlashLink 集成逻辑
    // flashLinkCommandExecutor 通过参数传入


    // 初始化 FlashLink 相关的 props
    const initialShortcuts = commandExecutor.getShortcuts();

    const flashLinkProps = {
        shortcuts: initialShortcuts,
        shortcutVisible: false,
        shortcutsLayout: 'column',
        onEnterPress({
            onClear,
        }: any) {
            const {
                isSuccess,
                isChat,
            } = commandExecutor.handleEnterPress();
            isSuccess && onClear();
            // 返回true的时候，会让aichat不发送消息， 也就走不到onMessage 里了
            return isChat ? false : isSuccess;
        },
        onShortcutChange: commandExecutor.handleShortcutChange,
    };

    const aiChatInstance = new AiChat({
        onMessage: async (role: Role, messageInfo: MessageInfo) => {
            if (location.pathname.includes('/ad/overview') && role === 'user') {
                linkToChat();
            }
            aiChatProxy.triggerEventListener('message', {role, messageInfo, isSuccess: true});
            // 有些消息不需要销毁之前的对话信息（置灰按钮等操作），如知识问答类
            const isNeedExpiredHistory = messageInfo.extra?.expiredHistory ?? true;
            if (role === 'assistant' && isNeedExpiredHistory) {
                instructions[ASSIST_INSTRUCTION_KEY_MAP.ExpireHistoryCards]({trigger: 'newMessage'});
            }
            if (role === 'assistant') {
                efficiencyLogger.appendMessage(messageInfo.content.join('|'), 'assistant'); // update 不会执行 onMessage
                instructions[ASSIST_INSTRUCTION_KEY_MAP.ReceiveNewMessage](messageInfo);
                triggerConfig.resetTrigger();
                return;
            }
            const apiParams = await getChatApiParams(messageInfo, aiChatProxy, {hasInit: accountInfo.accountHasInit});
            const trigger = apiParams?.trigger || triggerConfig.getTrigger();
            efficiencyLogger.appendMessage(apiParams.text, 'user');
            const startTime = Date.now();
            measureCustomPerf('chat_timestamp');
            // const lastSessionIdInAddPlan = get(apiParams, 'payload.interactStorage.lastSessionIdInAddPlan.value');

            let msg: any = null;
            let cardsCache: Record<string, any> = {};
            const INSTRUCTION_CACHE: Record<string, any> = {};

            chatApi(apiParams, {
                // eslint-disable-next-line complexity, max-statements
                onFulfill: async (data: ChatResult) => {
                    const messageList = aiChatProxy.getMessageList();
                    if (!messageList.length) {
                        return;
                    }
                    const {answer, done, sessionId, scene, tag} = data;
                    // const previousSessionId = sessionConfig.getSessionId();
                    sessionConfig.setSessionId(sessionId);
                    instructions.updateGlobalSceneData({scene});
                    globalData.set({scene, isChatDone: done});

                    const {content, cards, instructions: instructionsFromApi, sugs} = answer;
                    const duration = Date.now() - startTime;
                    const campaignId = get(apiParams, 'payload.interactStorage.campaignId.value');
                    // 渲染富文本+卡片
                    const processResult = await processContentAndCards(data, {
                        msg, cardsCache, duration, campaignId, instructions,
                    });
                    msg = processResult.msg;
                    cardsCache = processResult.cardsCache;

                    // 执行指令
                    if (instructionsFromApi?.length) {
                        for (const {type, payload, execStrategy = 'once'} of instructionsFromApi) {
                            const key = stringifyJSON({type, payload});
                            if (execStrategy === 'once' && INSTRUCTION_CACHE[key]) {
                                continue;
                            }
                            instructions[ASSIST_INSTRUCTION_KEY_BY_VALUE[type]]({
                                ...(payload || {}),
                                sessionId,
                            });
                            INSTRUCTION_CACHE[key] = {execCount: (INSTRUCTION_CACHE[key]?.execCount || 0) + 1};

                            // ! 这里需要后端确保依赖顺序
                            if (BASE_INSTRUCTION.includes(type as typeof BASE_INSTRUCTION[number])) {
                                await new Promise(resolve => setTimeout(resolve, 100));
                            }
                        }
                    }

                    if (!done) {
                        return;
                    }
                    // const draftLogs = {
                    //     currentSessionId: sessionId,
                    //     scene,
                    //     tag,
                    //     instructions: instructionsFromApi,
                    // };
                    // draftLogger.afterBotResponse({
                    //     ...draftLogs,
                    //     previousSessionId,
                    //     lastSessionIdInAddPlan,
                    // });
                    // 用户的聊天记录下移到这里，这样 scene、field 都是最新的，和后端智能体口径一致（不再错配）
                    const field = cards?.[0]?.payload?.field;
                    messageLogger.afterUserMessage({scene, field, prompt: apiParams.text, trigger});
                    messageLogger.afterBotMessage({scene, field, prompt: content, duration, tag});
                    efficiencyLogger.onSceneChanged({scene, tag, card: cards?.[0], callback});
                    tag && sendEventByTag(tag);

                    aiChatProxy.triggerEventListener('messageComplete', {role, messageInfo, isSuccess: true, answer});
                    instructions[ASSIST_INSTRUCTION_KEY_MAP.Recover](undefined); // 加个undefined防止ts报错

                    // 方案创建成功或者明确放弃创建方案时，清除草稿缓存
                    // if (tag && [TagType.ADD_PLAN_ABORTED, TagType.ADD_PLAN_SUCCESS].includes(tag)) {
                    //     clearDraftValue(sessionId);
                    // }

                    // 在消息中披露 sugs
                    if (sugs?.length) {
                        aiChatProxy.setCustomSuggestions(sugs);
                    }
                    else {
                        aiChatProxy.clearSugs();
                    }
                },

                onError: async (response: any) => {
                    messageLogger.send({ // 智能体失败的消息记录不宜遗漏，用 default SceneType
                        role: 'user', scene: SceneType.DEFAULT, prompt: apiParams.text, level: trigger,
                    });
                    aiChatProxy.triggerEventListener('messageComplete', {
                        role,
                        messageInfo,
                        isSuccess: false,
                        errors: response?.errors || [],
                    });
                    const errMsg = getFailureMsg(
                        response,
                        {defaultErrorMsg: '我正在升级获取新的知识，升级完成后将为您提供更好的服务！请稍等片刻。'}
                    );

                    console.error(response);

                    messageLogger.send({ // 智能体错误、以及超时导致开小差，此时性能等也应该记录
                        role: 'bot', scene: SceneType.DEFAULT, prompt: errMsg, duration: Date.now() - startTime,
                    });

                    if (msg) {
                        await aiChatProxy.updateMessage(msg.id, errMsg, {isUpdating: false});
                    }
                    else {
                        msg = await aiChatProxy.instance.pushMessage('assistant', errMsg, {isUpdating: false});
                    }
                },
            });
            doPrefetcher(apiParams);
            aiChatProxy.setInputExtraInfo({});
            aiChatProxy.setInputContent('');
        },
        avatar: {
            user: (
                <Avatar shape="circle" className="user-avatar">
                    {accountInfo.optAccountName[0].toLocaleUpperCase()}
                </Avatar>
            ),
            assistant: (
                <Avatar shape="circle" className="assist-avatar">
                    <img src="https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png" />
                </Avatar>
            ),
        },
        simple: true,
        locale: {
            empty: (
                <div className="empty-container"><PromptPage optAccountName={accountInfo.optAccountName} /></div>
            ),
        },
        emptyContentChatCls: 'empty-content-chat-container',
        input: {
            ...flashLinkProps, // FlashLink 集成（包含 shortcuts等）
            onFocus() {
                commandExecutor.handleFocus();
                const {inputValue, selectedCommand} = commandExecutor.getCurrentState();
                if (!inputValue && !selectedCommand) {
                    aiChatInstance.setShortcutVisible(true);
                }
            },
            onBlur() {
                commandExecutor.handleBlur();
                aiChatInstance.setShortcutVisible(false);
            },
            hidePromptCenter: true,
            placeholder: '请输入您想搜索的功能或物料名称',
            maxRows: 4.25,
            maxLength: 3000,
            renderConfig: {
                week: {
                    placeholder: '选择周段',
                    options: weekDayDataSourceMap,
                    formatLabel: wrapSelectedWeekLabel,
                    editable: false,
                },
                dateRange: {
                    placeholder: '选择日期区间',
                    props: {
                        dateFormat: 'YYYY-MM-DD',
                    },
                },
            },
        },
        logger: ({data}: {data: EventData}) => {
            sendMonitor('instructions_center', {event: data.label});
        },
        useNewInput: true,
        inputOption: {
            render: HotKeyCampaignsOverlay,
        },
        style: {
            backgroundType: 'transparent',
        },
        onRestartChat: () => {
            instructions[ASSIST_INSTRUCTION_KEY_MAP.RestartChat]();
        },
    });
    aiChatProxy.instance = aiChatInstance;

    // 更新搜索建议的函数（现在使用内置防抖）
    const updateSearchSuggestions = async (inputValue: string) => {
        try {
            const suggestions = await commandExecutor.getSearchSuggestions(inputValue);
            // 只有最后一个请求成功时才会执行到这里
            aiChatInstance.setConfigByPath('input', {
                ...aiChatInstance.getConfig().input,
                searchSug: suggestions,
            });
        } catch (error) {
            // 被防抖取消的请求会被忽略，不需要特殊处理
            // 只有真正的错误才会被记录
            if (!(error instanceof DebounceCancelledError)) {
                console.error('Search suggestions failed:', error);
            }
        }
    };

    // 合并的输入变化处理逻辑
    const handleInputChange = (e: any) => {
        const messageList = aiChatProxy.getMessageList();
        if (!!messageList.length && (location.pathname === '/ad' || location.pathname === '/ad/')) {
            updateSearchSuggestions('');
            return;
        }
        // FlashLink 输入处理
        const result = commandExecutor.handleInputChange(e);
        const shortcutVisible = !result.inputValue && !result.selectedCommand;

        // 立即更新 shortcutVisible 状态（保持响应性）
        aiChatInstance.setShortcutVisible(shortcutVisible);

        // 触发搜索建议更新（内置防抖）
        updateSearchSuggestions(result.inputValue);

        aiChatProxy.setInputContent(result.originValue as any);

        // 通用输入监听逻辑（包括 clickShortcutButton 功能）
        const richValue = e.target.value;
        // 触发 triggerConfig.resetTrigger
        if (
            Array.isArray(richValue) && richValue.length <= 1
            || typeof richValue === 'string' && richValue.length < 1
        ) {
            triggerConfig.resetTrigger();
        }
    };

    aiChatProxy.instance.on('input.change', handleInputChange);

    setTimeout(() => {
        // light ai 有bug， setEditable 会触发onUpdate 导致 aichat 触发了focus， 这里需要取消自动focus
        const currentActiveEle = document.activeElement as HTMLElement;
        if (currentActiveEle && 'editor' in currentActiveEle) {
            currentActiveEle.blur();
        }
    }, 500);

    return {
        aiChat: aiChatProxy,
        disposeEvents: () => {
            efficiencyLogger.disposeEvents();
        },
    };
}

/* eslint complexity: ["error", 20] */
/**
 * 获取聊天 API 参数
 *
 * @param messageInfo 消息信息
 * @param aiChat AI 聊天对象
 * @param {hasInit} 是否初始化
 * @returns 聊天 API 参数对象
 */
async function getChatApiParams(messageInfo: MessageInfo, aiChat: typeof aiChatProxy, {hasInit}: {hasInit?: boolean}) {
    const {
        content: [initialContent],
        extra: {formatContentAndExtra, ...initialExtra} = {},
    } = messageInfo;

    let content = initialContent;
    let extra = initialExtra;
    if (isFunction(formatContentAndExtra)) {
        [content, extra] = formatContentAndExtra(initialContent, messageInfo.extra);
    }
    const {custom, trigger = triggerConfig.getTrigger()} = extra;

    const messageList = aiChat.getMessageList();
    const {
        content: [lastContent] = [],
        extra: lastExtra,
    } = messageList[messageList.length - 2] || {};

    const extraPayload = globalData.get('extraPayload') || {};
    const {interactStorage = {}, oncePayload = {}} = extraPayload;
    const postBackStorage: Record<string, any> = {};
    for (const field in interactStorage) {
        const {value, expire, trigger: triggers = []} = interactStorage[field];
        if (triggers.includes(trigger)) {
            postBackStorage[field] = {
                value,
                expire,
                trigger: triggers,
            };
        }
        if (expire === 'once') {
            delete interactStorage[field];
        }
    }

    await handleParamsIfOnlyOneCampaign(postBackStorage);
    // const draftSessionId = getLastValidSessionIdInStorage();
    // if (draftSessionId) {
    //     postBackStorage.lastSessionIdInAddPlan = {
    //         value: draftSessionId,
    //         expire: 'every',
    //         trigger: ['input', 'sug', 'card', 'gui'],
    //     };
    // }

    globalData.set({extraPayload: {...extraPayload, interactStorage, oncePayload: {}}});

    const {getChatProjectData} = await import('../Project/common/util');
    const chatProjectData = getChatProjectData();

    const {getChatFcThreeCampaignApiData} = await import('../newFcAd/common/util/normalizer');
    const chatFcThreeCampaignData = getChatFcThreeCampaignApiData();

    const {getFeedEditorChatData} = await import('../FeedEditor/common/getFeedEditorData');
    const chatFeedThreeCampaignData = getFeedEditorChatData();

    const chatFormData = {
        ...chatProjectData,
        ...chatFcThreeCampaignData,
        ...chatFeedThreeCampaignData,
    };

    if (lastExtra?.custom?.shouldSendGuide) {
        return {
            text: content,
            guide: lastContent,
            payload: {
                interactStorage: postBackStorage,
                ...oncePayload,
                ...chatFormData,
                ...custom,
                ...(lastExtra?.custom?.shouldSendGuidePayload ? lastExtra.custom : {}),
            },
            trigger,
            hasInit,
        };
    }
    return {
        text: content,
        payload: {
            interactStorage: postBackStorage,
            ...chatFormData,
            ...oncePayload,
            ...custom,
        },
        trigger,
        hasInit,
    };
}

// 只有一个计划的时候优化编辑流程
async function handleParamsIfOnlyOneCampaign(postBackStorage: Record<string, any>) {
    try {
        // 获取缓存的campaignId
        const cachedResult = cacheData.getDataByTag(PrefetchedDataTagEnum.ALL_CAMPAIGNS)?.[0];
        if (cachedResult) {
            // 防止一直不返回卡住用户下一句表达
            const campaignList = await Promise.race([cachedResult, []]) as CampaignResponse[];
            if (campaignList?.length === 1) {
                const campaignIds = campaignList.map(item => item.campaignId);
                postBackStorage.singleCampaignId = {value: campaignIds[0]};
            }
        }
    } catch (e) {
        // do nothing
    }
}


export function initializeAiChatBotOld({instructions, accountInfo, linkToChat}: AiChatBot) {

    const omniProps: Partial<OmniInputProps> = {
        placeholder: '请告诉我需要协助的事情',
        onShortcutChange: shortcut => {
            if (!shortcut) {
                return;
            }
            sendMonitor('instructions_center', {event: 'shortcut', prompt: shortcut.value});
            triggerConfig.setTrigger('input_instruction');
        },
        onChange: e => {
            // @ts-ignore
            if (e.target.value.length <= 1) {
                triggerConfig.resetTrigger();
            }
        },
    };
    const messageLogger = new MessageLogger();
    // const draftLogger = new DraftLogger();
    const aiChatInstance = new AiChat({
        onMessage: async (role: Role, messageInfo: MessageInfo) => {
            if (location.pathname.includes('/ad/overview') && role === 'user') {
                linkToChat();
            }
            aiChatProxy.triggerEventListener('message', {role, messageInfo, isSuccess: true});
            // 有些消息不需要销毁之前的对话信息（置灰按钮等操作），如知识问答类
            const isNeedExpiredHistory = messageInfo.extra?.expiredHistory ?? true;
            if (role === 'assistant' && isNeedExpiredHistory) {
                instructions[ASSIST_INSTRUCTION_KEY_MAP.ExpireHistoryCards]({trigger: 'newMessage'});
            }
            if (role === 'assistant') {
                efficiencyLogger.appendMessage(messageInfo.content.join('|'), 'assistant'); // update 不会执行 onMessage
                instructions[ASSIST_INSTRUCTION_KEY_MAP.ReceiveNewMessage](messageInfo);
                triggerConfig.resetTrigger();
                return;
            }
            const apiParams = await getChatApiParams(messageInfo, aiChatProxy, {hasInit: accountInfo.accountHasInit});
            const trigger = apiParams?.trigger || triggerConfig.getTrigger();
            efficiencyLogger.appendMessage(apiParams.text, 'user');
            const startTime = Date.now();
            measureCustomPerf('chat_timestamp');
            // const lastSessionIdInAddPlan = get(apiParams, 'payload.interactStorage.lastSessionIdInAddPlan.value');

            let msg: any = null;
            let cardsCache: Record<string, any> = {};
            const INSTRUCTION_CACHE: Record<string, any> = {};

            chatApi(apiParams, {
                // eslint-disable-next-line complexity, max-statements
                onFulfill: async (data: ChatResult) => {
                    const messageList = aiChatProxy.getMessageList();
                    if (!messageList.length) {
                        return;
                    }
                    const {answer, done, sessionId, scene, tag} = data;
                    // const previousSessionId = sessionConfig.getSessionId();
                    sessionConfig.setSessionId(sessionId);
                    instructions.updateGlobalSceneData({scene});
                    globalData.set({scene, isChatDone: done});

                    const {content, cards, instructions: instructionsFromApi, sugs} = answer;
                    const duration = Date.now() - startTime;
                    const campaignId = get(apiParams, 'payload.interactStorage.campaignId.value');
                    // 渲染富文本+卡片
                    const processResult = await processContentAndCards(data, {
                        msg, cardsCache, duration, campaignId, instructions,
                    });
                    msg = processResult.msg;
                    cardsCache = processResult.cardsCache;

                    // 执行指令
                    if (instructionsFromApi?.length) {
                        for (const {type, payload, execStrategy = 'once'} of instructionsFromApi) {
                            const key = stringifyJSON({type, payload});
                            if (execStrategy === 'once' && INSTRUCTION_CACHE[key]) {
                                continue;
                            }
                            instructions[ASSIST_INSTRUCTION_KEY_BY_VALUE[type]]({
                                ...(payload || {}),
                                sessionId,
                            });
                            INSTRUCTION_CACHE[key] = {execCount: (INSTRUCTION_CACHE[key]?.execCount || 0) + 1};

                            // ! 这里需要后端确保依赖顺序
                            if (BASE_INSTRUCTION.includes(type as typeof BASE_INSTRUCTION[number])) {
                                await new Promise(resolve => setTimeout(resolve, 100));
                            }
                        }
                    }

                    if (!done) {
                        return;
                    }
                    // const draftLogs = {
                    //     currentSessionId: sessionId,
                    //     scene,
                    //     tag,
                    //     instructions: instructionsFromApi,
                    // };
                    // draftLogger.afterBotResponse({
                    //     ...draftLogs,
                    //     previousSessionId,
                    //     lastSessionIdInAddPlan,
                    // });
                    // 用户的聊天记录下移到这里，这样 scene、field 都是最新的，和后端智能体口径一致（不再错配）
                    const field = cards?.[0]?.payload?.field;
                    messageLogger.afterUserMessage({scene, field, prompt: apiParams.text, trigger});
                    messageLogger.afterBotMessage({scene, field, prompt: content, duration, tag});
                    efficiencyLogger.onSceneChanged({scene, tag, card: cards?.[0], callback});
                    tag && sendEventByTag(tag);

                    aiChatProxy.triggerEventListener('messageComplete', {role, messageInfo, isSuccess: true, answer});
                    instructions[ASSIST_INSTRUCTION_KEY_MAP.Recover](undefined); // 加个undefined防止ts报错

                    // 方案创建成功或者明确放弃创建方案时，清除草稿缓存
                    // if (tag && [TagType.ADD_PLAN_ABORTED, TagType.ADD_PLAN_SUCCESS].includes(tag)) {
                    //     clearDraftValue(sessionId);
                    // }

                    // 在消息中披露 sugs
                    if (sugs?.length) {
                        aiChatProxy.setCustomSuggestions(sugs);
                    }
                    else {
                        aiChatProxy.clearSugs();
                    }
                },

                onError: async (response: any) => {
                    messageLogger.send({ // 智能体失败的消息记录不宜遗漏，用 default SceneType
                        role: 'user', scene: SceneType.DEFAULT, prompt: apiParams.text, level: trigger,
                    });
                    aiChatProxy.triggerEventListener('messageComplete', {
                        role,
                        messageInfo,
                        isSuccess: false,
                        errors: response?.errors || [],
                    });
                    const errMsg = getFailureMsg(
                        response,
                        {defaultErrorMsg: '我正在升级获取新的知识，升级完成后将为您提供更好的服务！请稍等片刻。'}
                    );

                    console.error(response);

                    messageLogger.send({ // 智能体错误、以及超时导致开小差，此时性能等也应该记录
                        role: 'bot', scene: SceneType.DEFAULT, prompt: errMsg, duration: Date.now() - startTime,
                    });

                    if (msg) {
                        await aiChatProxy.updateMessage(msg.id, errMsg, {isUpdating: false});
                    }
                    else {
                        msg = await aiChatProxy.instance.pushMessage('assistant', errMsg, {isUpdating: false});
                    }
                },
            });
            doPrefetcher(apiParams);
            aiChatProxy.setInputExtraInfo({});
            aiChatProxy.setInputContent('');
        },
        avatar: {
            user: (
                <Avatar shape="circle" className="user-avatar">
                    {accountInfo.optAccountName[0].toLocaleUpperCase()}
                </Avatar>
            ),
            assistant: (
                <Avatar shape="circle" className="assist-avatar">
                    <img src="https://fc-feed.cdn.bcebos.com/aix/aiAvatar.png" />
                </Avatar>
            ),
        },
        simple: true,
        locale: {
            empty: (
                <div className="empty-container"><PromptPage optAccountName={accountInfo.optAccountName} /></div>
            ),
        },
        emptyContentChatCls: 'empty-content-chat-container',
        input: {
            ...omniProps,
            placeholder: '请描述您的问题',
            maxRows: 4.25,
            maxLength: 3000,
            shortcuts: makeShortcuts(),
            renderConfig: {
                week: {
                    placeholder: '选择周段',
                    options: weekDayDataSourceMap,
                    formatLabel: wrapSelectedWeekLabel,
                    editable: false,
                },
                dateRange: {
                    placeholder: '选择日期区间',
                    props: {
                        dateFormat: 'YYYY-MM-DD',
                    },
                },
            },
        },
        logger: ({data}: {data: EventData}) => {
            sendMonitor('instructions_center', {event: data.label});
        },
        useNewInput: true,
        inputOption: {
            render: HotKeyCampaignsOverlay,
        },
        style: {
            backgroundType: 'transparent',
        },
        onRestartChat: () => {
            instructions[ASSIST_INSTRUCTION_KEY_MAP.RestartChat]();
        },
    });
    aiChatProxy.instance = aiChatInstance;

    return {
        aiChat: aiChatProxy,
        disposeEvents: () => {
            efficiencyLogger.disposeEvents();
        },
    };
}


export default initializeAiChatBot;
