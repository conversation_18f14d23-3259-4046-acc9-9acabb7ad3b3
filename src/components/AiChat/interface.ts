import {OmniInputProps} from '@baidu/light-ai-react';
import {Instructions} from '@/utils/instructions';
import {BasicInfo} from '@/hooks/contexts/basicInfo';
import {AssistInstructionType, SceneType, TagType} from '@/modules/Ad/config';
import {LaunchAccountSettingEditorProps} from '@/utils/instructions';
import {useFlashLinkCommandExecutor} from '@/components/FlashLink/hooks';
import {Card} from '../ChatCards';
import {Trigger} from './config/triggerConfig';

export * from './config/triggerConfig';
export type Role = 'assistant' | 'user';

export interface ChatOptions {
    isUpdating?: boolean;
    custom?: object;
    initial?: boolean;
    trigger?: Trigger;
    expiredHistory?: boolean;
    formatContentAndExtra?: (content: string, extra?: ChatOptions) => [string, ChatOptions];
}
export interface MessageInfo {
    content: string[];
    extra?: ChatOptions;
}

export interface Sug {
    content: string;
    prompt?: string;
}

/**
 * 后端接口返回的指示
 */
interface InstructionBase<Type extends number, Payload extends Record<string, any>> {
    /**
     * 指示类型
     */
    type: Type; // 后续可以扩展其他的, 根据最终后端接口可以改成number
    /**
     * 指示内容
     */
    payload: Payload;
    /**
     * 智能体指定的指令执行策略
     */
    execStrategy?: 'once' | 'always';
}

export interface InitialStaticGUIProps {
    instructions: Array<InstructionBase<AssistInstructionType, any>>;
}
interface ChatAnswer extends InitialStaticGUIProps {
    content: string;
    cards: Card[];
    sugs: Sug[];
}

export interface ChatResult {
    sessionId: string;
    answer: ChatAnswer;
    done: boolean;
    scene?: SceneType;
    tag?: TagType;
    expiredHistory?: boolean;
    conversationId?: number;
}

interface BaseBot {
    instructions: Instructions;
    omniProps?: Partial<OmniInputProps>;
}

export type MessageCallback = (params: {
    role: Role;
    messageInfo: MessageInfo;
    isSuccess: boolean;
    answer?: ChatAnswer;
    errors?: any[];
}) => void;

export interface AiChatBot extends BaseBot {
    accountInfo: Pick<BasicInfo['accountInfo'], 'optAccountName'> & {accountHasInit?: boolean};
    linkToChat: () => void;
    // FlashLink 命令执行器
    commandExecutor: ReturnType<typeof useFlashLinkCommandExecutor>;
}

export interface InitialPayloadState {
    trigger?: Trigger;
    custom?: object;
}
export interface AiChatBotProps extends BaseBot {
    initialPrompt?: string;
    initialPayload?: InitialPayloadState;
}

interface AiChatSuggestOption {
    autoClear?: boolean;
    onClick?: (suggest: AiChatSuggest) => void;
}
export interface AiChatSuggest extends AiChatSuggestOption {
    content: string;
    prompt?: string;
}

export interface EventData {
    label: string;
    value: string;
}

export type OpenAccountSettingMethod = (params?: LaunchAccountSettingEditorProps) => void;
