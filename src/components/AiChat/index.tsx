import {useEffect, useContext} from 'react';
import {useAccountInfo} from '@/hooks';
import {BasicInfoContext} from '@/hooks/contexts';
import {sessionConfig} from '@/utils/config';
import {useAdRoute} from '@/modules/Ad/routes';
import {useFlashLinkCommandExecutor} from '@/components/FlashLink/hooks';
import {isFlashLinkUser} from '@/utils/getFlag';
import {useGlobalProductContext} from '@/hooks/productLine';
import {PLATFORM_ENUM} from '@/dicts';
import {AiChatBotProps} from './interface';
import initializeAiChatBot, {initializeAiChatBotOld} from './bot';
import './style.global.less';

export default (props: AiChatBotProps) => {
    const {initialPrompt, initialPayload, instructions} = props;
    const [{optAccountName}] = useAccountInfo();
    const {basicInfo: {aixAccountInitInfo: {hasInit, hasBrandInfo} = {}}} = useContext(BasicInfoContext);
    const {linkToChat} = useAdRoute();
    // 使用 hook 创建 FlashLink 命令执行器
    const commandExecutor = useFlashLinkCommandExecutor({
        enableFallback: false,
        adModule: 'overview',
    });
    const {product} = useGlobalProductContext();

    const isShowFlashLink = isFlashLinkUser() && product === PLATFORM_ENUM.FC;

    useEffect(
        () => {
            const bot = (
                isShowFlashLink ? initializeAiChatBot : initializeAiChatBotOld
            )({
                instructions,
                accountInfo: {optAccountName, accountHasInit: hasInit && hasBrandInfo},
                linkToChat,
                // FlashLink 命令执行器
                commandExecutor,
            });

            bot.aiChat.renderChat(document.getElementById('ai-chat') as HTMLElement);

            initialPrompt && bot.aiChat.pushMessage(
                'user',
                initialPrompt,
                {isUpdating: true, ...initialPayload}
            );

            return () => {
                const {aiChat, disposeEvents} = bot;
                if (aiChat) {
                    aiChat.clearMessage();
                    aiChat.setInputContent('');
                }
                disposeEvents?.();
                sessionConfig.resetSessionId();
            };
        },
        [
            initialPrompt, initialPayload,
            optAccountName, hasInit, hasBrandInfo,
            commandExecutor, instructions, linkToChat,
            product, isShowFlashLink,
        ]
    );

    return (
        <div id="ai-chat" className={isShowFlashLink ? 'flash-link-ai-chat' : ''} />
    );
};
