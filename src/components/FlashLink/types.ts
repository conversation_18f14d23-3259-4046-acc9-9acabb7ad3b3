/**
 * FlashLink 核心接口定义
 *
 * 定义了命令系统的所有核心接口和类型
 */

import {OmniInputProps} from '@baidu/light-ai-react';
import {PLATFORM_ENUM} from '@/dicts';
import {PushMessageParams} from '@/utils/aiChat';
import {RoutesType} from '@/modules/Ad/routes';

// 命令参数接口
export interface ActionParams {
  command: QuickLinkCommand; // 选中的命令
  inputText?: string; // 二次输入的文本
  linkTo: (name: string, options?: any) => void; // 路由跳转方法
  linkToChat: (message?: PushMessageParams) => void; // 跳转到聊天的路由方法
  pushHistory: (path: string) => void; // 历史记录推送方法
  product: PLATFORM_ENUM; // 当前平台/产品信息
  extra?: Record<string, any>; // 预留扩展参数
}

// 命令执行接口
export interface CommandAction {
    execute(params: ActionParams): void | Promise<void>;
}

// 命令定义接口
export interface QuickLinkCommand {
  id: string; // 命令唯一标识
  type: 'navigation' | 'search' | 'tool'; // 命令类型
  adModule?: string;
  pageType?: string;
  title: string; // 命令标题
  description?: string; // 命令描述
  keywords?: string[]; // 搜索关键词
  action: CommandAction; // 命令执行器
  requiresInput?: boolean; // 是否需要二次输入
  defaultDisplay?: boolean; // 是否作为默认展示项
  order?: number; // 排序权重，数字越小优先级越高
  inheritInputValue?: boolean;
  isHistory?: boolean; // 是否来自历史记录（埋点相关）
}

// 类型标签映射
export const CommandTypeLabels = {
    navigation: '跳转',
    search: '查询',
    tool: '工具',
} as const;

export type CommandType = keyof typeof CommandTypeLabels;

// 高亮片段接口
export interface HighlightSegment {
    text: string;
    highlighted: boolean;
}

// 搜索结果接口
export interface SearchResult {
    command: QuickLinkCommand;
    score: number; // 匹配得分，用于排序
    matchType: 'exact' | 'fuzzy' | 'pinyin' | 'pinyin-abbr' | 'fallback';
    matchedField: 'title' | 'description' | 'keywords' | 'fallback'
    | 'titlePinyin.initials' | 'titlePinyin.fullPinyin'
    | 'typeLabelPinyin.initials'
    | 'typeLabelPinyin.fullPinyin'
    | 'descriptionPinyin.initials'
     | 'descriptionPinyin.fullPinyin' | 'combinedPinyinInitials' | 'combinedPinyinFull';
    highlightSegments: HighlightSegment[]; // 结构化高亮片段
}

// 简化的搜索配置接口
export interface SearchConfig {
  threshold: number; // 匹配阈值，0.3 为合理值
  maxResults: number; // 最大结果数
  enablePinyin: boolean; // 是否启用拼音搜索
}

// FlashLink 状态接口
export interface FlashLinkState {
  inputValue: string; // 当前输入值
  selectedCommand?: QuickLinkCommand; // 选中的命令（二次输入模式）
}

// FlashLink 主组件 Props
export interface FlashLinkProps {
  inputRef: OmniInputProps['instance'];
}

// 搜索引擎配置
export interface SearchEngineConfig {
  commands: QuickLinkCommand[];
  config?: Partial<SearchConfig>;
}

// 路由配置的 QuickLink 扩展
export interface RouteQuickLinkConfig {
  enabled: boolean | (() => boolean);
  title: string;
  description?: string;
  keywords?: string[];
  defaultDisplay?: boolean; // 是否作为默认展示项
  order?: number; // 排序权重，数字越小优先级越高
}

// 命令注册配置
export interface CommandRegistryConfig {
    // 搜索配置
    searchConfigs?: SearchCommandConfig[];
    // 工具配置
    toolConfigs?: ToolCommandConfig[];
    // 导航配置
    navigationConfigs?: NavigationCommandConfig[];
    // 路由配置
    routes?: Required<RoutesType>;
    // 当前平台
    platform?: PLATFORM_ENUM;
}

// URL构建参数接口
export interface UrlBuilderParams {
  inputText?: string; // 搜索输入文本
  platform: PLATFORM_ENUM; // 当前平台
  userId?: string; // 用户ID
}

// 基础命令配置接口
export interface BaseCommandConfig {
    id: string;
    title: string;
    description?: string;
    keywords?: string[];
    supportedPlatforms?: PLATFORM_ENUM[]; // 支持的平台列表，如果不设置则支持所有平台
    defaultDisplay?: boolean; // 是否作为默认展示项
    order?: number; // 排序权重，数字越小优先级越高
    requiresInput?: boolean;
    inheritInputValue?: boolean; // requiresInput 为true时， 选择命令是否带入原来输入框的值
    action?: CommandAction;
}

// 搜索命令配置
export interface SearchCommandConfig extends BaseCommandConfig {
    supportsFallback?: boolean; // 是否支持作为 fallback 搜索
    urlBuilder: (params: UrlBuilderParams) => string; // URL构建函数
}

// 工具命令配置
export interface ToolCommandConfig extends BaseCommandConfig {
    action: CommandAction;
}

// 导航命令配置
export interface NavigationCommandConfig extends BaseCommandConfig {
    action: CommandAction;
    adModule?: string;
}

// 备选搜索配置
export interface FallbackSearchConfig {
    enabled: boolean;
    searchTargets: Array<{
        id: string;
        title: string;
        urlPath: string;
    }>;
}

// OmniInput 适配器接口
export interface OmniInputSugOption {
    label: string;
    value: string;
    desc?: string;
}

// 事件回调接口
export interface FlashLinkEventCallbacks {
    onCommandSelect?: (command: QuickLinkCommand) => void;
    onInputChange?: (inputValue: string) => void;
    onSearchComplete?: (results: SearchResult[]) => void;
}

// 默认搜索配置
export const DEFAULT_SEARCH_CONFIG: SearchConfig = {
    threshold: 0.3,
    maxResults: 8,
    enablePinyin: true,
};

// 命令类型枚举
export enum CommandTypes {
    NAVIGATION = 'navigation',
    SEARCH = 'search',
    TOOL = 'tool'
}

// 匹配类型枚举
export enum MatchTypes {
    EXACT = 'exact',
    FUZZY = 'fuzzy',
    PINYIN = 'pinyin',
    PINYIN_ABBR = 'pinyin-abbr',
    FALLBACK = 'fallback'
}

// 模式枚举
export enum FlashLinkModes {
    COMMAND = 'command',
    INPUT = 'input'
}

// 物料搜索常量
export const GLOBAL_SEARCH_CONSTANTS = {
    COMMAND_ID: 'global_search',
    COMMAND_PREFIX: 'global_search_',
    TITLE: '物料搜索',
    DESCRIPTION: '搜索项目/方案/单元/关键词/创意物料',
} as const;
