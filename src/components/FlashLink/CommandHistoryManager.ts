/**
 * 命令历史记录管理器
 *
 * 负责管理 FlashLink 命令的使用历史，包括:
 * - 保存命令使用记录
 * - 按使用频率和时间加权排序
 * - 本地存储持久化
 */

import {PLATFORM_ENUM} from '@/dicts';
import {getUserId} from '@/utils';

// 历史记录接口
export interface CommandHistory {
  commandId: string; // 命令ID
  title: string; // 命令标题
  inputText?: string; // 二次输入的内容（如果有）
  timestamp: number; // 执行时间戳
  count: number; // 使用次数
  platform: PLATFORM_ENUM; // 执行时的平台
}

/**
 * 命令历史记录管理器
 */
export class CommandHistoryManager {
    private static readonly STORAGE_KEY = `flash_link_history:${getUserId()}`;
    private static readonly MAX_HISTORY_SIZE = 100;

    /**
   * 添加命令到历史记录
   */
    addToHistory(newHistory: Omit<CommandHistory, 'count'>): void {
        const histories = this.getHistoryList();
        const existingIndex = histories.findIndex(
            h => h.commandId === newHistory.commandId
                && h.platform === newHistory.platform
        );

        if (existingIndex >= 0) {
            // 更新现有记录的使用次数、时间戳、标题和输入文本
            histories[existingIndex].count += 1;
            histories[existingIndex].timestamp = newHistory.timestamp;
            histories[existingIndex].title = newHistory.title;
            histories[existingIndex].inputText = newHistory.inputText;
        } else {
            // 添加新记录
            histories.unshift({...newHistory, count: 1});
        }

        // 限制历史记录数量
        if (histories.length > CommandHistoryManager.MAX_HISTORY_SIZE) {
            histories.splice(CommandHistoryManager.MAX_HISTORY_SIZE);
        }

        this.saveHistoryList(histories);
    }

    /**
   * 获取最近使用的命令
   */
    getRecentCommands(limit: number = 2, platform?: PLATFORM_ENUM): CommandHistory[] {
        const histories = this.getHistoryList();

        // 按平台过滤（如果指定了平台）
        const filteredHistories = platform
            ? histories.filter(h => h.platform === platform)
            : histories;

        // 按使用频率和时间加权排序
        return filteredHistories
            .sort((a, b) => {
                const scoreA = this.calculateScore(a);
                const scoreB = this.calculateScore(b);
                return scoreB - scoreA;
            })
            .slice(0, limit);
    }

    /**
   * 获取最近搜索的内容（用于展示历史搜索记录）
   */
    getRecentSearches(commandId: string, limit: number = 5): string[] {
        const histories = this.getHistoryList();

        return histories
            .filter(h => h.commandId === commandId && h.inputText)
            .sort((a, b) => b.timestamp - a.timestamp)
            .slice(0, limit)
            .map(h => h.inputText!)
            .filter((text, index, array) => array.indexOf(text) === index); // 去重
    }

    /**
   * 清空历史记录
   */
    clearHistory(): void {
        localStorage.removeItem(CommandHistoryManager.STORAGE_KEY);
    }


    /**
   * 删除特定命令的历史记录
   */
    removeCommandHistory(commandId: string): void {
        const histories = this.getHistoryList().filter(h => h.commandId !== commandId);
        this.saveHistoryList(histories);
    }

    /**
   * 获取历史统计信息
   */
    getHistoryStats(): {
    totalCommands: number;
    totalUsages: number;
    topCommands: Array<{ commandId: string, title: string, count: number }>;
    } {
        const histories = this.getHistoryList();

        const commandUsage = new Map<string, { title: string, count: number }>();
        let totalUsages = 0;

        histories.forEach(h => {
            const existing = commandUsage.get(h.commandId);
            if (existing) {
                existing.count += h.count;
            } else {
                commandUsage.set(h.commandId, {title: h.title, count: h.count});
            }
            totalUsages += h.count;
        });

        const topCommands = Array.from(commandUsage.entries())
            .map(([commandId, data]) => ({commandId, ...data}))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);

        return {
            totalCommands: commandUsage.size,
            totalUsages,
            topCommands,
        };
    }

    /**
   * 计算命令的加权得分
   */
    private calculateScore(history: CommandHistory): number {
        const now = Date.now();
        const daysPassed = (now - history.timestamp) / (1000 * 60 * 60 * 24);

        // 时间衰减因子：越近期使用得分越高，7天半衰期
        const timeDecay = Math.exp(-daysPassed / 7);

        // 使用频率权重：对数增长，避免频率过度影响
        const frequencyWeight = Math.log(history.count + 1);

        // 基础分数，避免得分为0
        const baseScore = 1;

        return baseScore + (timeDecay * frequencyWeight);
    }

    /**
   * 从本地存储获取历史记录
   */
    private getHistoryList(): CommandHistory[] {
        try {
            const stored = localStorage.getItem(CommandHistoryManager.STORAGE_KEY);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.warn('Failed to load command history:', error);
            return [];
        }
    }

    /**
   * 保存历史记录到本地存储
   */
    private saveHistoryList(histories: CommandHistory[]): void {
        try {
            localStorage.setItem(
                CommandHistoryManager.STORAGE_KEY,
                JSON.stringify(histories)
            );
        } catch (error) {
            console.warn('Failed to save command history:', error);
            // 如果存储失败，可能是存储空间不足，尝试清理旧记录
            if (histories.length > 50) {
                const reducedHistories = histories.slice(0, 50);
                try {
                    localStorage.setItem(
                        CommandHistoryManager.STORAGE_KEY,
                        JSON.stringify(reducedHistories)
                    );
                } catch (retryError) {
                    console.error('Failed to save reduced command history:', retryError);
                }
            }
        }
    }
}

// 导出单例实例
let historyManagerInstance: CommandHistoryManager | null = null;

export function getCommandHistoryManager(): CommandHistoryManager {
    if (!historyManagerInstance) {
        historyManagerInstance = new CommandHistoryManager();
    }
    return historyManagerInstance;
}
