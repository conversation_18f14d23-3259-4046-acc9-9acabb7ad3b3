# FlashLink 全局命令中心设计文档

## 1. 功能概述

FlashLink 是一个类似 VSCode 命令中心的全局快捷导航功能，支持快速定位到各种路由、执行快捷操作。该功能通过模糊匹配、拼音匹配、拼音首字母匹配等算法，帮助用户快速找到所需功能。

### 1.0 最新更新（v3.0 - 物料搜索功能改造）

**新增 UI 收起/展开功能**：用户可以手动控制FlashLink的显示状态

**v2.2 新功能特点**：
- 🔽 **展开收起**: 新增展开/收起按钮，用户可手动控制FlashLink显示状态
- 💾 **状态持久化**: 收起状态保存到localStorage，页面刷新后保持用户选择
- 🎯 **智能聚焦**: 展开时自动聚焦到输入框，收起时自动失焦
- 📐 **底部条适配**: 自动检测管理中心页面底部条，智能调整位置
- 🎨 **平滑动画**: 添加过渡动画，提升用户体验
- ⚡ **性能优化**: 优化搜索算法，调整threshold为0.2，改进组合拼音搜索

**v2.1 更新（Fallback 功能）**：
- 🔍 **智能备选**: 当常规搜索无结果时，自动展示基于查询词的备选搜索命令
- 🎯 **引导发现**: 帮助用户在不同模块中查找包含查询词的内容
- 💾 **记录历史**: Fallback 命令也会记录到历史中，方便用户快速重复搜索
- ⚡ **按需生成**: 只在需要时动态生成，性能优化
- 🔧 **接口优化**: 移除了未使用的冗余字段，优化代码结构

**v2.0 架构变更**：
- 🔄 **交互模式**: 从模态弹窗改为底部固定输入框
- 🏠 **页面适配**: 首页、对话页面 隐藏 FlashLink，使用 AiChat 输入框；非首页显示底部输入框
- 📱 **Mini 模式**: 默认 mini 状态，聚焦时展开显示完整功能
- ⌨️ **快捷键优化**: 统一快捷键行为，根据页面类型选择聚焦目标
- 🎯 **执行优化**: 执行命令后自动失焦并 mini 化

### 1.1 核心特性

- **全局调用**: 支持快捷键（如 Ctrl+Shift+P）全局唤起
- **智能搜索**: 支持模糊匹配、拼音匹配、拼音首字母匹配
- **二次输入**: 支持需要参数的命令（如搜索功能）
- **简化高亮**: 仅对字面完全匹配的文本进行高亮显示
- **快速定位**: 直接跳转到指定路由或执行相应操作
- **物料搜索**: 主动选择搜索类型，动态生成各类搜索操作

## 2. 用户交互设计

### 2.1 触发方式

- **全局快捷键**: `Ctrl+Shift+P` (Windows/Linux) 或 `Cmd+Shift+P` (Mac)
  - **首页模式**: 聚焦现有的 AiChat 输入框
  - **非首页模式**: 自动展开（如果收起）并聚焦底部固定的 FlashLink 输入框
- **点击触发**: 直接点击底部固定输入框
- **自动显示**: 非首页自动显示底部输入框

### 2.2 默认展示机制

#### 2.2.1 展示优先级

当用户首次打开 FlashLink 或输入框为空时，按以下优先级展示：

1. **历史记录（最高优先级）**: 显示用户最近使用过的命令
   - 保存最近 10 条使用记录
   - 按使用频率和时间加权排序
   - 优先展示最近 5 条记录

2. **默认推荐**: 智能推荐相关命令
   - 优先展示与当前页面模块相关的命令
   - 根据页面路径自动识别当前模块（例如manageCenter、dataCenter等）
   - 排序规则：
     - 首先显示当前模块内的命令（按order排序）
     - 过滤掉当前页面本身的命令（避免重复）
     - 然后显示其他模块的命令（按order排序）
   - 最多显示8个快捷命令，历史记录最多4个

#### 2.2.2 展示策略

```
┌─────────────────────────────────────────────────┐
│  搜索命令... (输入命令或路径)                      │
├─────────────────────────────────────────────────┤
│  🕒 最近使用                                     │
│      项目列表                                     │
│      智能搭建                                     │
│  ──────────────────────────────────────────────  │
│  💡 推荐功能（当前模块优先）                       │
│      项目报告                                     │
│      单元列表                                     │
│      关键词管理                                   │
│      账户报告                                     │
└─────────────────────────────────────────────────┘
```

**注意**: 显示格式已简化，移除了类型标签（如[跳转]、[搜索]等），直接显示命令标题。

### 2.3 交互流程

#### 2.3.1 基础交互流程

1. **唤起**: 用户按下 `Ctrl+Shift+P`
2. **搜索**: 输入关键词，实时过滤命令列表
3. **选择**: 方向键选择命令，或直接点击
4. **执行**: Enter 确认执行命令

#### 2.3.2 二次输入流程

1. **选择搜索命令**: 选择需要二次输入的命令（如"搜索项目"）
2. **切换输入模式**: 界面切换到二次输入状态
3. **输入参数**: 输入具体的搜索内容
4. **执行搜索**: Enter 确认，跳转到搜索结果页面

### 2.4 界面布局

#### 2.4.1 收起状态（用户主动收起）

```
┌─────────────────────────────────────────────────┐
│                                                 │
│                     [↑]                         │  ← 展开按钮，带阴影
│                                                 │
└─────────────────────────────────────────────────┘
```

#### 2.4.2 底部固定输入框（默认 mini 模式）

```
┌─────────────────────────────────────────────────┐
│              [开启智能搜索]                       │  ← 底部居中固定
│                     [↓]                         │  ← 收起按钮，hover显示
└─────────────────────────────────────────────────┘
```

#### 2.4.3 展开模式（聚焦时）

```
┌─────────────────────────────────────────────────┐
│  请输入搜索内容     ctrl + shift + P 可快速启动   │
├─────────────────────────────────────────────────┤
│  🕒 最近使用                                     │
│      [跳转] 项目列表                              │
│      [搜索] 关键词 > "运动鞋"                     │
│  ──────────────────────────────────────────────  │
│  💡 推荐功能                                     │
│      [跳转] <mark>项目</mark>列表                 │
│      [跳转] <mark>项目</mark>报告                 │
│      [搜索] <mark>项目</mark> (需要二次输入)       │
│                     [↓]                         │  ← 收起按钮，hover显示
└─────────────────────────────────────────────────┘
```

#### 2.4.4 页面模式适配

- **首页模式** (`/ad`、`/ad/`、`/ad/overview`、`/ad/overview/`): FlashLink 输入框隐藏，快捷键直接聚焦 AiChat 输入框
- **管理中心模式** (`/ad/manageCenter/*`): 自动添加 `has-bottom-bar` 类，向上偏移40px避开底部条
- **非首页模式**: 显示底部固定的 FlashLink 输入框

### 2.5 键盘交互

- **方向键**: 上下选择命令
- **Enter**: 确认执行当前选中的命令，执行后自动失焦 mini 化
- **Esc**: 返回上级（从二次输入返回命令列表）
- **Backspace**: 在二次输入模式下，清空时返回命令列表
- **失焦**: 输入框自动 mini 化，隐藏快捷方式列表

### 2.6 搜索交互特性

- **实时搜索**: 输入时进行过滤结果（搜索引擎内置防抖优化）
- **智能匹配**: 支持拼音搜索，如输入 "xm" 匹配 "项目"
- **简化高亮**: 仅对字面完全匹配的文本进行高亮显示
- **拼音搜索**: 拼音和缩写搜索能找到结果但不高亮显示
- **物料搜索**: 主动选择物料搜索命令，动态生成各类搜索操作

#### 2.6.1 物料搜索流程

**场景一：常规搜索**
```
用户输入: "项目"
→ 搜索匹配到: "项目列表", "项目报告", "搜索项目"
→ 用户选择后直接执行
```

**场景二：物料搜索**
```
1. 用户选择"物料搜索"
→ 进入物料搜索模式
2. 用户输入: "汽车广告"
→ 动态生成:
  - "查看项目名称包含『汽车广告』的物料"
  - "查看方案名称包含『汽车广告』的物料"
  - "查看单元名称包含『汽车广告』的物料"
→ 用户选择后直接跳转到对应搜索结果页面
```

## 3. 命令类型设计

### 3.1 命令分类

采用扁平化结构，按功能类型分为三类：

#### 3.1.1 [跳转] 类命令

**用途**: 页面导航，复用现有路由配置

**特点**:
- 直接执行，无需二次输入
- 从路由配置自动生成
- 支持关键词搜索

**示例**:
- `[跳转] 项目列表` → 跳转到 `/ad/ManageCenter/projectList`
- `[跳转] 账户报告` → 跳转到 `/ad/ManageCenter/accountReport`

**路由配置示例**:
```typescript
{
  name: PageType.ProjectList,
  path: '/projectList',
  component: lazy(() => import('@/modules/Ad/ManageCenter/projectList')),
  label: '项目列表',
  quickLink: {
    enabled: true,
    title: '项目列表',
    description: '查看和管理所有投放项目',
    keywords: ['项目', 'project', '投放'],
    defaultDisplay: true,     // 是否作为默认展示项
    order: 1                  // 排序权重，数字越小优先级越高
  }
}
```

#### 3.1.2 [查询] 类命令

**用途**: 带参数的搜索功能，需要二次输入

**特点**:
- 需要用户输入搜索关键词
- 支持平台特定的配置和URL构建
- 通过 urlBuilder 函数灵活构建不同平台的搜索URL
- **支持 Fallback**: 标记 `supportsFallback: true` 的命令可用于生成 fallback

**示例**:
- `[查询] 项目` → 输入项目名称 → 跳转到项目列表搜索结果
- `[查询] 关键词` → 输入关键词 → 跳转到关键词列表搜索结果

**配置示例**:
```typescript
{
  id: 'search_project',
  title: '搜索项目',
  description: '根据项目名称查找项目',
  keywords: ['项目', 'project', '搜索'],
  order: 1,
  supportsFallback: true,
  supportedPlatforms: [PLATFORM_ENUM.FC, PLATFORM_ENUM.FEED], // 支持的平台
  urlBuilder: ({inputText, userId, platform}) => {
    const searchParams = [{
      column: platform === PLATFORM_ENUM.FC ? 'projectName' : 'projectFeedName',
      operator: 'like',
      values: [inputText],
    }];
    const filters = encodeURIComponent(JSON.stringify(searchParams));
    return `/ad/manageCenter/projectList?globalProduct=${platform}&userId=${userId}&filters=${filters}&source=flashLink`;
  },
}
```

#### 3.1.3 [工具] 类命令

**用途**: 执行特定业务操作

**特点**:
- 可能需要也可能不需要二次输入
- 执行特定的业务逻辑
- 支持平台特定的配置
- 可以打开新窗口或弹窗

**示例**:
- `[工具] 智能搭建` → 直接跳转到智能搭建页面

**配置示例**:
```typescript
{
  id: 'tool_smart_build',
  title: '智能搭建',
  description: '基于关键词智能搭建账户结构',
  keywords: ['智能', '搭建', 'AI', '自动'],
  requiresInput: false,
  supportedPlatforms: [PLATFORM_ENUM.FC], // 仅支持 FC 平台
  action: {
    execute: ({linkToChat}) => {
      linkToChat(['user', '帮我搭建账户']);
    },
  },
}
```


## 4. 技术实现架构

### 4.1 Hook 化架构设计

**核心思路**: 使用 `useFlashLinkCommandExecutor` Hook 作为唯一入口，集中管理所有 FlashLink 功能

**架构特点**:

- **统一资源管理**: 命令注册表、搜索引擎、历史管理器在 Hook 内部一次性创建
- **自动依赖注入**: Hook 内部自动获取 `linkTo`、`linkToChat`、`pushHistory` 等依赖
- **完整功能集成**: 包含命令执行、搜索建议、快捷方式、输入处理等所有功能
- **配置支持**: 支持 `enableFallback` 等配置选项，灵活控制功能启用
- **性能优化**: 通过 `useMemo` 确保资源只创建一次，避免重复计算
- **搜索优化**: 搜索引擎内置防抖机制，优化输入响应性能

**API 设计**:

```typescript
const commandExecutor = useFlashLinkCommandExecutor();

// 完整的方法集合
commandExecutor.executeCommand(commandId, inputText?)      // 执行命令
commandExecutor.getAllCommands()                           // 获取所有命令
commandExecutor.getSearchSuggestions(inputValue)          // 搜索建议
commandExecutor.getShortcuts()                            // 快捷方式列表
commandExecutor.handleInputChange(e)                      // 输入处理
commandExecutor.handleEnterPress()                        // 回车处理
commandExecutor.getCurrentState()                         // 获取当前状态
commandExecutor.getGlobalProduct()                        // 获取平台信息
```

### 4.2 核心接口设计

```typescript
// 命令参数接口
interface ActionParams {
  command: QuickLinkCommand;        // 选中的命令
  inputText?: string;               // 二次输入的文本
  linkTo: (name: string, options?: any) => void; // 路由跳转方法
  linkToChat: (message?: PushMessageParams) => void; // 跳转到聊天的路由方法
  pushHistory: (path: string) => void; // 历史记录推送方法
  product: PLATFORM_ENUM;           // 当前平台/产品信息
  extra?: Record<string, any>;      // 预留扩展参数
}

// 历史记录接口
interface CommandHistory {
  commandId: string;                // 命令ID
  title: string;                    // 命令标题
  inputText?: string;               // 二次输入的内容（如果有）
  timestamp: number;                // 执行时间戳
  count: number;                    // 使用次数
  platform: PLATFORM_ENUM;         // 执行时的平台
}

// URL构建参数接口
interface UrlBuilderParams {
  inputText?: string;               // 搜索输入文本
  platform: PLATFORM_ENUM;         // 当前平台
  userId?: string;                  // 用户ID
}

// 搜索命令配置
interface SearchCommandConfig {
  id: string;
  title: string;
  description?: string;
  keywords?: string[];
  order?: number;                   // 排序权重，数字越小优先级越高
  supportedPlatforms?: PLATFORM_ENUM[]; // 支持的平台列表，如果不设置则支持所有平台
  urlBuilder: (params: UrlBuilderParams) => string; // URL构建函数
}

// 命令执行接口
interface CommandAction {
  execute(params: ActionParams): void | Promise<void>;
}

// 命令定义
interface QuickLinkCommand {
  id: string;
  type: 'navigation' | 'search' | 'tool';
  adModule?: string;                // 所属模块（如manageCenter、dataCenter）
  pageType?: string;                // 页面类型（用于过滤当前页面）
  title: string;
  description?: string;
  keywords?: string[];
  action: CommandAction;
  requiresInput?: boolean;          // 是否需要二次输入
  order?: number;                   // 排序权重，数字越小优先级越高
}

// 类型标签映射
const CommandTypeLabels = {
  navigation: '跳转',
  search: '查询',
  tool: '工具'
} as const;
```

### 4.2 搜索引擎设计

#### 4.2.0 重要更新：UI 显示简化

**移除类型标签显示**：
- 搜索结果和快捷方式不再显示类型标签（如[跳转]、[搜索]、[工具]）
- 直接显示命令标题，界面更简洁
- 内部仍保留类型信息用于逻辑处理

**智能模块优先级**：
- 根据当前页面路径自动识别所属模块
- 优先显示相关模块的命令
- 过滤当前页面本身，避免重复导航

**样式优化**：
- 使用 `??` 操作符替代 `||` 处理order排序
- 快捷方式列表在路径变化时自动刷新

#### 4.2.1 拼音命令接口（v2.2 类型整理版）

```typescript
// 拼音相关字段接口（集中管理所有拼音字段）
interface PinyinCommand {
    typeLabel: string;
    titlePinyin?: PinyinData;           // 标题拼音数据
    typeLabelPinyin?: PinyinData;       // 类型标签拼音数据
    descriptionPinyin?: PinyinData;     // 描述拼音数据
    // v2.2: 组合拼音字段
    combinedPinyinInitials?: string;    // 组合首字母（类型+标题）
    combinedPinyinFull?: string;        // 组合完整拼音（类型+标题）
}

// 预处理命令（继承拼音字段）
interface ProcessedCommand extends QuickLinkCommand, PinyinCommand {
    // 所有拼音相关字段已在 PinyinCommand 中定义
}
```

#### 4.2.2 搜索结果接口（v2.2 优化版）

```typescript
interface SearchResult {
  command: QuickLinkCommand;
  score: number; // 匹配得分，用于排序
  matchType: 'exact' | 'fuzzy' | 'pinyin' | 'pinyin-abbr';
  matchedField: 'title' | 'description' | 'keywords' | 'fallback'
    | 'titlePinyin.initials' | 'titlePinyin.fullPinyin'
    | 'typeLabelPinyin.initials' | 'typeLabelPinyin.fullPinyin'
    | 'descriptionPinyin.initials' | 'descriptionPinyin.fullPinyin'
    | 'combinedPinyinInitials' | 'combinedPinyinFull';  // 新增组合拼音字段
  highlightSegments: HighlightSegment[]; // 结构化高亮片段
}
```

**v2.2 接口优化说明**：
- ✅ **增强拼音匹配**: 新增多个拼音匹配字段，支持更精细的匹配分析
- ✅ **组合拼音支持**: 添加 `combinedPinyinInitials` 和 `combinedPinyinFull` 支持类型+标题的组合拼音搜索
- ✅ **详细匹配信息**: 通过 `matchedField` 可以明确知道匹配的具体字段
- ✅ **类型整理优化**: 所有拼音相关字段集中到 `PinyinCommand` 接口中，避免重复定义
- ✅ **保持向下兼容**: 保留所有原有字段，新字段为增量添加

#### 4.2.2 搜索算法

**简化的搜索和高亮策略**:

使用 Fuse.js 进行模糊搜索，配置简单且实用：

```typescript
const fuseOptions = {
  keys: [
    // 基础搜索字段
    { name: 'title', weight: 0.6 },
    { name: 'description', weight: 0.3 },
    { name: 'keywords', weight: 0.1 },

    // 拼音搜索字段 (v2.2 优化)
    { name: 'titlePinyin.fullPinyin', weight: 0.5 },        // 标题完整拼音
    { name: 'combinedPinyinFull', weight: 0.48 },           // 组合完整拼音（类型+标题）
    { name: 'titlePinyin.initials', weight: 0.45 },         // 标题首字母
    { name: 'combinedPinyinInitials', weight: 0.43 },       // 组合首字母（类型+标题）
    { name: 'typeLabelPinyin.fullPinyin', weight: 0.35 },   // 类型完整拼音
    { name: 'typeLabelPinyin.initials', weight: 0.32 },     // 类型首字母
    { name: 'descriptionPinyin.fullPinyin', weight: 0.25 }, // 描述完整拼音
    { name: 'descriptionPinyin.initials', weight: 0.22 }    // 描述首字母
  ],
  threshold: 0.2,                   // v2.2: 调整为更宽松的阈值，支持短拼音查询
  includeScore: true,               // 包含得分用于排序
  minMatchCharLength: 1,            // 最小匹配字符长度
  shouldSort: true                  // 自动按得分排序
};
```

**拼音支持 (v2.2 增强)**:
- 使用 pinyin-pro 库预处理命令数据，生成拼音和首字母
- 支持完整拼音搜索：`xiangmu` → "项目"
- 支持拼音缩写搜索：`xmbg` → "项目报告"
- 支持组合拼音搜索：`tzccb` → "[跳转] 超成本诊断" (tiaozhuan + chaochengben 的首字母组合)
- 支持跨字段拼音匹配：类型标签和标题的拼音组合

**简化的高亮规则**:
- **字面完全匹配高亮**：搜索 "管理" 会高亮 "项目**管理**"
- **拼音搜索不高亮**：搜索 "xiangmu" 能找到 "项目报告" 但不高亮
- **保持搜索功能**：所有搜索算法仍然有效，只是高亮更简洁
- **移除类型标签**：不再显示[跳转]、[搜索]等类型标签，界面更简洁

**排序规则**:
- 优先级：精确匹配 > 拼音缩写匹配 > 拼音匹配 > 模糊匹配
- 按匹配类型和得分排序

**去重机制**:
- Fuse.js 天然去重，每个命令只会出现一次

### 4.3 组件架构

#### 4.3.1 核心 Hook 架构

现代化的 Hook 模式实现，所有逻辑集中在 `useFlashLinkCommandExecutor` 中：

```typescript
// 🔥 唯一的入口点 - Hook 模式
export function useFlashLinkCommandExecutor(config?: FlashLinkConfig) {
  const history = useHistory();
  const {linkTo, linkToChat} = useAdRoute();
  const {product: globalProduct} = useGlobalProductContext();

  return useMemo(() => {
    // 一次性创建所有资源，避免重复
    const commandRegistry = createCommandRegistry(AdRoutes, globalProduct);
    const allCommands = commandRegistry.getAllCommands();
    const commandHistoryManager = getCommandHistoryManager();
    const searchEngine = createSearchEngine(commandRegistry, config);  // v2.1: 支持配置

    // 统一的执行器对象，包含所有功能
    return {
      // 命令执行
      executeCommand: (commandId: string, inputText?: string) => { /* 实现 */ },

      // 获取所有命令
      getAllCommands: () => allCommands,

      // 搜索建议（原 getFlashLinkSearchSug）
      getSearchSuggestions: (inputValue: string) => { /* 实现 */ },

      // 获取快捷方式（原 getFlashLinkShortcuts）
      getShortcuts: () => { /* 实现 */ },

      // 输入处理（原 getFlashLinkInputHandlers）
      handleInputChange: (e) => { /* 实现 */ },
      handleEnterPress: () => { /* 实现 */ },
      getCurrentState: () => { /* 实现 */ },

      // 工具方法
      getGlobalProduct: () => globalProduct,
    };
  }, [globalProduct, linkTo, linkToChat, history, config]);
}
```

#### 4.3.2 FlashLink 主组件（v2.2 展开收起版）

基于底部固定输入框的新实现，支持展开收起功能：

```typescript
const FlashLink = React.forwardRef<{handleExpand: () => void}, FlashLinkProps>(({
    inputRef,
}, ref) => {
    // 🔥 核心：一个 Hook 包含所有功能
    const commandExecutor = useFlashLinkCommandExecutor();

    const [state, setState] = useState(commandExecutor.getCurrentState());
    const {pathname} = useLocation();
    const [mini, setMini] = useState(true);
    const [isFocus, setIsFocus] = useState(false);
    const [visible, setVisible] = useState(!getIsHomePageInput(pathname));

    // v2.2: 收起状态管理和底部条检测
    const [collapsed, setCollapsed] = useLocalStorage(['flashLink', 'ui', 'collapsed'], false);
    const hasBottomBar = pathname.startsWith('/ad/manageCenter/');

    useEffect(() => {
        setVisible(!getIsHomePageInput(pathname));
    }, [pathname]);

    // v2.2: 展开收起功能
    const handleExpand = useCallback(() => {
        setCollapsed(false);
        setMini(false);
        // 延迟focus确保DOM更新完成
        setTimeout(() => {
            inputRef?.current?.focus();
        }, 100);
    }, [inputRef, setCollapsed]);

    const handleCollapse = useCallback(() => {
        setCollapsed(true);
        inputRef?.current?.blur();
    }, [inputRef, setCollapsed]);

    // v2.2: 暴露给父组件的方法
    React.useImperativeHandle(ref, () => ({
        handleExpand,
    }), [handleExpand]);

    // 动态生成 shortcuts 和 searchSug
    const shortcuts: Shortcut[] = useMemo(() => {
        return commandExecutor.getShortcuts();
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
        commandExecutor,
        pathname, // path 变化时重新获取
    ]);

    const searchSug: Shortcut[] = useMemo(() => {
        return state.inputValue ? commandExecutor.getSearchSuggestions(state.inputValue) : [];
    }, [state.inputValue, commandExecutor]);

    if (!visible) {
        return null;
    }

    return (
        <div
            className={`flash-link-container ${collapsed ? 'collapsed' : ''} ${hasBottomBar ? 'has-bottom-bar' : ''}`}
            style={mini ? {width: 'auto'} : {width: 800}}
        >
            {!collapsed && (
                <OmniInput
                    value={state.originValue as any}
                    className="flash-link-input"
                    mini={mini}
                    placeholder={mini ? '开启智能搜索' : `请输入搜索内容     ${shortcutText}`}
                    searchSug={searchSug}
                    onChange={handleInputChange}
                    onEnterPress={handleInputSubmit}
                    shortcutsLayout="column"
                    instance={inputRef}
                    shortcutKey=""
                    shortcuts={shortcuts}
                    shortcutVisible={state.inputValue === '' && !state.selectedCommand && isFocus}
                    onBlur={onBlur}
                    onFocus={onFocus}
                    focusOnChange={false}
                />
            )}
            {collapsed ? (
                <div className="flash-link-expand-btn" onClick={handleExpand}>
                    <IconChevronDoubleUp className="flash-link-arrow-up" />
                </div>
            ) : (
                <div className="flash-link-collapse-btn" onClick={handleCollapse}>
                    <IconChevronDoubleDown className="flash-link-arrow-down" />
                </div>
            )}
        </div>
    );
});

FlashLink.displayName = 'FlashLink';
```

#### 4.3.3 首页输入框 AiChat 集成

在 AiChat 中的集成大幅简化：

```typescript
// AiChat/index.tsx - 组件级使用
export default (props: AiChatBotProps) => {
  // 🔥 直接使用 Hook，自动管理所有依赖
  const flashLinkCommandExecutor = useFlashLinkCommandExecutor();

  useEffect(() => {
    const bot = initializeAiChatBot({
      instructions,
      accountInfo: {optAccountName, accountHasInit: hasInit && hasBrandInfo},
      linkToChat,
      flashLinkCommandExecutor, // 直接传递
    });
    // ...
  }, [flashLinkCommandExecutor, /* 其他依赖 */]);
};

// AiChat/bot.tsx - 函数级使用
function initializeAiChatBot({
  instructions, accountInfo, linkToChat, flashLinkCommandExecutor,
}: AiChatBot) {
  // 🔥 合并后的输入处理逻辑
  const handleInputChange = async (e: any) => {
    // FlashLink 处理
    const result = flashLinkCommandExecutor.handleInputChange(e);

    // 立即更新 shortcutVisible 状态（保持响应性）
    const shortcutVisible = !result.inputValue && !result.selectedCommand;
    !isFirstInit && aiChatInstance.setShortcutVisible(shortcutVisible);
    isFirstInit = false;

    // 获取搜索建议（异步方法，防抖已内置在搜索引擎中）
    try {
      const suggestions = result.inputValue
        ? await flashLinkCommandExecutor.getSearchSuggestions(result.inputValue)
        : [];

      aiChatInstance.setConfigByPath('input', {
        ...aiChatInstance.getConfig().input,
        searchSug: suggestions,
      });
    } catch (error) {
      // 防抖取消错误，忽略即可
      if (error.name !== 'DebounceCancelledError') {
        console.warn('FlashLink search suggestions error:', error);
      }
    }

    // 通用逻辑（合并多个监听器，提高性能）
    const richValue = e.target.value;
    if (
      Array.isArray(richValue) && richValue.length <= 1
      || typeof richValue === 'string' && richValue.length < 1
    ) {
      triggerConfig.resetTrigger();
    }
  };

  return {
    aiChat: aiChatProxy,
    disposeEvents: () => {
      efficiencyLogger.disposeEvents();
    },
  };
}
```

#### 4.3.2 历史记录管理器 (v2.2 优化)

```typescript
class CommandHistoryManager {
  private static readonly STORAGE_KEY = 'flash_link_history';  // v2.2: 更新存储键名
  private static readonly MAX_HISTORY_SIZE = 100;

  // 添加命令到历史记录 (v2.2: 优化匹配策略)
  addToHistory(newHistory: Omit<CommandHistory, 'count'>): void {
    const histories = this.getHistoryList();
    const existingIndex = histories.findIndex(
      h => h.commandId === newHistory.commandId
        && h.platform === newHistory.platform  // v2.2: 仅按commandId和platform匹配
    );

    if (existingIndex >= 0) {
      // v2.2: 更新现有记录的使用次数、时间戳、标题和输入文本
      histories[existingIndex].count += 1;
      histories[existingIndex].timestamp = newHistory.timestamp;
      histories[existingIndex].title = newHistory.title;        // v2.2: 更新标题
      histories[existingIndex].inputText = newHistory.inputText; // v2.2: 更新输入文本
    } else {
      // 添加新记录
      histories.unshift({ ...newHistory, count: 1 });
    }

    // 限制历史记录数量
    if (histories.length > CommandHistoryManager.MAX_HISTORY_SIZE) {
      histories.splice(CommandHistoryManager.MAX_HISTORY_SIZE);
    }

    this.saveHistoryList(histories);
  }

  // 获取最近使用的命令
  getRecentCommands(limit: number = 5): CommandHistory[] {
    const histories = this.getHistoryList();

    // 按使用频率和时间加权排序
    return histories
      .sort((a, b) => {
        const scoreA = this.calculateScore(a);
        const scoreB = this.calculateScore(b);
        return scoreB - scoreA;
      })
      .slice(0, limit);
  }

  // 计算命令的加权得分
  private calculateScore(history: CommandHistory): number {
    const now = Date.now();
    const daysPassed = (now - history.timestamp) / (1000 * 60 * 60 * 24);

    // 时间衰减因子：越近期使用得分越高
    const timeDecay = Math.exp(-daysPassed / 7); // 7天半衰期

    // 使用频率权重
    const frequencyWeight = Math.log(history.count + 1);

    return timeDecay * frequencyWeight;
  }

  // 从本地存储获取历史记录
  private getHistoryList(): CommandHistory[] {
    try {
      const stored = localStorage.getItem(CommandHistoryManager.STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.warn('Failed to load command history:', error);
      return [];
    }
  }

  // 保存历史记录到本地存储
  private saveHistoryList(histories: CommandHistory[]): void {
    try {
      localStorage.setItem(CommandHistoryManager.STORAGE_KEY, JSON.stringify(histories));
    } catch (error) {
      console.warn('Failed to save command history:', error);
    }
  }

  // 清空历史记录
  clearHistory(): void {
    localStorage.removeItem(CommandHistoryManager.STORAGE_KEY);
  }
}
```

#### 4.3.3 命令注册机制

```typescript
class CommandRegistry {
  // 从路由生成跳转命令
  generateNavigationCommands(routes: IRoute[], platform?: PLATFORM_ENUM): QuickLinkCommand[] {
    const commands: QuickLinkCommand[] = [];

    const processRoute = (route: IRoute) => {
      // 检查平台过滤条件
      if (route.quickLink?.enabled) {
        const shouldShow = !route.platform || route.platform === platform;
        if (shouldShow) {
          commands.push({
            id: `nav_${route.name}`,
            type: 'navigation',
            title: route.quickLink?.title || route.label,
            description: route.quickLink?.description,
            keywords: route.quickLink?.keywords,
            defaultDisplay: route.quickLink?.defaultDisplay,
            order: route.quickLink?.order,
            action: { execute: (params: ActionParams) => params.linkTo(route.name) }
          });
        }
      }

      // 递归处理子路由
      if (route.children) {
        route.children.forEach(child => processRoute(child));
      }
    };

    routes.forEach(route => processRoute(route));
    return commands;
  }

  // 注册搜索命令（支持平台过滤）
  registerSearchCommands(platform?: PLATFORM_ENUM): QuickLinkCommand[] {
    return this.searchConfigs
      .filter(config => {
        // 如果配置了 supportedPlatforms，则只有在支持的平台列表中才显示
        return !config.supportedPlatforms
               || !platform
               || config.supportedPlatforms.includes(platform);
      })
      .map(config => ({
        id: config.id,
        type: 'search',
        title: config.title,
        requiresInput: true,
        action: this.createSearchAction(config)
      }));
  }

  // 注册工具命令（支持平台过滤）
  registerToolCommands(platform?: PLATFORM_ENUM): QuickLinkCommand[] {
    return this.toolConfigs
      .filter(config => {
        // 如果配置了 supportedPlatforms，则只有在支持的平台列表中才显示
        return !config.supportedPlatforms
               || !platform
               || config.supportedPlatforms.includes(platform);
      })
      .map(config => ({
        id: config.id,
        type: 'tool',
        title: config.title,
        requiresInput: config.requiresInput || false,
        action: config.action
      }));
  }

  // 创建统一的搜索 Action
  private createSearchAction(config: SearchCommandConfig): CommandAction {
    return {
      execute: ({inputText, pushHistory, product}: ActionParams) => {
        const userId = getUserId();
        const url = config.urlBuilder({
          inputText,
          platform: product,
          userId: userId || undefined,
        });
        pushHistory(url);
      }
    };
  }
}
```

### 4.4 集成方式

#### 4.4.1 FlashLinkTrigger 触发器组件 (v2.2 优化)

```typescript
const FlashLinkTrigger: React.FC = () => {
    const inputRef: OmniInputProps['instance'] = useRef(null);
    const flashLinkRef = useRef<{handleExpand: () => void}>(null);  // v2.2: 新增ref

    const focusAiChatInput = () => {
        aiChatProxy.instance.focusInput();
        aiChatProxy.instance.setShortcutVisible(true);
    };

    const focusFlashLinkInput = () => {
        // v2.2: 展开FlashLink（内部已包含聚焦逻辑）
        flashLinkRef.current?.handleExpand();
    };

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key.toUpperCase() === 'P' && e.shiftKey && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();

                if (getIsHomePageInput(location.pathname)) {
                    // 首页：聚焦现有输入框
                    focusAiChatInput();
                } else {
                    // v2.2: 非首页：展开并聚焦FlashLink
                    focusFlashLinkInput();
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, []);

    return (
        <FlashLink
            inputRef={inputRef}
            ref={flashLinkRef}  // v2.2: 传递ref
        />
    );
};
```

#### 4.4.2 页面判断工具函数

```typescript
export const getIsHomePageInput = (pathname: string) => {
    return pathname === '/ad' || pathname === '/ad/overview';
};
```

#### 4.4.3 应用级集成

```typescript
const App: React.FC = () => (
  <div className="app">
    <Routes>{/* 路由配置 */}</Routes>
    <FlashLinkTrigger />
  </div>
);
```

## 5. 样式和布局实现

### 5.1 CSS 样式更新 (v2.2 展开收起版)

```less
.flash-link-container {
    position: fixed;
    bottom: 48px;
    left: 50%;
    transform: translateX(-50%);
    width: auto;
    border-radius: 8px;
    overflow: visible;          // v2.2: 改为 visible 支持按钮显示
    display: flex;
    flex-direction: column;
    z-index: 9999;
    pointer-events: auto;
    transition: all 0.3s ease;  // v2.2: 添加过渡动画

    // v2.2: 当页面有底部条时，向上移动40px
    &.has-bottom-bar {
        bottom: 88px;
    }

    // v2.2: 收起状态样式
    &.collapsed {
        .flash-link-input {
            display: none;
        }
    }

    // v2.2: 展开/收起按钮样式
    .flash-link-expand-btn,
    .flash-link-collapse-btn {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        cursor: pointer;
        display: inline-flex;
        padding: 8px 12px 6px;
        align-items: center;
        justify-content: center;
        border-radius: 8px 8px 0 0;
        border: 1px solid transparent;
        transition: all 0.2s ease;
        opacity: 0;
        visibility: hidden;

        .flash-link-arrow-down,
        .flash-link-arrow-up {
            font-size: 20px;
            color: #1890ff;
        }
    }

    // 展开按钮始终可见，有hover效果
    .flash-link-expand-btn {
        opacity: 1;
        visibility: visible;
        background: #fff;
        border: 1px solid rgba(102, 146, 222, 0.15);
        box-shadow:
            0 4px 20px 1px rgba(0, 71, 194, 0.04),
            0 3px 16px 1px rgba(0, 71, 194, 0.03),
            0 2px 12px 1px rgba(0, 71, 194, 0.02);
        bottom: -47px;

        &:hover {
            transform: translateX(-50%) scale(1.05);
            box-shadow:
                0 6px 24px 2px rgba(0, 71, 194, 0.06),
                0 4px 20px 1px rgba(0, 71, 194, 0.05),
                0 3px 16px 1px rgba(0, 71, 194, 0.04);
        }
    }

    // 收起按钮的hover效果（无背景和阴影变化）
    .flash-link-collapse-btn {
        bottom: -40px;

        &:hover {
            opacity: 1;
            visibility: visible;
        }
    }

    // 容器hover时显示收起按钮
    &:hover {
        .flash-link-collapse-btn {
            opacity: 1;
            visibility: visible;
        }
    }
}
```

### 5.2 动态宽度控制

- **Mini 模式**: `width: auto` - 自适应内容宽度
- **展开模式**: `width: 800px` - 固定宽度显示完整功能

### 5.3 平台自适应

```typescript
const isMac = /Mac|iPhone|iPod|iPad/.test(navigator.userAgent);
const shortcutText = isMac ? 'cmd + shift + P 可快速启动' : 'ctrl + shift + P 可快速启动';
```

## 6. 搜索引擎详细实现

### 6.1 简单的搜索实现

```typescript
// 使用 Fuse.js 的简单搜索实现
const createSearchEngine = (commands: QuickLinkCommand[]) => {
  return new Fuse(commands, fuseOptions);
};

// 如果需要拼音支持，可以预处理命令数据
const preprocessCommands = (commands: QuickLinkCommand[]): QuickLinkCommand[] => {
  return commands.map(command => ({
    ...command,
    // 添加拼音字段供搜索使用
    searchText: `${command.title} ${command.description || ''} ${command.keywords?.join(' ') || ''}`,
    pinyin: pinyin(command.title, { toneType: 'none' }).join(''),
    pinyinAbbr: pinyin(command.title, { pattern: 'first', toneType: 'none' }).join('')
  }));
};
```

### 6.2 简化的高亮功能实现

```typescript
// 简化的高亮文本实现（仅支持字面匹配）
const generateHighlightSegments = (text: string, query: string): HighlightSegment[] => {
  if (!query || !text) {
    return [{ text, highlighted: false }];
  }

  const segments: HighlightSegment[] = [];
  const queryLower = query.toLowerCase();
  const textLower = text.toLowerCase();

  let lastIndex = 0;
  let matchIndex = textLower.indexOf(queryLower);

  while (matchIndex !== -1) {
    // 添加匹配前的文本
    if (matchIndex > lastIndex) {
      segments.push({
        text: text.slice(lastIndex, matchIndex),
        highlighted: false,
      });
    }

    // 添加匹配的文本
    segments.push({
      text: text.slice(matchIndex, matchIndex + query.length),
      highlighted: true,
    });

    lastIndex = matchIndex + query.length;
    matchIndex = textLower.indexOf(queryLower, lastIndex);
  }

  // 添加剩余文本
  if (lastIndex < text.length) {
    segments.push({
      text: text.slice(lastIndex),
      highlighted: false,
    });
  }

  return segments.filter(seg => seg.text.length > 0);
};

// 使用示例
// 输入: generateHighlightSegments("项目列表", "项目")
// 输出: [
//   { text: "项目", highlighted: true },
//   { text: "列表", highlighted: false }
// ]

// 拼音搜索示例
// 输入: generateHighlightSegments("项目列表", "xiangmu")
// 输出: [{ text: "项目列表", highlighted: false }]  // 不高亮
```

## 7. 使用示例

### 7.1 Hook 使用示例

#### 7.1.1 FlashLink 组件使用

```typescript
const FlashLink: React.FC<FlashLinkProps> = ({visible = false, onClose}) => {
  const [state, setState] = useState<FlashLinkState>({
    inputValue: '',
    selectedCommand: undefined,
  });

  // 使用 Hook 创建命令执行器
  const commandExecutor = useFlashLinkCommandExecutor();

  // 动态生成 shortcuts 和 searchSug
  const shortcuts: Shortcut[] = useMemo(() => {
    return state.inputValue ? [] : commandExecutor.getShortcuts();
  }, [state.inputValue, commandExecutor]);

  const [searchSug, setSearchSug] = useState<Shortcut[]>([]);

  // 异步获取搜索建议
  useEffect(() => {
    if (state.inputValue) {
      commandExecutor.getSearchSuggestions(state.inputValue)
        .then(setSearchSug)
        .catch(error => {
          if (error.name !== 'DebounceCancelledError') {
            console.warn('FlashLink search suggestions error:', error);
          }
        });
    } else {
      setSearchSug([]);
    }
  }, [state.inputValue, commandExecutor]);

  // 输入处理
  const handleInputChange = useCallback((e) => {
    const result = commandExecutor.handleInputChange(e);
    setState(prev => ({
      ...prev,
      inputValue: result.inputValue,
      selectedCommand: result.selectedCommand,
    }));
  }, [commandExecutor]);

  // 提交处理
  const handleInputSubmit = useCallback(() => {
    const success = commandExecutor.handleEnterPress();
    if (success) {
      onClose?.();
    }
  }, [commandExecutor, onClose]);

  return (
    <Portal>
      <div className="flash-link-overlay">
        <OmniInput
          shortcuts={shortcuts}
          searchSug={searchSug}
          onChange={handleInputChange}
          onEnterPress={handleInputSubmit}
        />
      </div>
    </Portal>
  );
};
```

#### 7.1.2 AiChat 集成使用

```typescript
// AiChat/index.tsx - 组件级使用
export default (props: AiChatBotProps) => {
  const {initialPrompt, initialPayload, instructions} = props;
  const [{optAccountName}] = useAccountInfo();
  const {basicInfo: {aixAccountInitInfo: {hasInit, hasBrandInfo} = {}}} = useContext(BasicInfoContext);
  const {linkToChat} = useAdRoute();

  // 使用 Hook 创建 FlashLink 命令执行器
  const flashLinkCommandExecutor = useFlashLinkCommandExecutor();

  useEffect(() => {
    const bot = initializeAiChatBot({
      instructions,
      accountInfo: {optAccountName, accountHasInit: hasInit && hasBrandInfo},
      linkToChat,
      flashLinkCommandExecutor, // 直接传递
    });

    // 初始化完成后的处理...
    return () => {
      // 清理逻辑...
    };
  }, [flashLinkCommandExecutor, /* 其他依赖 */]);

  return <div id="ai-chat" />;
};

// AiChat/bot.tsx - 函数级使用
function initializeAiChatBot({
  instructions, accountInfo, linkToChat, flashLinkCommandExecutor,
}: AiChatBot) {
  // 使用传入的 executor，获取初始数据
  const initialShortcuts = flashLinkCommandExecutor.getShortcuts();

  // 合并的输入处理逻辑
  const handleInputChange = async (e: any) => {
    // FlashLink 处理
    const result = flashLinkCommandExecutor.handleInputChange(e);

    // 异步获取搜索建议
    try {
      const searchSug = result.inputValue
        ? await flashLinkCommandExecutor.getSearchSuggestions(result.inputValue)
        : [];

      // 更新 AiChat 配置
      aiChatInstance.setConfigByPath('input', {
        ...aiChatInstance.getConfig().input,
        searchSug,
      });
    } catch (error) {
      // 防抖取消错误，忽略即可
      if (error.name !== 'DebounceCancelledError') {
        console.warn('FlashLink search suggestions error:', error);
      }
    }

    // 其他通用逻辑
    const richValue = e.target.value;
    if (
      Array.isArray(richValue) && richValue.length <= 1
      || typeof richValue === 'string' && richValue.length < 1
    ) {
      triggerConfig.resetTrigger();
    }
  };

  // 单一事件监听器
  aiChatProxy.instance.on('input.change', handleInputChange);
}
```

### 7.2 命令定义示例

```typescript
// 导航命令
const projectListCommand: QuickLinkCommand = {
  id: 'nav_project_list',
  type: 'navigation',
  title: '项目列表',
  description: '查看和管理所有投放项目',
  keywords: ['项目', 'project', '列表'],
  requiresInput: false,
  action: {
    execute: () => history.push('/ad/ManageCenter/projectList')
  }
};

// 搜索命令
const searchProjectCommand: QuickLinkCommand = {
  id: 'search_project',
  type: 'search',
  title: '搜索项目',
  description: '根据项目名称查找项目',
  keywords: ['项目', 'project'],
  requiresInput: true,
  action: {
    execute: (params: ActionParams) => {
      const url = `/ad/ManageCenter/projectList?search=${params.inputText}&source=flashLink`;
      history.push(url);
    }
  }
};

// 工具命令
const smartBuildCommand: QuickLinkCommand = {
  id: 'tool_smart_build',
  type: 'tool',
  title: '智能搭建',
  description: '基于关键词智能搭建账户结构',
  keywords: ['智能', '搭建', 'AI'],
  requiresInput: true,
  action: {
    execute: (params: ActionParams) => {
      const url = `/tools/smart-build?keywords=${encodeURIComponent(params.inputText || '')}`;
      window.open(url);
    }
  }
};
```

### 7.3 完整交互流程

```typescript
// 用户操作流程示例
const interactionFlow = {
  // 1. 用户按下 Ctrl+Shift+P
  trigger: 'Ctrl+Shift+P',

  // 2. FlashLink 弹出，显示所有命令
  initialState: {
    mode: 'command',
    placeholder: '搜索命令...',
    commands: allCommands
  },

  // 3. 用户输入 "项目"
  userInput: '项目',
  searchResults: [
    '<mark>项目</mark>列表',
    '<mark>项目</mark>报告',
    '查询<mark>项目</mark>名称'
  ],

  // 4. 用户选择 "查询项目名称"
  commandSelect: {
    command: searchProjectCommand,
    requiresInput: true
  },

  // 5. 切换到二次输入模式
  secondaryInput: {
    mode: 'input',
    placeholder: '输入项目名称或关键词',
    breadcrumb: '查询项目名称 > 请输入'
  },

  // 6. 用户输入 "汽车广告" 并按 Enter
  finalInput: '汽车广告',
  execution: {
    url: '/ad/ManageCenter/projectList?search=汽车广告&source=flashLink', // 非实际链接仅示例
    action: 'history.push(url)'
  },

  // 7. FlashLink 关闭，跳转到搜索结果页
  result: 'FlashLink 关闭，页面跳转到项目列表搜索结果'
};
```

