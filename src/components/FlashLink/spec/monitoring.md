# FlashLink 埋点统计设计文档

## 概述

为了量化 FlashLink 功能带来的用户体验提升和业务价值，需要建立完善的埋点体系。

## 字段含义

| 字段名 | 类型 | 说明 |
|--------|------|------|
| field | string | 埋点事件类型 |
| query | string | 当前 query 词，为空时说明是默认的 shortcut |
| input_value | string | omni组件的值， 不一定是用户输入的，例如选了shortcut也会有 |
| result_count | number | 当前 query 词有多少个搜索结果 |
| command_id | string | 命令ID（如 nav_project_list） |
| command_title | string | 命令标题 |
| is_history | 0\|1 | 是否来自历史记录（1: 是, 0: 否） |
| has_input | 0\|1 | 是否有二次输入（1: 是, 0: 否） |
| is_chat | 0\|1 | 1: 发送文案到AiBot聊天, 0: 直接执行FlashLink命令 |
| duration | number | 从focus到执行结束的时间 |
| platform | PLATFORM_ENUM | 当前平台 |
| is_continue | 0\|1 | focus时是否有输入值或选中的命令（1: 是, 0: 否） |
| abandon | 0\|1 | 是否为放弃（1: 是, 0: 否） |

## 统计目标

### 主要目标
- **效率提升量化**: 通过 FlashLink 快捷操作相比传统操作节省的时间
- **使用频次分析**: 用户对 FlashLink 功能的依赖程度和使用习惯
- **功能价值评估**: 不同命令类型的使用效果和用户满意度

### 次要目标
- **产品优化**: 用户痛点识别、功能改进方向
- **平台对比**: 不同平台（FC/FEED）上的使用差异

## 核心埋点设计

### 1. 核心功能埋点

#### 1.1 命令执行 + 搜索结果 埋点

用户执行时触发

```typescript
// FlashLink 命令执行时触发埋点
sendMonitor('flash_link', {
  field: 'command_execute',
  query: string,
  input_value: string,
  result_count: number,
  command_id: string,
  command_title: string,
  is_history: 0 | 1,
  has_input: 0 | 1,
  is_chat: 0 | 1,
  duration: number,
  platform: PLATFORM_ENUM,
});
```

#### 1.2 放弃搜索结果

onBlur 时触发， 如果没有输入值且没有选中的command则abandon为0，否则为1
与blur 复用， 用 abandon 字段区分



### 2. UI交互状态埋点

#### 2.1 快捷键操作埋点
```typescript
// FlashLink cmd + shift + p按键埋点 - 仅在收起状态时触发（focusStartTime为0）
sendMonitor('flash_link', {
  field: 'show_keyboard',
  platform: PLATFORM_ENUM,
});

// FlashLink ESC按键埋点 - 仅在展开状态时触发（focusStartTime大于0）
sendMonitor('flash_link', {
  field: 'hide_keyboard',
  platform: PLATFORM_ENUM,
});
```

**注意:** 快捷键埋点包含状态检测逻辑，避免重复发送埋点。

#### 2.2 UI状态变化埋点

```typescript
// FlashLink UI状态变化埋点 - 展开
sendMonitor('flash_link', {
  field: 'expand',
  platform: PLATFORM_ENUM,
});

// FlashLink UI状态变化埋点 - 收起
sendMonitor('flash_link', {
  field: 'collapse',
  platform: PLATFORM_ENUM,
});

// FlashLink UI状态变化埋点 - 聚焦
sendMonitor('flash_link', {
  field: 'focus',
  platform: PLATFORM_ENUM,
  is_continue: 0 | 1,
});

// FlashLink UI状态变化埋点 - 失焦
sendMonitor('flash_link', {
  field: 'blur',
  query: string,
  input_value: string,
  result_count: number,
  command_id: string,
  command_title: string,
  is_history: 0 | 1,
  has_input: 0 | 1,
  duration: number,
  platform: PLATFORM_ENUM,
  abandon: 0 | 1,
});
```

**说明：**
- `expand`: 用户点击展开按钮时触发
- `collapse`: 用户点击收起按钮时触发
- `focus`/`blur`: 在 AiChat 中集成时有特殊逻辑，首次初始化时不触发 focus/blur 埋点
- 这些埋点用于统计用户对 FlashLink UI 展开/收起功能的使用频率以及交互行为

### 4. 效率提升量化埋点

#### 4.1 导航效率统计方法

**基于 FlashLink 内部数据推算导航效率提升**

**重点关注的三个导航场景**：

1. **深层输入页面导航**（如账户定向设置页面）
   - **FlashLink 导航**: 通过 `command_execute` 埋点中 `duration` 统计
   - **传统导航时间**: 顶部导航 → 管理 → 账户树 → 具体账户 → 定向设置，约 15-25 秒
   - **效率计算**: `节省时间 = 20秒(平均) - FlashLink duration`

2. **数据报告类页面导航**（hover 报告 tab 到选中具体报告）
   - **FlashLink 导航**: 通过 `command_execute` 埋点中 `duration` 统计
   - **传统导航时间**: 数据 tab hover → 选择报告类别 → 点击具体报告，约 8-15 秒
   - **效率计算**: `节省时间 = 12秒(平均) - FlashLink duration`

3. **工具类页面导航**（hover 工具 tab 到选中具体工具）
   - **FlashLink 导航**: 通过 `command_execute` 埋点中 `duration` 统计
   - **传统导航时间**: 工具 tab hover → 选择工具分类 → 点击具体工具，约 6-12 秒
   - **效率计算**: `节省时间 = 9秒(平均) - FlashLink duration`

**统计数据来源**：
- **传统导航基准时间**: 通过录屏分析真实用户的 hover + 点击操作耗时
- **FlashLink 导航时间**: 从触发 FlashLink 到命令执行完成的实际耗时
- **定期校准**: 根据用户反馈和实际使用情况调整基准时间

**注意**: 搜索功能本身已体现在导航效率中，一旦进入目标页面，页面内搜索的时间统计意义不大

## 实现说明

### 组件集成方案

1. **FlashLink 组件集成**: 通过 `useFlashLinkCommandExecutor` hook 提供统一的埋点方法
2. **AiChat 集成**: 在 bot.tsx 中集成 FlashLink 功能，支持首次初始化逻辑
3. **全局快捷键**: 通过 FlashLinkTrigger 组件处理 cmd+shift+p 快捷键
4. **历史记录追踪**: 通过扩展 QuickLinkCommand 接口的 isHistory 字段实现

### 埋点方法暴露

所有埋点方法以 `handleXXX` 格式暴露，便于扩展和集成:

- `handleFocus()` - 处理聚焦事件
- `handleBlur()` - 处理失焦事件
- `handleKeyboardShow()` - 处理快捷键显示
- `handleKeyboardHide()` - 处理快捷键隐藏
- `handleExpand()` - 处理展开事件
- `handleCollapse()` - 处理收起事件
- `handleEnterPress()` - 处理命令执行（内部已包含埋点逻辑）
