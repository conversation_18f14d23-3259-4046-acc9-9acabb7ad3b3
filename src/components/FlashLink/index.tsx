import React, {useState, useEffect, useCallback, useMemo, useRef} from 'react';
import {OmniInput, Shortcut} from '@baidu/light-ai-react';
import {useLocation} from 'react-router-dom';
import {IconChevronDoubleDown, IconChevronDoubleUp} from 'dls-icons-react';
import {useAdRoute} from '@/modules/Ad/routes';
import {useLocalStorage} from '@/hooks/storage';
import {GlobalShortcutName, useGlobalShortcut} from '@/hooks/keyboard/useGlobalShortcut';
import {FlashLinkProps} from './types';
import {useFlashLinkCommandExecutor} from './hooks';
import {DebounceCancelledError} from './SearchEngine';
import './index.global.less';

const isMac = /Mac|iPhone|iPod|iPad/.test(navigator.userAgent);
const shortcutText = isMac ? 'cmd + shift + P 可快速启动' : 'ctrl + shift + P 可快速启动';

export const getIsHomePageInput = (pathname: string) => {
    return pathname === '/ad'
        || pathname === '/ad/overview'
        || pathname === '/ad/'
        || pathname === '/ad/overview/';
};

// eslint-disable-next-line complexity
const FlashLink = React.forwardRef<{handleExpand: () => void}, FlashLinkProps>(({
    inputRef,
}, ref) => {

    // 使用 hook 创建命令执行器（自动获取依赖）
    const commandExecutor = useFlashLinkCommandExecutor();
    const {linkToChat} = useAdRoute();

    const [state, setState] = useState(commandExecutor.getCurrentState());
    // 搜索建议状态
    const [searchSugState, setSearchSugState] = useState<Shortcut[]>([]);

    const {pathname} = useLocation();
    const [mini, setMini] = useState(true);
    const [isFocus, setIsFocus] = useState(false);

    const [visible, setVisible] = useState(!getIsHomePageInput(pathname));
    const [collapsed, setCollapsed] = useLocalStorage(['flashLink', 'ui', 'collapsed'], false);

    // 检查是否需要为底部条预留空间
    const hasBottomBar = pathname.startsWith('/ad/manageCenter/');

    useEffect(() => {
        setVisible(!getIsHomePageInput(pathname));
    }, [pathname]);


    // 使用 hook 中的方法生成 shortcuts 和 searchSug
    const shortcuts: Shortcut[] = useMemo(() => {
        return commandExecutor.getShortcuts();
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
        commandExecutor,
        pathname, // path 变化时重新获取
    ]);

    // 搜索建议更新函数（现在使用内置防抖）
    const updateSearchSuggestions = useCallback(async (inputValue: string) => {
        try {
            const suggestions = await commandExecutor.getSearchSuggestions(inputValue);
            // 只有最后一个请求成功时才会执行到这里
            setSearchSugState(suggestions);
        } catch (error) {
            // 被防抖取消的请求会被忽略，不需要特殊处理
            // 只有真正的错误才会被记录
            if (!(error instanceof DebounceCancelledError)) {
                console.error('Search suggestions failed:', error);
            }
        }
    }, [commandExecutor]);

    // 搜索建议
    const searchSug: Shortcut[] = searchSugState;


    const onBlur = useCallback(() => {
        commandExecutor.handleBlur();
        setIsFocus(false);
        // 不加延迟的话 点击不到发送按钮， 因为blur完就缩小了，实际点击不到了
        setTimeout(() => {
            setMini(true);
        }, 100);
    }, [commandExecutor]);


    const triggerBlur = useCallback(() => {
        setIsFocus(false);
        inputRef?.current?.blur();
    }, [inputRef]);

    // ESC 快捷键回调
    const handleEscKey = useCallback(() => {
        commandExecutor.handleKeyboardHide();
        triggerBlur();
    }, [triggerBlur, commandExecutor]);

    useGlobalShortcut(GlobalShortcutName.FlashLinkEsc, handleEscKey);

    // 监听window blur事件，当点击iframe时主窗口失去焦点会触发
    useEffect(() => {
        window.addEventListener('blur', triggerBlur);
        return () => window.removeEventListener('blur', triggerBlur);
    }, [triggerBlur]);


    const onFocus = useCallback(() => {
        commandExecutor.handleFocus();
        setMini(false);
        setIsFocus(true);
    }, [commandExecutor]);
    // Handle input change - 使用 utils 中的方法
    const handleInputChange = useCallback((e: {target: {value: any, rawValue?: any}}) => {
        const result = commandExecutor.handleInputChange(e);
        setState(prev => ({
            ...prev,
            inputValue: result.inputValue,
            selectedCommand: result.selectedCommand,
            originValue: result.originValue,
        }));

        // 触发搜索建议更新（内置防抖）
        updateSearchSuggestions(result.inputValue);
    }, [commandExecutor, updateSearchSuggestions]);

    // Handle input submission (Enter key)
    const handleInputSubmit = useCallback(() => {

        const {isSuccess, isChat, promptForChat} = commandExecutor.handleEnterPress();
        if (isSuccess && isChat) {
            // 直接跳转到chat
            promptForChat
                && linkToChat(['user', promptForChat]);
        }
        if (isSuccess) {
            // 重置搜索建议状态
            setSearchSugState([]);
            setState(commandExecutor.getCurrentState());
            triggerBlur();

        }
    }, [commandExecutor, triggerBlur, linkToChat]);

    // Handle expand/collapse buttons
    const handleExpand = useCallback(() => {
        commandExecutor.handleExpand();
        setCollapsed(false);
        setMini(false);
        // 延迟focus确保DOM更新完成
        setTimeout(() => {
            inputRef?.current?.focus();
        }, 100);
    }, [inputRef, setCollapsed, commandExecutor]);

    const handleCollapse = useCallback(() => {
        commandExecutor.handleCollapse();
        setCollapsed(true);
        triggerBlur();
    }, [triggerBlur, setCollapsed, commandExecutor]);

    // 暴露给父组件的方法
    React.useImperativeHandle(ref, () => ({
        handleExpand,
    }), [handleExpand]);

    if (!visible) {
        return null;
    }

    return (
        <div
            className={`flash-link-container ${collapsed ? 'collapsed' : ''} ${hasBottomBar ? 'has-bottom-bar' : ''}`}
            style={mini ? {width: 'auto'} : {width: 800}}
        >
            {!collapsed && (
                <OmniInput
                    value={state.originValue as any}
                    className="flash-link-input"
                    mini={mini}
                    placeholder={mini ? '开启智能搜索' : `请输入您想搜索的功能或物料名称   ${shortcutText}`}
                    searchSug={searchSug}
                    onChange={handleInputChange}
                    onEnterPress={handleInputSubmit}
                    onShortcutChange={commandExecutor.handleShortcutChange}
                    shortcutsLayout="column"
                    instance={inputRef}
                    shortcutKey=""
                    shortcuts={shortcuts}
                    shortcutVisible={state.inputValue === '' && !state.selectedCommand && isFocus}
                    onBlur={onBlur}
                    onFocus={onFocus}
                    focusOnChange={false}
                />
            )}
            {collapsed ? (
                <div className="flash-link-expand-btn" onClick={handleExpand}>
                    <IconChevronDoubleUp className="flash-link-arrow-up" />
                </div>
            ) : (
                !!mini && (
                    <div className="flash-link-collapse-btn" onClick={handleCollapse}>
                        <IconChevronDoubleDown className="flash-link-arrow-down" />
                    </div>
                )
            )}
        </div>
    );
});

FlashLink.displayName = 'FlashLink';

export default FlashLink;
