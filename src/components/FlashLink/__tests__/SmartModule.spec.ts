/**
 * FlashLink 智能模块功能测试
 * 测试基于当前页面的智能推荐功能和UI显示简化
 */

import {describe, test, expect, beforeEach} from 'vitest';
import {PLATFORM_ENUM} from '@/dicts';
import AdRoutes from '@/modules/Ad/routes';
import {FlashLinkSearchEngine} from '../SearchEngine';
import {createCommandRegistry} from '../CommandRegistry';

describe('FlashLink 智能模块功能 - 简化版', () => {
    let searchEngine: FlashLinkSearchEngine;
    let commandRegistry: ReturnType<typeof createCommandRegistry>;

    beforeEach(() => {
        // 使用真实的 AdRoutes 和 FC 平台配置
        commandRegistry = createCommandRegistry(AdRoutes, PLATFORM_ENUM.FC);
        searchEngine = new FlashLinkSearchEngine(commandRegistry);
    });

    describe('基础功能测试', () => {
        test('应该能够获取所有命令', () => {
            const allCommands = commandRegistry.getAllCommands();
            expect(allCommands.length).toBeGreaterThan(0);

            // 验证命令结构包含新字段
            const commandsWithAdModule = allCommands.filter(cmd => cmd.adModule);
            const commandsWithPageType = allCommands.filter(cmd => cmd.pageType);

            // 应该有一些命令包含 adModule 字段
            expect(commandsWithAdModule.length).toBeGreaterThan(0);
            // 应该有一些命令包含 pageType 字段
            expect(commandsWithPageType.length).toBeGreaterThan(0);
        });

        test('应该正确识别模块名称格式', () => {
            const allCommands = commandRegistry.getAllCommands();
            const navigationCommands = allCommands.filter(cmd => cmd.type === 'navigation');

            if (navigationCommands.length > 0) {
                const commandsWithModule = navigationCommands.filter(cmd => cmd.adModule);

                expect(commandsWithModule.length).toBeGreaterThan(0);
            }
        });
    });

    describe('搜索引擎集成测试', () => {
        test('空查询应该返回默认命令', () => {
            const results = searchEngine.searchWithHighlight('');
            expect(results.length).toBeGreaterThan(0);

            // 空查询不应该有高亮
            results.forEach(result => {
                const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
                expect(highlightedSegments.length).toBe(0);
            });
        });

        test('搜索结果应该包含正确的命令信息', () => {
            const results = searchEngine.searchWithHighlight('项目');
            expect(results.length).toBeGreaterThan(0);

            // 验证搜索结果结构
            results.forEach(result => {
                expect(result.command.id).toBeTruthy();
                expect(result.command.title).toBeTruthy();
                expect(result.command.type).toMatch(/^(navigation|search|tool)$/);
                expect(result.highlightSegments).toBeInstanceOf(Array);
                expect(result.score).toBeGreaterThan(0);
            });
        });
    });

    describe('UI显示简化测试', () => {
        test('搜索结果不应包含类型标签', () => {
            const results = searchEngine.searchWithHighlight('管理');

            if (results.length > 0) {
                results.forEach(result => {
                    const segments = result.highlightSegments;
                    const fullText = segments.map(seg => seg.text).join('');

                    // 不应包含类型标签格式 [xxx]
                    expect(fullText).not.toContain('[跳转]');
                    expect(fullText).not.toContain('[搜索]');
                    expect(fullText).not.toContain('[工具]');
                    expect(fullText).not.toContain('[');
                    expect(fullText).not.toContain(']');

                    // 应该包含命令标题
                    expect(fullText).toContain(result.command.title);
                });
            }
        });

        test('高亮只对字面匹配生效', () => {
            const results = searchEngine.searchWithHighlight('报告');

            const reportResults = results.filter(r => r.command.title.includes('报告'));
            if (reportResults.length > 0) {
                const result = reportResults[0];
                const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);

                if (highlightedSegments.length > 0) {
                    // 高亮的文本应该包含搜索词
                    const highlightedText = highlightedSegments.map(seg => seg.text).join('');
                    expect(highlightedText).toContain('报告');
                }
            }
        });
    });

    describe('排序和权重测试', () => {
        test('应该正确处理order排序', () => {
            const allCommands = commandRegistry.getAllCommands();
            const commandsWithOrder = allCommands.filter(cmd => cmd.order !== undefined);

            if (commandsWithOrder.length > 1) {
                // 验证排序逻辑（已在CommandRegistry中排序）
                for (let i = 1; i < commandsWithOrder.length; i++) {
                    const prevOrder = commandsWithOrder[i - 1].order ?? 999;
                    const currentOrder = commandsWithOrder[i].order ?? 999;
                    expect(currentOrder).toBeGreaterThanOrEqual(prevOrder);
                }
            }
        });

        test('order为0的命令应该被正确处理', () => {
            const allCommands = commandRegistry.getAllCommands();
            const zeroOrderCommands = allCommands.filter(cmd => cmd.order === 0);

            if (zeroOrderCommands.length > 0) {
                // order为0的命令应该排在前面（如果使用 || 而不是 ?? 会错误地使用999）
                zeroOrderCommands.forEach(cmd => {
                    expect(cmd.order).toBe(0);
                });
            }
        });
    });

    describe('平台兼容性测试', () => {
        test('FC平台应该有相应的命令集', () => {
            const allCommands = commandRegistry.getAllCommands();
            expect(allCommands.length).toBeGreaterThan(0);

            // 验证有各种类型的命令
            const navigationCommands = allCommands.filter(cmd => cmd.type === 'navigation');
            const searchCommands = allCommands.filter(cmd => cmd.type === 'search');
            const toolCommands = allCommands.filter(cmd => cmd.type === 'tool');

            expect(navigationCommands.length).toBeGreaterThan(0);
            expect(searchCommands.length).toBeGreaterThan(0);
            // 工具命令数量取决于用户权限，可能为0
            expect(toolCommands.length).toBeGreaterThanOrEqual(0);
        });

        test('不同平台应该有不同的命令集', () => {
            // 创建FEED平台的registry进行对比
            const feedRegistry = createCommandRegistry(AdRoutes, PLATFORM_ENUM.FEED);
            const fcCommands = commandRegistry.getAllCommands();
            const feedCommands = feedRegistry.getAllCommands();

            // 不同平台的命令数量可能不同
            expect(Array.isArray(fcCommands)).toBe(true);
            expect(Array.isArray(feedCommands)).toBe(true);

            // 命令集合可能有差异（某些命令仅支持特定平台）
            const fcCommandIds = fcCommands.map(cmd => cmd.id).sort();
            const feedCommandIds = feedCommands.map(cmd => cmd.id).sort();

            // 允许有差异，但都应该有命令
            expect(fcCommandIds.length).toBeGreaterThan(0);
            expect(feedCommandIds.length).toBeGreaterThan(0);
        });
    });

    describe('性能测试', () => {
        test('命令获取应该高效', () => {
            const startTime = Date.now();

            for (let i = 0; i < 100; i++) {
                commandRegistry.getAllCommands();
            }

            const endTime = Date.now();
            expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成100次调用
        });

        test('搜索应该高效', () => {
            const startTime = Date.now();

            const queries = ['项目', '报告', '管理', '关键词', '创意'];
            queries.forEach(query => {
                searchEngine.searchWithHighlight(query);
            });

            const endTime = Date.now();
            expect(endTime - startTime).toBeLessThan(500); // 应该在500ms内完成
        });
    });
});
