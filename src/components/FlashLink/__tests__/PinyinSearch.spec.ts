/**
 * 拼音搜索功能测试
 */

import {describe, it, expect, beforeEach} from 'vitest';
import {PLATFORM_ENUM} from '@/dicts';
import AdRoutes from '@/modules/Ad/routes';
import {createSearchEngine} from '../SearchEngine';
import {MatchTypes} from '../types';
import {createCommandRegistry} from '../CommandRegistry';

describe('拼音搜索功能', () => {
    let searchEngine: ReturnType<typeof createSearchEngine>;

    beforeEach(() => {
        // 使用真实的 AdRoutes 和 FC 平台配置
        const commandRegistry = createCommandRegistry(AdRoutes, PLATFORM_ENUM.FC);

        searchEngine = createSearchEngine(commandRegistry, {
            threshold: 0.3,
            maxResults: 10,
            enablePinyin: true,
        });
    });

    describe('完整拼音匹配', () => {
        it('应该通过完整拼音找到命令', () => {
            const results = searchEngine.searchWithHighlight('xiangmu');

            expect(results.length).toBeGreaterThan(0);
            // 检查是否找到包含"项目"的命令
            const projectResults = results.filter(r => r.command.title.includes('项目'));
            expect(projectResults.length).toBeGreaterThan(0);
            expect(
                [MatchTypes.PINYIN, MatchTypes.PINYIN_ABBR, MatchTypes.FUZZY]
            ).toContain(projectResults[0].matchType);
        });

        it('应该通过拼音找到搜索命令', () => {
            const results = searchEngine.searchWithHighlight('sousuo');

            expect(results.length).toBeGreaterThan(0);
            // 检查是否找到包含"查询"的命令
            const queryResults = results.filter(r => r.command.title.includes('搜索'));
            expect(queryResults.length).toBeGreaterThan(0);
            expect([MatchTypes.PINYIN, MatchTypes.PINYIN_ABBR, MatchTypes.FUZZY]).toContain(queryResults[0].matchType);
        });

        it('应该通过拼音找到搜索相关命令', () => {
            const results = searchEngine.searchWithHighlight('sousuo');

            // 应该能找到一些结果（可能是搜索相关的命令或者fallback命令）
            expect(Array.isArray(results)).toBe(true);
        });
    });

    describe('拼音首字母匹配', () => {
        it('应该通过首字母找到命令', () => {
            const results = searchEngine.searchWithHighlight('cx');

            // 应该能找到一些结果
            expect(Array.isArray(results)).toBe(true);
        });

        it('应该支持拼音缩写搜索', () => {
            const results = searchEngine.searchWithHighlight('zhng');

            // 应该能找到一些结果（可能是"智能"相关的命令）
            expect(Array.isArray(results)).toBe(true);
        });
    });

    describe('拼音模糊匹配', () => {
        it('应该支持渐进式拼音输入', () => {
            const queries = ['zhn', 'zhnn', 'zhnng'];

            queries.forEach(query => {
                const results = searchEngine.searchWithHighlight(query);
                expect(Array.isArray(results)).toBe(true);
            });
        });

        it('应该支持不完整拼音输入', () => {
            const results = searchEngine.searchWithHighlight('cha');

            expect(Array.isArray(results)).toBe(true);
        });

        it('应该忽略大小写', () => {
            const results1 = searchEngine.searchWithHighlight('CHA');
            const results2 = searchEngine.searchWithHighlight('cha');

            expect(Array.isArray(results1)).toBe(true);
            expect(Array.isArray(results2)).toBe(true);
        });
    });

    describe('拼音与文本混合搜索', () => {
        it('文本匹配优先级应该高于拼音匹配', () => {
            const results = searchEngine.searchWithHighlight('查询');

            expect(results.length).toBeGreaterThan(0);
            // 检查匹配类型，文本匹配应该优先
            const exactMatches = results.filter(r => r.matchType === MatchTypes.EXACT);
            if (exactMatches.length > 0) {
                expect(exactMatches[0].matchType).toBe(MatchTypes.EXACT);
            }
        });

        it('应该能够搜索多个命令', () => {
            const results = searchEngine.searchWithHighlight('guanli');

            expect(Array.isArray(results)).toBe(true);
        });
    });

    describe('拼音搜索配置', () => {
        it('禁用拼音时应该不进行拼音匹配', () => {
            const registryWithoutPinyin = createCommandRegistry(AdRoutes, PLATFORM_ENUM.FC);
            const engineWithoutPinyin = createSearchEngine(registryWithoutPinyin, {
                threshold: 0.3,
                maxResults: 10,
                enablePinyin: false,
            });

            const results = engineWithoutPinyin.searchWithHighlight('zhn');

            // 应该没有结果或者匹配类型不是拼音相关
            if (results.length > 0) {
                expect(results[0].matchType).not.toBe(MatchTypes.PINYIN);
                expect(results[0].matchType).not.toBe(MatchTypes.PINYIN_ABBR);
            }
        });
    });

    describe('性能和边界情况', () => {
        it('应该处理空查询', () => {
            const results = searchEngine.searchWithHighlight('');
            expect(results.length).toBeGreaterThan(0);
        });

        it('应该处理不存在的拼音', () => {
            const results = searchEngine.searchWithHighlight('zzzzz');
            // 由于CommandRegistry包含默认搜索命令，在搜索无结果时会生成fallback，所以可能有结果
            // 这里只检查不会抛出错误
            expect(Array.isArray(results)).toBe(true);
        });

        it('应该处理特殊字符', () => {
            const results = searchEngine.searchWithHighlight('xm@#$');
            // 应该能gracefully处理，不应该抛出错误
            expect(Array.isArray(results)).toBe(true);
        });
    });
});
