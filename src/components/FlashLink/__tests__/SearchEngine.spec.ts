import {describe, test, expect, beforeEach, vi} from 'vitest';
import {PLATFORM_ENUM} from '@/dicts';
import AdRoutes from '@/modules/Ad/routes';
import {FlashLinkSearchEngine, DebounceCancelledError} from '../SearchEngine';
import {MatchTypes} from '../types';
import {CommandRegistry, createCommandRegistry} from '../CommandRegistry';

// 模拟 pinyin-pro
vi.mock('pinyin-pro', () => ({
    pinyin: vi.fn((text: string, options?: any) => {
        // 测试用模拟拼音实现
        const pinyinMap: Record<string, string> = {
            '项': 'xiang',
            '目': 'mu',
            '管': 'guan',
            '理': 'li',
            '方': 'fang',
            '案': 'an',
            '报': 'bao',
            '告': 'gao',
            '跳': 'tiao',
            '转': 'zhuan',
            '搜': 'sou',
            '索': 'suo',
            '工': 'gong',
            '具': 'ju',
            '超': 'chao',
            '成': 'cheng',
            '本': 'ben',
        };

        if (options?.pattern === 'first') {
            // 返回首字母缩写
            if (text.length > 1) {
                const result = text.split('').map(char => pinyinMap[char]?.[0] || char);
                return options?.type === 'array' ? result : result.join('');
            }

            if (options?.type === 'array') {
                return [pinyinMap[text]?.[0] || text];
            }
            return pinyinMap[text]?.[0] || text;
        }

        // 处理多个字符
        if (text.length > 1) {
            const result = text.split('').map(char => pinyinMap[char] || char);
            return result.join('');
        }

        // 单个字符
        return pinyinMap[text] || text;
    }),
}));

describe('FlashLinkSearchEngine - 简化版', () => {
    let searchEngine: FlashLinkSearchEngine;
    let commandRegistry: CommandRegistry;

    beforeEach(() => {
        // 使用真实的 AdRoutes 和 FC 平台配置
        commandRegistry = createCommandRegistry(AdRoutes, PLATFORM_ENUM.FC);
        searchEngine = new FlashLinkSearchEngine(commandRegistry);
    });

    describe('基础搜索功能', () => {
        test('应该返回所有命令当查询为空', () => {
            const results = searchEngine.searchWithHighlight('');
            expect(results.length).toBeGreaterThan(0); // 应该返回所有可用命令

            // 空查询不应该有高亮
            results.forEach(result => {
                const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
                expect(highlightedSegments.length).toBe(0);
            });
        });

        test('应该基于标题搜索', () => {
            const results = searchEngine.searchWithHighlight('项目');
            expect(results.length).toBeGreaterThan(0);

            const projectResults = results.filter(r => r.command.title.includes('项目'));
            expect(projectResults.length).toBeGreaterThan(0);
        });

        test('应该基于关键词搜索', () => {
            const results = searchEngine.searchWithHighlight('搜索');
            expect(results.length).toBeGreaterThan(0);

            // 检查是否找到查询相关的命令
            const queryCommands = results.filter(r => r.command.title.includes('搜索'));
            expect(queryCommands.length).toBeGreaterThan(0);
        });
    });

    describe('字面匹配高亮', () => {
        test('应该高亮精确匹配的文本 - 搜索"管理"', () => {
            const results = searchEngine.searchWithHighlight('管理');
            expect(results.length).toBeGreaterThan(0);

            const managementResults = results.filter(r => r.command.title.includes('管理'));
            expect(managementResults.length).toBeGreaterThan(0);

            const result = managementResults[0];
            const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
            expect(highlightedSegments.length).toBeGreaterThan(0);

            // 验证高亮的文本包含"管理"
            const highlightedText = highlightedSegments.map(seg => seg.text).join('');
            expect(highlightedText).toContain('管理');
        });

        test('应该高亮精确匹配的文本 - 搜索"报告"', () => {
            const results = searchEngine.searchWithHighlight('报告');
            expect(results.length).toBeGreaterThan(0);

            const reportResults = results.filter(r => r.command.title.includes('报告'));
            expect(reportResults.length).toBeGreaterThan(0);

            const result = reportResults[0];
            const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
            expect(highlightedSegments.length).toBeGreaterThan(0);

            // 验证高亮的文本包含"报告"
            const highlightedText = highlightedSegments.map(seg => seg.text).join('');
            expect(highlightedText).toContain('报告');
        });

        test('应该高亮标题匹配 - 搜索"跳转"', () => {
            const results = searchEngine.searchWithHighlight('跳转');

            if (results.length > 0) {
                const result = results[0];
                const segments = result.highlightSegments;

                // 检查是否有命令标题
                const fullText = segments.map(seg => seg.text).join('');
                expect(fullText).toContain(result.command.title);

                // 如果找到匹配，应该有高亮片段
                const highlightedSegments = segments.filter(seg => seg.highlighted);
                if (highlightedSegments.length > 0) {
                    const highlightedText = highlightedSegments.map(seg => seg.text).join('');
                    expect(highlightedText).toContain('跳转');
                }
            }
        });
    });

    describe('组合拼音搜索', () => {

        test('应该通过组合首字母找到结果 - tzccb 匹配 [跳转]超成本诊断', () => {
            const results = searchEngine.searchWithHighlight('tzccb');
            expect(results.length).toBeGreaterThan(0);

            // 寻找包含"超成本诊断"且类型为跳转的命令
            const targetResults = results.filter(r =>
                r.command.title.includes('超成本诊断')
                && r.command.type === 'navigation'
            );

            expect(targetResults.length).toBeGreaterThan(0);

            const result = targetResults[0];
            // 应该有合理的分数
            expect(result.score).toBeGreaterThan(0);

            // 应该通过拼音匹配（可能是 titlePinyin.initials 或 combinedPinyinInitials）
            expect(['titlePinyin.initials', 'combinedPinyinInitials']).toContain(result.matchedField);

        });

        test('应该通过完整组合拼音找到结果 - tiaozhuan 匹配跳转类型', () => {
            const results = searchEngine.searchWithHighlight('tiaozhuan');

            if (results.length > 0) {
                const navigationResults = results.filter(r => r.command.type === 'navigation');
                expect(navigationResults.length).toBeGreaterThan(0);
            }
        });
    });

    describe('拼音搜索（找到结果但不高亮）', () => {
        test('应该通过拼音找到结果但不高亮 - xiangmu', () => {
            const results = searchEngine.searchWithHighlight('xiangmu');

            if (results.length > 0) {
                const projectResults = results.filter(r => r.command.title.includes('项目'));
                expect(projectResults.length).toBeGreaterThan(0);

                const result = projectResults[0];
                // 拼音搜索不应该有高亮（简化逻辑）
                const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
                expect(highlightedSegments.length).toBe(0);

                // 但应该包含完整的显示文本
                const fullText = result.highlightSegments.map(seg => seg.text).join('');
                expect(fullText).toContain(result.command.title);
            }
        });

        test('应该通过拼音缩写找到结果但不高亮 - xmgl', () => {
            const results = searchEngine.searchWithHighlight('xmgl');

            if (results.length > 0) {
                const result = results[0];
                // 拼音缩写搜索不应该有高亮
                const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
                expect(highlightedSegments.length).toBe(0);

                // 但应该包含完整的显示文本
                const fullText = result.highlightSegments.map(seg => seg.text).join('');
                expect(fullText).toContain(result.command.title);
            }
        });

        test('应该通过拼音找到方案管理但不高亮 - fanganguanli', () => {
            const results = searchEngine.searchWithHighlight('chakanquanbufangan');

            if (results.length > 0) {
                const solutionResults = results.filter(r => r.command.title.includes('方案'));
                expect(solutionResults.length).toBeGreaterThan(0);

                const result = solutionResults[0];
                // 拼音搜索不应该有高亮
                const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
                expect(highlightedSegments.length).toBe(0);
            }
        });
    });

    describe('边界情况', () => {
        test('应该处理不存在的搜索词', () => {
            const results = searchEngine.searchWithHighlight('不存在的内容xyz123');

            if (results.length > 0) {
                // 如果有结果，应该没有高亮
                results.forEach(result => {
                    const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
                    expect(highlightedSegments.length).toBe(0);
                });
            }
        });

        test('应该保持文本完整性', () => {
            const testQueries = ['管理', '项目', '报告', 'xiangmu', 'xmgl'];

            testQueries.forEach(query => {
                const results = searchEngine.searchWithHighlight(query);
                results.forEach(result => {
                    // 重建文本应该包含原始标题
                    const reconstructedText = result.highlightSegments.map(seg => seg.text).join('');
                    expect(reconstructedText).toContain(result.command.title);
                });
            });
        });

        test('应该正确显示命令标题', () => {
            const results = searchEngine.searchWithHighlight('项目');
            expect(results.length).toBeGreaterThan(0);

            results.forEach(result => {
                const fullText = result.highlightSegments.map(seg => seg.text).join('');
                // 应该包含命令标题（不再包含类型标签）
                expect(fullText).toContain(result.command.title);
            });
        });
    });

    describe('匹配类型判断', () => {
        test('字面匹配应该返回EXACT类型', () => {
            const results = searchEngine.searchWithHighlight('管理');
            expect(results.length).toBeGreaterThan(0);

            const exactMatches = results.filter(r => r.command.title.includes('管理'));
            expect(exactMatches.length).toBeGreaterThan(0);

            const result = exactMatches[0];
            expect(result.matchType).toBe(MatchTypes.EXACT);
        });

        test('拼音匹配应该返回相应的匹配类型', () => {
            const results = searchEngine.searchWithHighlight('xiangmu');

            if (results.length > 0) {
                const projectResults = results.filter(r => r.command.title.includes('项目'));
                if (projectResults.length > 0) {
                    const result = projectResults[0];
                    // 应该是拼音匹配类型
                    expect([MatchTypes.PINYIN, MatchTypes.PINYIN_ABBR, MatchTypes.FUZZY]).toContain(result.matchType);
                }
            }
        });
    });

    describe('性能和配置', () => {
        test('应该尊重maxResults配置', () => {
            const smallRegistry = new CommandRegistry();
            smallRegistry.registerCustomCommands([
                {
                    id: 'test1',
                    type: 'navigation',
                    title: '测试1',
                    description: '测试命令1',
                    keywords: [],
                    action: {execute: vi.fn()},
                },
                {
                    id: 'test2',
                    type: 'navigation',
                    title: '测试2',
                    description: '测试命令2',
                    keywords: [],
                    action: {execute: vi.fn()},
                },
                {
                    id: 'test3',
                    type: 'navigation',
                    title: '测试3',
                    description: '测试命令3',
                    keywords: [],
                    action: {execute: vi.fn()},
                },
            ]);
            const smallEngine = new FlashLinkSearchEngine(smallRegistry, {maxResults: 2});
            const results = smallEngine.searchWithHighlight('');
            expect(results.length).toBeLessThanOrEqual(2);
        });

        test('应该快速处理搜索', () => {
            const startTime = Date.now();
            const results = searchEngine.searchWithHighlight('管理');
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
            expect(results.length).toBeGreaterThan(0);
        });

        test('相同查询应该产生一致的结果', () => {
            const query = '管理';

            const results1 = searchEngine.searchWithHighlight(query);
            const results2 = searchEngine.searchWithHighlight(query);

            expect(results1.length).toBe(results2.length);

            // 比较相同命令的高亮片段
            results1.forEach((result1, index) => {
                const result2 = results2[index];
                expect(result1.command.id).toBe(result2.command.id);
                expect(result1.highlightSegments).toEqual(result2.highlightSegments);
            });
        });
    });

    describe('防抖搜索功能', () => {
        // 简单的模拟渲染函数，专注于防抖测试
        const simpleRender = vi.fn(() => []);

        beforeEach(() => {
            simpleRender.mockClear();
        });

        test('防抖机制应该取消之前的搜索请求', async () => {
            const promises = [];

            // 快速发起多个搜索请求
            promises.push(searchEngine.searchAndConvertToShortcutsDebounced('项', simpleRender, 100));
            promises.push(searchEngine.searchAndConvertToShortcutsDebounced('项目', simpleRender, 100));
            promises.push(searchEngine.searchAndConvertToShortcutsDebounced('项目管理', simpleRender, 100));

            // 等待所有 Promise 完成
            const results = await Promise.allSettled(promises);

            // 前面的请求应该被取消，最后一个应该成功
            expect(results[0].status).toBe('rejected');
            expect(results[1].status).toBe('rejected');
            expect(results[2].status).toBe('fulfilled');

            // 验证被取消的请求抛出正确的错误类型
            if (results[0].status === 'rejected') {
                expect(results[0].reason).toBeInstanceOf(DebounceCancelledError);
                expect(results[0].reason.name).toBe('DebounceCancelledError');
            }

            if (results[1].status === 'rejected') {
                expect(results[1].reason).toBeInstanceOf(DebounceCancelledError);
                expect(results[1].reason.name).toBe('DebounceCancelledError');
            }
        });

        test('防抖应该等待指定的延迟时间', async () => {
            const startTime = Date.now();
            const debounceMs = 200;

            await searchEngine.searchAndConvertToShortcutsDebounced(
                '管理',
                simpleRender,
                debounceMs
            );

            const endTime = Date.now();
            const actualDelay = endTime - startTime;

            // 应该至少等待防抖时间（允许一些误差）
            expect(actualDelay).toBeGreaterThanOrEqual(debounceMs - 50);
        });

        test('连续的相同查询应该取消前面的请求', async () => {
            const sameQuery = '项目';

            // 发起两个相同的查询
            const promise1 = searchEngine.searchAndConvertToShortcutsDebounced(sameQuery, simpleRender, 100);
            const promise2 = searchEngine.searchAndConvertToShortcutsDebounced(sameQuery, simpleRender, 100);

            const results = await Promise.allSettled([promise1, promise2]);

            // 第一个应该被取消，第二个应该成功
            expect(results[0].status).toBe('rejected');
            expect(results[1].status).toBe('fulfilled');

            if (results[0].status === 'rejected') {
                expect(results[0].reason).toBeInstanceOf(DebounceCancelledError);
            }
        });

        test('防抖取消后新的搜索应该正常工作', async () => {
            // 发起一个会被取消的请求
            const cancelledPromise = searchEngine.searchAndConvertToShortcutsDebounced('项', simpleRender, 100);

            // 立即发起新的请求
            const successPromise = searchEngine.searchAndConvertToShortcutsDebounced('项目', simpleRender, 100);

            const results = await Promise.allSettled([cancelledPromise, successPromise]);

            expect(results[0].status).toBe('rejected');
            expect(results[1].status).toBe('fulfilled');

            // 成功的请求应该返回有效结果
            if (results[1].status === 'fulfilled') {
                expect(Array.isArray(results[1].value)).toBe(true);
            }
        });

        test('防抖定时器应该正确清理', async () => {
            // 这个测试确保多次调用不会有内存泄漏
            const queries = ['a', 'ab', 'abc', 'abcd'];
            const promises = queries.map(query =>
                searchEngine.searchAndConvertToShortcutsDebounced(query, simpleRender, 50)
            );

            const results = await Promise.allSettled(promises);

            // 只有最后一个应该成功
            expect(results[results.length - 1].status).toBe('fulfilled');

            // 前面的都应该被取消
            for (let i = 0; i < results.length - 1; i++) {
                expect(results[i].status).toBe('rejected');
                if (results[i].status === 'rejected') {
                    expect((results[i] as any).reason).toBeInstanceOf(DebounceCancelledError);
                }
            }
        });

        test('防抖取消错误应该有正确的属性', () => {
            const error = new DebounceCancelledError();

            expect(error).toBeInstanceOf(Error);
            expect(error.name).toBe('DebounceCancelledError');
            expect(error.message).toBe('Search request was cancelled by debounce');
        });

        test('不同的防抖延迟时间应该正确工作', async () => {
            const shortDelay = 50;
            const longDelay = 200;

            const startTime1 = Date.now();
            await searchEngine.searchAndConvertToShortcutsDebounced('test1', simpleRender, shortDelay);
            const endTime1 = Date.now();

            const startTime2 = Date.now();
            await searchEngine.searchAndConvertToShortcutsDebounced('test2', simpleRender, longDelay);
            const endTime2 = Date.now();

            expect(endTime1 - startTime1).toBeGreaterThanOrEqual(shortDelay - 20);
            expect(endTime2 - startTime2).toBeGreaterThanOrEqual(longDelay - 20);
            expect(endTime2 - startTime2).toBeGreaterThan(endTime1 - startTime1);
        });
    });
});
