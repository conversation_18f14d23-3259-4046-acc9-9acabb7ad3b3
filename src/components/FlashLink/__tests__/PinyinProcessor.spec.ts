/**
 * 拼音处理器测试
 */

import {describe, it, expect} from 'vitest';
import {PinyinProcessor} from '../PinyinProcessor';

describe('PinyinProcessor', () => {
    describe('getPinyinData', () => {
        it('应该正确获取中文文本的拼音数据', () => {
            const result = PinyinProcessor.getPinyinData('项目');

            expect(result).toBeTruthy();
            expect(result?.fullPinyin).toBe('xiangmu');
            expect(result?.initials).toBe('xm');
            expect(result?.pinyinArray).toEqual(['xiang', 'mu']);
            expect(result?.initialsArray).toEqual(['x', 'm']);
        });

        it('应该处理空字符串', () => {
            const result = PinyinProcessor.getPinyinData('');
            expect(result).toBeNull();
        });

        it('应该处理单个字符', () => {
            const result = PinyinProcessor.getPinyinData('跳');

            expect(result).toBeTruthy();
            expect(result?.fullPinyin).toBe('tiao');
            expect(result?.initials).toBe('t');
            expect(result?.pinyinArray).toEqual(['tiao']);
            expect(result?.initialsArray).toEqual(['t']);
        });
    });

    describe('generatePinyinVariants', () => {
        it('应该生成拼音变体', () => {
            const variants = PinyinProcessor.generatePinyinVariants('项目');

            expect(variants).toContain('xiangmu'); // 完整拼音
            expect(variants).toContain('xm'); // 首字母
            expect(variants).toContain('x'); // 单个首字母
            expect(variants).toContain('xi'); // 部分拼音
            expect(variants).toContain('xian'); // 部分拼音
            expect(variants).toContain('xiang'); // 部分拼音
        });

        it('应该处理单字', () => {
            const variants = PinyinProcessor.generatePinyinVariants('跳');

            expect(variants).toContain('tiao');
            expect(variants).toContain('t');
        });

        it('应该去重变体', () => {
            const variants = PinyinProcessor.generatePinyinVariants('项目');
            const uniqueVariants = [...new Set(variants)];

            expect(variants.length).toBe(uniqueVariants.length);
        });
    });


    describe('matchesPinyin', () => {
        it('应该匹配完整拼音', () => {
            const pinyinData = PinyinProcessor.getPinyinData('项目');

            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'xiangmu')).toBe(true);
            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'xiang')).toBe(true);
            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'mu')).toBe(true);
        });

        it('应该匹配首字母', () => {
            const pinyinData = PinyinProcessor.getPinyinData('项目');

            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'xm')).toBe(true);
            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'x')).toBe(true);
        });

        it('应该忽略大小写', () => {
            const pinyinData = PinyinProcessor.getPinyinData('项目');

            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'XIANGMU')).toBe(true);
            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'XM')).toBe(true);
        });

        it('应该返回false对于不匹配的查询', () => {
            const pinyinData = PinyinProcessor.getPinyinData('项目');

            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'abc')).toBe(false);
            expect(PinyinProcessor.matchesPinyin(pinyinData!, 'zzz')).toBe(false);
        });
    });
});
