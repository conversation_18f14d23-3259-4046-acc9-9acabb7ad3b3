import {describe, test, expect, beforeEach, vi} from 'vitest';
import {PLATFORM_ENUM} from '@/dicts';
import AdRoutes from '@/modules/Ad/routes';
import {FlashLinkSearchEngine} from '../SearchEngine';
import {CommandRegistry, createCommandRegistry} from '../CommandRegistry';
import {QuickLinkCommand} from '../types';

// 模拟 pinyin-pro
vi.mock('pinyin-pro', () => ({
    pinyin: vi.fn((text: string, options?: any) => {
        // 测试用模拟拼音实现
        const pinyinMap: Record<string, string> = {
            '项': 'xiang',
            '目': 'mu',
            '管': 'guan',
            '理': 'li',
            '方': 'fang',
            '案': 'an',
            '报': 'bao',
            '告': 'gao',
        };

        if (options?.pattern === 'first') {
            // 返回首字母缩写
            if (text.length > 1) {
                const result = text.split('').map(char => pinyinMap[char]?.[0] || char);
                return options?.type === 'array' ? result : result.join('');
            }

            if (options?.type === 'array') {
                return [pinyinMap[text]?.[0] || text];
            }
            return pinyinMap[text]?.[0] || text;
        }

        // 处理多个字符
        if (text.length > 1) {
            const result = text.split('').map(char => pinyinMap[char] || char);
            return result.join('');
        }

        // 单个字符
        return pinyinMap[text] || text;
    }),
}));

describe('FlashLink 简单高亮功能', () => {
    let searchEngine: FlashLinkSearchEngine;
    let commandRegistry: CommandRegistry;

    beforeEach(() => {
        // 使用真实的 AdRoutes 和 FC 平台配置
        commandRegistry = createCommandRegistry(AdRoutes, PLATFORM_ENUM.FC);
        searchEngine = new FlashLinkSearchEngine(commandRegistry);
    });

    describe('字面完全匹配高亮', () => {
        test('应该正确高亮精确文本匹配 - 搜索"管理"', () => {
            const results = searchEngine.searchWithHighlight('管理');
            expect(results.length).toBeGreaterThan(0);

            const firstResult = results[0];
            const segments = firstResult.highlightSegments;

            // 应该包含高亮的"管理"片段
            const highlightedSegments = segments.filter(seg => seg.highlighted);
            expect(highlightedSegments.length).toBeGreaterThan(0);

            // 验证高亮的文本是"管理"
            const highlightedText = highlightedSegments.map(seg => seg.text).join('');
            expect(highlightedText).toContain('管理');

            // 验证完整文本包含原始标题
            const fullText = segments.map(seg => seg.text).join('');
            expect(fullText).toContain(firstResult.command.title);
        });

        test('应该正确高亮部分文本匹配 - 搜索"项目"', () => {
            const results = searchEngine.searchWithHighlight('项目');
            expect(results.length).toBeGreaterThan(0);

            const projectResult = results.find(r => r.command.title.includes('项目'));
            expect(projectResult).toBeDefined();

            const segments = projectResult!.highlightSegments;
            const highlightedSegments = segments.filter(seg => seg.highlighted);

            // 应该高亮"项目"
            const highlightedText = highlightedSegments.map(seg => seg.text).join('');
            expect(highlightedText).toContain('项目');
        });

        test('应该正确高亮标题匹配 - 搜索"跳转"', () => {
            const results = searchEngine.searchWithHighlight('跳转');

            if (results.length > 0) {
                const result = results[0];
                const segments = result.highlightSegments;

                // 检查显示文本（不再包含类型标签）
                const fullText = segments.map(seg => seg.text).join('');
                expect(fullText).toBe(result.command.title);

                // 如果找到匹配，应该有高亮片段
                const highlightedSegments = segments.filter(seg => seg.highlighted);
                if (highlightedSegments.length > 0) {
                    const highlightedText = highlightedSegments.map(seg => seg.text).join('');
                    expect(highlightedText).toContain('跳转');
                }
            }
        });
    });

    describe('无匹配情况', () => {
        test('拼音搜索应该找到结果但不高亮 - 搜索"xmgl"', () => {
            const results = searchEngine.searchWithHighlight('xmgl');

            if (results.length > 0) {
                const result = results[0];
                const segments = result.highlightSegments;

                // 应该返回完整的显示文本但没有高亮
                const fullText = segments.map(seg => seg.text).join('');
                expect(fullText).toContain(result.command.title);

                // 拼音搜索不应该有高亮（简化版）
                const highlightedSegments = segments.filter(seg => seg.highlighted);
                expect(highlightedSegments.length).toBe(0);
            }
        });

        test('不存在的搜索词应该返回空结果或无高亮', () => {
            const results = searchEngine.searchWithHighlight('不存在的内容xyz123');

            if (results.length > 0) {
                // 如果有结果，应该没有高亮
                results.forEach(result => {
                    const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
                    expect(highlightedSegments.length).toBe(0);
                });
            }
        });
    });

    describe('边界情况', () => {
        test('空查询应该返回所有结果且无高亮', () => {
            const results = searchEngine.searchWithHighlight('');
            expect(results.length).toBeGreaterThan(0);

            // 所有结果都不应该有高亮
            results.forEach(result => {
                const highlightedSegments = result.highlightSegments.filter(seg => seg.highlighted);
                expect(highlightedSegments.length).toBe(0);

                // 但应该有完整的显示文本
                const fullText = result.highlightSegments.map(seg => seg.text).join('');
                expect(fullText).toContain(result.command.title);
            });
        });

        test('应该保持文本完整性', () => {
            const testQueries = ['管理', '项目', '报告'];

            testQueries.forEach(query => {
                const results = searchEngine.searchWithHighlight(query);
                results.forEach(result => {
                    // 重建文本应该包含原始标题
                    const reconstructedText = result.highlightSegments.map(seg => seg.text).join('');
                    expect(reconstructedText).toContain(result.command.title);
                });
            });
        });
    });

    describe('性能和一致性', () => {
        test('相同查询应该产生一致的结果', () => {
            const query = '管理';

            const results1 = searchEngine.searchWithHighlight(query);
            const results2 = searchEngine.searchWithHighlight(query);

            expect(results1.length).toBe(results2.length);

            // 比较相同命令的高亮片段
            results1.forEach((result1, index) => {
                const result2 = results2[index];
                expect(result1.command.id).toBe(result2.command.id);
                expect(result1.highlightSegments).toEqual(result2.highlightSegments);
            });
        });

        test('应该高效处理大量命令', () => {
            // 创建大量命令
            const largeCommandList: QuickLinkCommand[] = [];
            for (let i = 0; i < 1000; i++) {
                largeCommandList.push({
                    id: `cmd-${i}`,
                    type: 'navigation',
                    title: `测试命令${i}`,
                    description: `Description ${i}`,
                    action: {execute: vi.fn()},
                });
            }

            const largeRegistry = new CommandRegistry();
            largeRegistry.registerCustomCommands(largeCommandList);
            const largeEngine = new FlashLinkSearchEngine(largeRegistry);

            // 搜索应该快速完成
            const startTime = Date.now();
            const results = largeEngine.searchWithHighlight('测试');
            const endTime = Date.now();

            expect(endTime - startTime).toBeLessThan(1000); // 应该在1秒内完成
            expect(results.length).toBeGreaterThan(0);
        });
    });
});
