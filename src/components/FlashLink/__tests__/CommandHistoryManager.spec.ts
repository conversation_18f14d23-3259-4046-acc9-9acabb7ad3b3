/**
 * CommandHistoryManager 单元测试
 */

import {describe, it, expect, beforeEach, afterEach, vi} from 'vitest';
import {PLATFORM_ENUM} from '@/dicts';
import {getUserId} from '@/utils';
import {CommandHistoryManager, getCommandHistoryManager, type CommandHistory} from '../CommandHistoryManager';

// 模拟 localStorage
const mockLocalStorage = (() => {
    let store: Record<string, string> = {};

    return {
        getItem: vi.fn((key: string) => store[key] || null),
        setItem: vi.fn((key: string, value: string) => {
            store[key] = value;
        }),
        removeItem: vi.fn((key: string) => {
            delete store[key];
        }),
        clear: vi.fn(() => {
            store = {};
        }),
        get length() {
            return Object.keys(store).length;
        },
        key: vi.fn((index: number) => {
            const keys = Object.keys(store);
            return keys[index] || null;
        }),
    };
})();

Object.defineProperty(window, 'localStorage', {
    value: mockLocalStorage,
});

describe('命令历史管理器', () => {
    let manager: CommandHistoryManager;
    const mockTimestamp = 1640995200000; // 2022-01-01 00:00:00

    beforeEach(() => {
        manager = new CommandHistoryManager();
        mockLocalStorage.clear();
        vi.clearAllMocks();
        vi.spyOn(Date, 'now').mockReturnValue(mockTimestamp);
    });

    afterEach(() => {
        vi.restoreAllMocks();
    });

    describe('添加到历史记录', () => {
        it('应该将新命令添加到历史记录', () => {
            const newHistory = {
                commandId: 'cmd1',
                title: 'Test Command',
                inputText: 'test input',
                timestamp: mockTimestamp,
                platform: PLATFORM_ENUM.FC,
            };

            manager.addToHistory(newHistory);

            expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
                `flash_link_history:${getUserId()}`,
                JSON.stringify([{...newHistory, count: 1}])
            );
        });

        it('应该更新现有命令的计数和时间戳', () => {
            const existingHistory: CommandHistory = {
                commandId: 'cmd1',
                title: 'Test Command',
                inputText: 'test input',
                timestamp: mockTimestamp - 1000,
                platform: PLATFORM_ENUM.FC,
                count: 1,
            };

            mockLocalStorage.setItem(`flash_link_history:${getUserId()}`, JSON.stringify([existingHistory]));

            const newHistory = {
                commandId: 'cmd1',
                title: 'Updated Command',
                inputText: 'updated input',
                timestamp: mockTimestamp,
                platform: PLATFORM_ENUM.FC,
            };

            manager.addToHistory(newHistory);

            const expectedHistory = {
                ...existingHistory,
                count: 2,
                timestamp: mockTimestamp,
                title: 'Updated Command',
                inputText: 'updated input',
            };

            expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
                `flash_link_history:${getUserId()}`,
                JSON.stringify([expectedHistory])
            );
        });

        it('应该将历史记录大小限制为 MAX_HISTORY_SIZE', () => {
            const histories: CommandHistory[] = [];
            for (let i = 0; i < 100; i++) {
                histories.push({
                    commandId: `cmd${i}`,
                    title: `Command ${i}`,
                    timestamp: mockTimestamp - i * 1000,
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                });
            }

            mockLocalStorage.setItem(`flash_link_history:${getUserId()}`, JSON.stringify(histories));

            const newHistory = {
                commandId: 'new-cmd',
                title: 'New Command',
                timestamp: mockTimestamp,
                platform: PLATFORM_ENUM.FC,
            };

            manager.addToHistory(newHistory);

            // 获取最新的 setItem 调用（addToHistory 之后）
            const lastCall = mockLocalStorage.setItem.mock.calls[mockLocalStorage.setItem.mock.calls.length - 1];
            const savedHistories = JSON.parse(lastCall[1]);
            expect(savedHistories).toHaveLength(100);
            expect(savedHistories[0].commandId).toBe('new-cmd');
        });

        it('应该分别处理不同的平台', () => {
            const fcHistory = {
                commandId: 'cmd1',
                title: 'Test Command',
                inputText: 'test input',
                timestamp: mockTimestamp,
                platform: PLATFORM_ENUM.FC,
            };

            const feedHistory = {
                commandId: 'cmd1',
                title: 'Test Command',
                inputText: 'test input',
                timestamp: mockTimestamp,
                platform: PLATFORM_ENUM.FEED,
            };

            manager.addToHistory(fcHistory);
            manager.addToHistory(feedHistory);

            const savedHistories = JSON.parse(mockLocalStorage.setItem.mock.calls[1][1]);
            expect(savedHistories).toHaveLength(2);
            expect(savedHistories[0].platform).toBe(PLATFORM_ENUM.FEED);
            expect(savedHistories[1].platform).toBe(PLATFORM_ENUM.FC);
        });

        it('相同命令和平台但不同输入应该更新标题和输入文本', () => {
            const firstHistory = {
                commandId: 'search_project',
                title: '查询项目名称',
                inputText: '第一次搜索',
                timestamp: mockTimestamp - 1000,
                platform: PLATFORM_ENUM.FC,
            };

            manager.addToHistory(firstHistory);

            const secondHistory = {
                commandId: 'search_project',
                title: '查询项目名称',
                inputText: '第二次搜索',
                timestamp: mockTimestamp,
                platform: PLATFORM_ENUM.FC,
            };

            manager.addToHistory(secondHistory);

            const savedHistories = JSON.parse(mockLocalStorage.setItem.mock.calls[1][1]);
            expect(savedHistories).toHaveLength(1);
            expect(savedHistories[0].count).toBe(2);
            expect(savedHistories[0].inputText).toBe('第二次搜索');
            expect(savedHistories[0].timestamp).toBe(mockTimestamp);
        });
    });

    describe('获取最近命令', () => {
        beforeEach(() => {
            const histories: CommandHistory[] = [
                {
                    commandId: 'cmd1',
                    title: 'Command 1',
                    timestamp: mockTimestamp - 86400000, // 1天前
                    platform: PLATFORM_ENUM.FC,
                    count: 5,
                },
                {
                    commandId: 'cmd2',
                    title: 'Command 2',
                    timestamp: mockTimestamp - 172800000, // 2天前
                    platform: PLATFORM_ENUM.FEED,
                    count: 3,
                },
                {
                    commandId: 'cmd3',
                    title: 'Command 3',
                    timestamp: mockTimestamp - 259200000, // 3天前
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                },
            ];

            mockLocalStorage.setItem(`flash_link_history:${getUserId()}`, JSON.stringify(histories));
        });

        it('应该返回按加权分数排序的最近命令', () => {
            const recentCommands = manager.getRecentCommands(3);

            expect(recentCommands).toHaveLength(3);
            expect(recentCommands[0].commandId).toBe('cmd1'); // 最高分数
        });

        it('应该按指定限制限制结果数量', () => {
            const recentCommands = manager.getRecentCommands(1);

            expect(recentCommands).toHaveLength(1);
        });

        it('应该在指定时按平台过滤', () => {
            const fcCommands = manager.getRecentCommands(10, PLATFORM_ENUM.FC);

            expect(fcCommands).toHaveLength(2);
            expect(fcCommands.every(cmd => cmd.platform === PLATFORM_ENUM.FC)).toBe(true);
        });

        it('当不存在历史记录时应该返回空数组', () => {
            mockLocalStorage.clear();

            const recentCommands = manager.getRecentCommands(5);

            expect(recentCommands).toHaveLength(0);
        });
    });

    describe('获取最近搜索', () => {
        beforeEach(() => {
            const histories: CommandHistory[] = [
                {
                    commandId: 'search',
                    title: 'Search Command',
                    inputText: 'query1',
                    timestamp: mockTimestamp - 1000,
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                },
                {
                    commandId: 'search',
                    title: 'Search Command',
                    inputText: 'query2',
                    timestamp: mockTimestamp - 2000,
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                },
                {
                    commandId: 'search',
                    title: 'Search Command',
                    inputText: 'query1', // 重复
                    timestamp: mockTimestamp - 3000,
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                },
                {
                    commandId: 'other',
                    title: 'Other Command',
                    inputText: 'other query',
                    timestamp: mockTimestamp,
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                },
            ];

            mockLocalStorage.setItem(`flash_link_history:${getUserId()}`, JSON.stringify(histories));
        });

        it('应该返回特定命令的最近搜索', () => {
            const searches = manager.getRecentSearches('search', 5);

            expect(searches).toEqual(['query1', 'query2']);
        });

        it('应该去除重复项并按时间戳排序', () => {
            const searches = manager.getRecentSearches('search', 5);

            expect(searches).toHaveLength(2);
            expect(searches[0]).toBe('query1'); // 最近的唯一项
        });

        it('应该按指定限制限制搜索结果数量', () => {
            const searches = manager.getRecentSearches('search', 1);

            expect(searches).toHaveLength(1);
        });

        it('对于不存在的命令应该返回空数组', () => {
            const searches = manager.getRecentSearches('nonexistent', 5);

            expect(searches).toHaveLength(0);
        });
    });

    describe('清空历史记录', () => {
        it('应该从 localStorage 移除历史记录', () => {
            manager.clearHistory();

            expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(`flash_link_history:${getUserId()}`);
        });
    });

    describe('移除命令历史记录', () => {
        beforeEach(() => {
            const histories: CommandHistory[] = [
                {
                    commandId: 'cmd1',
                    title: 'Command 1',
                    timestamp: mockTimestamp,
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                },
                {
                    commandId: 'cmd2',
                    title: 'Command 2',
                    timestamp: mockTimestamp,
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                },
            ];

            mockLocalStorage.setItem(`flash_link_history:${getUserId()}`, JSON.stringify(histories));
        });

        it('应该移除特定命令的所有历史记录条目', () => {
            manager.removeCommandHistory('cmd1');

            // 获取最新的 setItem 调用（removeCommandHistory 之后）
            const lastCall = mockLocalStorage.setItem.mock.calls[mockLocalStorage.setItem.mock.calls.length - 1];
            const savedHistories = JSON.parse(lastCall[1]);
            expect(savedHistories).toHaveLength(1);
            expect(savedHistories[0].commandId).toBe('cmd2');
        });
    });

    describe('获取历史统计信息', () => {
        beforeEach(() => {
            const histories: CommandHistory[] = [
                {
                    commandId: 'cmd1',
                    title: 'Command 1',
                    timestamp: mockTimestamp,
                    platform: PLATFORM_ENUM.FC,
                    count: 5,
                },
                {
                    commandId: 'cmd1',
                    title: 'Command 1',
                    inputText: 'different input',
                    timestamp: mockTimestamp,
                    platform: PLATFORM_ENUM.FC,
                    count: 3,
                },
                {
                    commandId: 'cmd2',
                    title: 'Command 2',
                    timestamp: mockTimestamp,
                    platform: PLATFORM_ENUM.FEED,
                    count: 2,
                },
            ];

            mockLocalStorage.setItem(`flash_link_history:${getUserId()}`, JSON.stringify(histories));
        });

        it('应该返回正确的统计信息', () => {
            const stats = manager.getHistoryStats();

            expect(stats.totalCommands).toBe(2);
            expect(stats.totalUsages).toBe(10);
            expect(stats.topCommands).toHaveLength(2);
            expect(stats.topCommands[0]).toEqual({
                commandId: 'cmd1',
                title: 'Command 1',
                count: 8,
            });
        });

        it('应该处理空历史记录', () => {
            mockLocalStorage.clear();

            const stats = manager.getHistoryStats();

            expect(stats.totalCommands).toBe(0);
            expect(stats.totalUsages).toBe(0);
            expect(stats.topCommands).toHaveLength(0);
        });
    });

    describe('localStorage 错误处理', () => {
        it('应该优雅地处理 localStorage.getItem 错误', () => {
            mockLocalStorage.getItem.mockImplementation(() => {
                throw new Error('Storage error');
            });

            const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            const recentCommands = manager.getRecentCommands(5);

            expect(recentCommands).toHaveLength(0);
            expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to load command history:', expect.any(Error));

            consoleWarnSpy.mockRestore();
        });

        it('应该优雅地处理 localStorage.setItem 错误', () => {
            mockLocalStorage.setItem.mockImplementation(() => {
                throw new Error('Storage full');
            });

            const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            const newHistory = {
                commandId: 'cmd1',
                title: 'Test Command',
                timestamp: mockTimestamp,
                platform: PLATFORM_ENUM.FC,
            };

            manager.addToHistory(newHistory);

            expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to save command history:', expect.any(Error));

            consoleWarnSpy.mockRestore();
        });

        it('当存储失败时应该用减少的历史记录重试', () => {
            const largeHistories: CommandHistory[] = [];
            for (let i = 0; i < 60; i++) {
                largeHistories.push({
                    commandId: `cmd${i}`,
                    title: `Command ${i}`,
                    timestamp: mockTimestamp,
                    platform: PLATFORM_ENUM.FC,
                    count: 1,
                });
            }

            mockLocalStorage.setItem
                .mockImplementationOnce(() => {
                    throw new Error('Storage full');
                })
                .mockImplementationOnce((_key, value) => {
                    const histories = JSON.parse(value);
                    expect(histories).toHaveLength(50);
                });

            mockLocalStorage.getItem.mockReturnValue(JSON.stringify(largeHistories));

            const consoleWarnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

            const newHistory = {
                commandId: 'new-cmd',
                title: 'New Command',
                timestamp: mockTimestamp,
                platform: PLATFORM_ENUM.FC,
            };

            manager.addToHistory(newHistory);

            expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(2);
            expect(consoleWarnSpy).toHaveBeenCalledWith('Failed to save command history:', expect.any(Error));

            consoleWarnSpy.mockRestore();
        });
    });

    describe('计算分数（私有方法行为）', () => {
        it('应该给更近期和更频繁的命令更高分数', () => {
            const recentFrequent: CommandHistory = {
                commandId: 'cmd1',
                title: 'Command 1',
                timestamp: mockTimestamp - 86400000, // 1天前
                platform: PLATFORM_ENUM.FC,
                count: 10,
            };

            const oldRare: CommandHistory = {
                commandId: 'cmd2',
                title: 'Command 2',
                timestamp: mockTimestamp - 604800000, // 7天前
                platform: PLATFORM_ENUM.FC,
                count: 1,
            };

            mockLocalStorage.setItem(`flash_link_history:${getUserId()}`, JSON.stringify([recentFrequent, oldRare]));

            const recentCommands = manager.getRecentCommands(2);

            expect(recentCommands[0].commandId).toBe('cmd1');
            expect(recentCommands[1].commandId).toBe('cmd2');
        });
    });
});

describe('获取命令历史管理器', () => {
    it('应该返回相同的实例（单例模式）', () => {
        const instance1 = getCommandHistoryManager();
        const instance2 = getCommandHistoryManager();

        expect(instance1).toBe(instance2);
    });

    it('应该返回一个 CommandHistoryManager 实例', () => {
        const instance = getCommandHistoryManager();

        expect(instance).toBeInstanceOf(CommandHistoryManager);
    });
});
