/**
 * 命令注册中心
 *
 * 负责管理所有 FlashLink 命令的注册、生成和配置
 */
/* eslint-disable max-len */
import {getUserId} from '@/utils';
import {IRoute, RoutesType} from '@/modules/Ad/routes';
import {PLATFORM_ENUM} from '@/dicts';
import {isAiBuildUser} from '@/utils/getFlag';
import {
    QuickLinkCommand,
    CommandRegistryConfig,
    SearchCommandConfig,
    ToolCommandConfig,
    NavigationCommandConfig,
    ActionParams,
    CommandAction,
    CommandTypes,
    UrlBuilderParams,
    GLOBAL_SEARCH_CONSTANTS,
} from './types';

/**
 * 命令注册中心类
 */
export class CommandRegistry {
    private commands: QuickLinkCommand[] = [];
    private searchConfigs: SearchCommandConfig[] = [];
    private toolConfigs: ToolCommandConfig[] = [];
    private navigationConfigs: NavigationCommandConfig[] = [];
    private readonly cachedCommands: Map<string, QuickLinkCommand[]> = new Map();
    private readonly routes?: Required<RoutesType>;
    private readonly platform?: PLATFORM_ENUM;

    constructor(
        config?: CommandRegistryConfig
    ) {
        // linkTo 和 pushHistory 现在通过 ActionParams 传递，这里保持兼容性
        if (config) {
            this.searchConfigs = config.searchConfigs || [];
            this.toolConfigs = config.toolConfigs || [];
            this.navigationConfigs = config.navigationConfigs || [];
            this.routes = config.routes;
            this.platform = config.platform;
        }

        this.initializeDefaultCommands();
    }


    /**
     * 从路由配置生成导航命令
     */
    generateNavigationCommands(routes: Required<RoutesType>, platform?: PLATFORM_ENUM): QuickLinkCommand[] {
        const commands: QuickLinkCommand[] = [];

        const processRoute = (route: IRoute) => {
            // 如果当前路由启用了 quickLink，生成命令
            const isEnabled = typeof route.quickLink?.enabled === 'function'
                ? route.quickLink.enabled()
                : route.quickLink?.enabled;
            if (isEnabled) {
                // 检查平台过滤条件
                // 如果路由配置了 platform，则只有在当前 platform 匹配时才显示
                // 如果没有配置 platform，则所有 platform 都显示
                const shouldShow = !route.platform || route.platform === platform;
                if (shouldShow) {
                    const pathName = routes.getPathByName(route.name);
                    const adModule = pathName?.split('/')[2];

                    commands.push({
                        id: `nav_${route.name}`,
                        adModule,
                        pageType: route.name,
                        type: CommandTypes.NAVIGATION,
                        title: route.quickLink?.title || (typeof route.label === 'string' ? route.label : route.name),
                        // description: route.quickLink?.description,
                        keywords: route.quickLink?.keywords || [],
                        requiresInput: false,
                        action: {
                            execute: (params: ActionParams) => {
                                // 使用路由名称进行跳转
                                params.linkTo(route.name);
                            },
                        },
                        defaultDisplay: route.quickLink?.defaultDisplay,
                        order: route.quickLink?.order,
                        inheritInputValue: false,
                    });
                }
            }

            // 递归处理子路由
            if (route.children && route.children.length > 0) {
                route.children.forEach(child => {
                    processRoute(child);
                });
            }
        };

        routes.forEach(route => {
            processRoute(route);
        });

        return commands;
    }

    /**
     * 注册查询命令
     */
    registerSearchCommands(): QuickLinkCommand[] {
        return this.searchConfigs
            .filter(config => {
                // 如果配置了 supportedPlatforms，则只有在支持的平台列表中才显示
                // 如果没有配置 supportedPlatforms，则所有平台都显示
                return !config.supportedPlatforms
                    || !this.platform
                    || config.supportedPlatforms.includes(this.platform);
            })
            .map(config => ({
                id: config.id,
                type: CommandTypes.SEARCH,
                title: config.title,
                // description: config.description,
                keywords: config.keywords || [],
                requiresInput: true,
                action: this.createSearchAction(config),
                defaultDisplay: config.defaultDisplay,
                order: config.order,
                inheritInputValue: config.inheritInputValue || false,
            }));
    }

    /**
     * 注册工具命令
     */
    registerToolCommands(): QuickLinkCommand[] {
        return this.toolConfigs
            .filter(config => {
                // 如果配置了 supportedPlatforms，则只有在支持的平台列表中才显示
                // 如果没有配置 supportedPlatforms，则所有平台都显示
                return !config.supportedPlatforms
                    || !this.platform
                    || config.supportedPlatforms.includes(this.platform);
            })
            .map(config => ({
                id: config.id,
                type: CommandTypes.TOOL,
                title: config.title,
                description: config.description,
                keywords: config.keywords || [],
                requiresInput: config.requiresInput || false,
                action: config.action,
                defaultDisplay: config.defaultDisplay,
                order: config.order,
                inheritInputValue: config.inheritInputValue || false,
            }));
    }

    /**
     * 注册手动配置的导航命令
     */
    registerNavigationCommands(): QuickLinkCommand[] {
        return this.navigationConfigs
            .filter(config => {
                // 如果配置了 supportedPlatforms，则只有在支持的平台列表中才显示
                // 如果没有配置 supportedPlatforms，则所有平台都显示
                return !config.supportedPlatforms
                    || !this.platform
                    || config.supportedPlatforms.includes(this.platform);
            })
            .map(config => ({
                id: config.id,
                type: CommandTypes.NAVIGATION,
                title: config.title,
                // description: config.description,
                keywords: config.keywords || [],
                requiresInput: false,
                action: config.action,
                defaultDisplay: config.defaultDisplay,
                order: config.order,
                inheritInputValue: config.inheritInputValue || false,
                adModule: config.adModule,
            }));
    }


    /**
     * 注册自定义命令
     */
    registerCustomCommand(command: QuickLinkCommand): void {
        const existingIndex = this.commands.findIndex(
            cmd => cmd.id === command.id
        );
        if (existingIndex >= 0) {
            // 替换现有命令
            this.commands[existingIndex] = command;
        } else {
            // 添加新命令
            this.commands.push(command);
        }
        this.clearCache();
    }

    /**
     * 批量注册自定义命令
     */
    registerCustomCommands(commands: QuickLinkCommand[]): void {
        commands.forEach(command => this.registerCustomCommand(command));
    }

    /**
     * 移除命令
     */
    removeCommand(commandId: string): void {
        this.commands = this.commands.filter(cmd => cmd.id !== commandId);
        this.clearCache();
    }

    /**
     * 获取所有命令
     */
    getAllCommands(): QuickLinkCommand[] {
        // 生成缓存键
        const routesKey = this.routes
            ? JSON.stringify(this.routes.map(r => r.name))
            : 'no-routes';
        const cacheKey = `${this.platform || 'all'}_${routesKey}`;

        // 检查缓存
        if (this.cachedCommands.has(cacheKey)) {
            return this.cachedCommands.get(cacheKey)!;
        }

        const allCommands: QuickLinkCommand[] = [];

        // 添加路由生成的导航命令
        if (this.routes) {
            allCommands.push(...this.generateNavigationCommands(this.routes, this.platform));
        }

        // 添加手动配置的导航命令
        allCommands.push(...this.registerNavigationCommands());

        // 添加查询命令
        allCommands.push(...this.registerSearchCommands());

        // 添加工具命令
        allCommands.push(...this.registerToolCommands());

        // 添加自定义命令
        allCommands.push(...this.commands);

        // 按 order 排序（数字越小优先级越高）
        const sortedCommands = allCommands.sort((a, b) => (a.order ?? 999) - (b.order ?? 999));

        // 缓存结果
        this.cachedCommands.set(cacheKey, sortedCommands);
        return sortedCommands;
    }

    /**
     * 根据类型获取命令
     */
    getCommandsByType(type: CommandTypes): QuickLinkCommand[] {
        return this.getAllCommands().filter(cmd => cmd.type === type);
    }

    /**
     * 根据ID获取命令
     */
    getCommandById(commandId: string): QuickLinkCommand | undefined {
        // 普通命令直接查找
        return this.getAllCommands().find(cmd => cmd.id === commandId);
    }

    /**
     * 更新查询配置
     */
    updateSearchConfigs(configs: SearchCommandConfig[]): void {
        this.searchConfigs = configs;
        this.clearCache();
    }

    /**
     * 更新工具配置
     */
    updateToolConfigs(configs: ToolCommandConfig[]): void {
        this.toolConfigs = configs;
        this.clearCache();
    }

    /**
     * 添加查询配置
     */
    addSearchConfig(config: SearchCommandConfig): void {
        const existingIndex = this.searchConfigs.findIndex(
            c => c.id === config.id
        );
        if (existingIndex >= 0) {
            this.searchConfigs[existingIndex] = config;
        } else {
            this.searchConfigs.push(config);
        }
        this.clearCache();
    }

    /**
     * 添加工具配置
     */
    addToolConfig(config: ToolCommandConfig): void {
        const existingIndex = this.toolConfigs.findIndex(
            c => c.id === config.id
        );
        if (existingIndex >= 0) {
            this.toolConfigs[existingIndex] = config;
        } else {
            this.toolConfigs.push(config);
        }
        this.clearCache();
    }

    /**
     * 生成备选查询命令
     */
    generateFallbackSearchCommands(query: string): QuickLinkCommand[] {
        // 仅使用支持 fallback 的查询配置，并根据平台过滤
        const fallbackConfigs = this.searchConfigs
            .filter(config => config.supportsFallback)
            .filter(config => {
                return !config.supportedPlatforms
                    || !this.platform
                    || config.supportedPlatforms.includes(this.platform);
            });
            // 已在初始化时排序，不需要再次排序

        return fallbackConfigs.map(config => ({
            id: `${GLOBAL_SEARCH_CONSTANTS.COMMAND_PREFIX}${config.id}`,
            type: CommandTypes.SEARCH,
            title: `${config.title}包含“${query}”的物料`,
            keywords: [],
            requiresInput: false,
            action: {
                execute: (params: ActionParams) => {
                    // 复用 createSearchAction 的逻辑，传入预设的查询内容
                    const searchAction = this.createSearchSugAction(config, query);
                    searchAction.execute({
                        ...params,
                        inputText: query, // 使用预设的查询内容
                    });
                },
            },
            inheritInputValue: false,
        }));
    }

    /**
     * 清空所有命令
     */
    clearAllCommands(): void {
        this.commands = [];
        this.clearCache();
    }

    /**
     * 获取命令统计信息
     */
    getCommandStats(): {
        total: number;
        byType: Record<CommandTypes, number>;
        } {
        const allCommands = this.getAllCommands();
        const byType = {
            [CommandTypes.NAVIGATION]: 0,
            [CommandTypes.SEARCH]: 0,
            [CommandTypes.TOOL]: 0,
        };

        allCommands.forEach(cmd => {
            byType[cmd.type as CommandTypes]++;
        });

        return {
            total: allCommands.length,
            byType,
        };
    }

    /**
     * 清空缓存
     */
    private clearCache(): void {
        this.cachedCommands.clear();
    }

    /**
     * 初始化默认命令配置
     */
    private initializeDefaultCommands(): void {
        // 默认查询命令配置
        this.searchConfigs = [
            {
                id: 'search_project',
                title: '搜索项目名称',
                description: '根据项目名称查找项目',
                keywords: ['项目', 'project', '查询', '搜索'],
                supportsFallback: true,
                supportedPlatforms: [PLATFORM_ENUM.FC, PLATFORM_ENUM.FEED],
                urlBuilder: (params: UrlBuilderParams) => {
                    const {inputText, userId, platform} = params;
                    const searchParams = [{
                        column: platform === PLATFORM_ENUM.FC ? 'projectName' : 'projectFeedName',
                        operator: 'like',
                        values: [inputText],
                    }];
                    const filters = encodeURIComponent(JSON.stringify(searchParams));
                    return `/ad/manageCenter/projectList?globalProduct=${platform}&userId=${userId}&filters=${filters}&source=flashLink`;
                },
            },
            {
                id: 'search_campaign',
                title: '搜索方案名称',
                description: '根据方案名称查找方案',
                keywords: ['计划名称', 'campaign', '查询', '搜索'],
                supportsFallback: true,
                supportedPlatforms: [PLATFORM_ENUM.FC, PLATFORM_ENUM.FEED],
                urlBuilder: (params: UrlBuilderParams) => {
                    const {inputText, userId, platform} = params;
                    const searchParams = [{
                        column: platform === PLATFORM_ENUM.FC ? 'campaignName' : 'campaignFeedName',
                        operator: 'like',
                        values: [inputText],
                    }];
                    const filters = encodeURIComponent(JSON.stringify(searchParams));
                    return `/ad/manageCenter/campaignList?globalProduct=${platform}&userId=${userId}&filters=${filters}&source=flashLink`;
                },
            },
            {
                id: 'search_adgroup',
                title: '搜索单元名称',
                description: '根据单元名称查找单元',
                keywords: ['单元', 'adgroup', '查询', '搜索'],
                supportsFallback: true,
                supportedPlatforms: [PLATFORM_ENUM.FC, PLATFORM_ENUM.FEED],
                urlBuilder: (params: UrlBuilderParams) => {
                    const {inputText, userId, platform} = params;
                    const searchParams = [{
                        column: platform === PLATFORM_ENUM.FC ? 'adgroupName' : 'unitname',
                        operator: 'like',
                        values: [inputText],
                    }];
                    const filters = encodeURIComponent(JSON.stringify(searchParams));
                    return `/ad/manageCenter/adgroupList?globalProduct=${platform}&userId=${userId}&filters=${filters}&source=flashLink`;
                },
            },
            {
                id: 'search_keyword',
                title: '搜索关键词',
                description: '根据关键词查找相关内容',
                keywords: ['关键词', 'keyword', '查询', '搜索'],
                supportsFallback: true,
                supportedPlatforms: [PLATFORM_ENUM.FC],
                urlBuilder: (params: UrlBuilderParams) => {
                    const {inputText, userId, platform} = params;
                    const searchParams = [{
                        field: 'keyword',
                        operatorValue: 'like',
                        value: [inputText],
                    }];
                    const filters = encodeURIComponent(JSON.stringify(searchParams));
                    return `/ad/manageCenter/keywordList?globalProduct=${platform}&userId=${userId}&filters=${filters}&source=flashLink`;
                },
            },
            {
                id: 'search_creative_text',
                title: '搜索创意文案',
                description: '根据创意文案查找相关内容',
                keywords: ['搜索创意文案', 'creative'],
                supportsFallback: true,
                supportedPlatforms: [PLATFORM_ENUM.FC],
                urlBuilder: (params: UrlBuilderParams) => {
                    const {inputText, userId, platform} = params;
                    const searchParams = [{
                        column: 'creativeText',
                        operator: 'like',
                        values: [inputText],
                    }];
                    const filters = encodeURIComponent(JSON.stringify(searchParams));
                    return `/ad/manageCenter/creativeList/creativeTextList?globalProduct=${platform}&userId=${userId}&filters=${filters}&source=flashLink`;
                },
            },
        ];

        // 默认工具命令配置
        this.toolConfigs = [
            // 物料搜索命令（所有平台都支持）
            {
                id: GLOBAL_SEARCH_CONSTANTS.COMMAND_ID,
                title: GLOBAL_SEARCH_CONSTANTS.TITLE,
                description: GLOBAL_SEARCH_CONSTANTS.DESCRIPTION,
                keywords: ['全局查询', '全局查找', '物料查找', '搜索', '搜索物料'],
                requiresInput: true,
                inheritInputValue: true,
                defaultDisplay: false,
                order: 9999, // 确保在列表末尾
                action: {
                    execute: () => {
                        // 不执行任何操作，状态切换由 hooks 处理
                    },
                },
            },
            // AI 搭建工具（仅限部分用户）
            ...(
                isAiBuildUser()
                    ? [
                        {
                            id: 'tool_smart_build',
                            title: '智能搭建',
                            // description: '快速搭建账户物料',
                            keywords: ['生成', '创建', '自动', 'AI'],
                            requiresInput: false,
                            supportedPlatforms: [PLATFORM_ENUM.FC],
                            order: -10,
                            action: {
                                execute: ({linkToChat}: ActionParams) => {
                                    linkToChat(['user', '帮我搭建账户']);
                                },
                            },
                        },
                        {
                            id: 'tool_smart_refresh',
                            title: '智能翻新',
                            // description: '基于现有项目、方案翻新物料',
                            keywords: ['生成', '翻新', 'AI', '重提'],
                            requiresInput: false,
                            supportedPlatforms: [PLATFORM_ENUM.FC],
                            order: -9,
                            action: {
                                execute: ({linkToChat}: ActionParams) => {
                                    linkToChat(['user', '帮我翻新账户']);
                                },
                            },
                        },
                    ]
                    : []
            ),
            {
                id: 'tool_create_aix_campaign',
                title: '创建提示词广告',
                requiresInput: false,
                order: -8,
                action: {
                    execute: ({linkToChat}: ActionParams) => {
                        linkToChat(['user', '新建提示词方案']);
                    },
                },
            },
        ];

        this.navigationConfigs = [
            {
                id: 'custom_nav_consume_fluctuation',
                title: '消费波动诊断',
                description: '诊断消费下降原因，优化举措，竞争趋势分析，投放历程分析等',
                keywords: ['消费突降', '消费下降'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/optCenter/advertiseDiagnosis?globalProduct=1&tab=CONSUME_FLUCTUATION&userId=${userId}`);
                    },
                },
                order: -7,
                adModule: 'overview',
            },
            {
                id: 'custom_nav_exceed_cost',
                title: '超成本诊断',
                description: '诊断超成本原因，优化举措，竞争趋势分析，操作分析等',
                keywords: ['超成本', '成本溢出', '空耗'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/optCenter/advertiseDiagnosis?globalProduct=1&tab=EXCEED_COST&userId=${userId}`);
                    },
                },
                order: 6,
            },
            {
                id: 'custom_nav_xst_2001',
                title: '电话创意组件管理',
                keywords: ['电话组件', '线索通'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/manageCenter/creativeComponent?globalProduct=1&userId=${userId}&creativeLevel=xst&creativeSubLevel=phone`);
                    },
                },
            },
            {
                id: 'custom_nav_xst_2108',
                title: '咨询创意组件管理',
                keywords: ['咨询组件', '线索通'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/manageCenter/creativeComponent?globalProduct=1&userId=${userId}&creativeLevel=xst&creativeSubLevel=consult`);
                    },
                },
            },
            {
                id: 'custom_nav_xst_8572',
                title: '营销创意组件管理',
                keywords: ['营销组件', '线索通'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/manageCenter/creativeComponent?globalProduct=1&userId=${userId}&creativeLevel=xst&creativeSubLevel=marketing`);
                    },
                },
            },
            {
                id: 'custom_nav_xst_4060',
                title: '自定义表单创意组件管理',
                keywords: ['表单组件', '线索通'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/manageCenter/creativeComponent?globalProduct=1&userId=${userId}&creativeLevel=xst&creativeSubLevel=customform`);
                    },
                },
            },
            {
                id: 'custom_nav_creative_component_text',
                title: '文字类创意组件管理',
                keywords: ['文字类组件'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/manageCenter/creativeComponent?globalProduct=1&userId=${userId}&creativeLevel=text`);
                    },
                },
            },
            {
                id: 'custom_nav_creative_component_app',
                title: '应用类创意组件管理',
                keywords: ['应用类组件', 'app组件', '下载组件', '应用绑定管理'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/manageCenter/creativeComponent?globalProduct=1&userId=${userId}&creativeLevel=app`);
                    },
                },
            },
            {
                id: 'custom_nav_creative_component_product',
                title: '商品类创意组件管理',
                keywords: ['商品类组件'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/manageCenter/creativeComponent?globalProduct=1&userId=${userId}&creativeLevel=product`);
                    },
                },
            },
            {
                id: 'custom_nav_creative_component_store',
                title: '本地类创意组件管理',
                keywords: ['本地类组件', '门店组件'],
                action: {
                    execute: ({pushHistory}: ActionParams) => {
                        const userId = getUserId();
                        pushHistory(`/ad/manageCenter/creativeComponent?globalProduct=1&userId=${userId}&creativeLevel=store`);
                    },
                },
            },
        ];
    }

    /**
     * 创建查询动作
     */
    private createSearchAction(config: SearchCommandConfig): CommandAction {
        return {
            execute: ({inputText, pushHistory, product}: ActionParams) => {
                const userId = getUserId();
                const url = config.urlBuilder({
                    inputText,
                    platform: product,
                    userId: userId || undefined,
                });
                pushHistory(url);
            },
        };
    }

    private createSearchSugAction(config: SearchCommandConfig, query: string): CommandAction {
        return {
            execute: ({pushHistory, product}: ActionParams) => {
                const userId = getUserId();
                const url = config.urlBuilder({
                    inputText: query,
                    platform: product,
                    userId: userId || undefined,
                });
                pushHistory(url);
            },
        };
    }
}

// 导出默认实例创建函数
export function createCommandRegistry(
    routes: Required<RoutesType>,
    platform: PLATFORM_ENUM,
    config?: Omit<CommandRegistryConfig, 'routes' | 'platform'>
): CommandRegistry {
    return new CommandRegistry({
        ...config,
        routes,
        platform,
    });
}
