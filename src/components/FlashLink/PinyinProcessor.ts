/**
 * 拼音处理器
 *
 * 提供完整拼音匹配、拼音首字母匹配、拼音模糊匹配功能
 */

import {pinyin} from 'pinyin-pro';

/**
 * 拼音处理结果
 */
export interface PinyinData {
    /** 完整拼音（无声调）- 如: "xiangmubaogao" */
    fullPinyin: string;
    /** 拼音首字母 - 如: "xmbg" */
    initials: string;
    /** 拼音数组（用于部分匹配）- 如: ["xiang", "mu", "bao", "gao"] */
    pinyinArray: string[];
    /** 首字母数组（用于部分匹配）- 如: ["x", "m", "b", "g"] */
    initialsArray: string[];
}

/**
 * 拼音处理器类
 */
// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class PinyinProcessor {
    /**
     * 获取文本的拼音数据
     */
    static getPinyinData(text: string): PinyinData | null {
        if (!text || !text.trim()) {
            return null;
        }

        try {
            // 获取完整拼音数组
            const pinyinResult = pinyin(text, {
                toneType: 'none',
                type: 'array',
            });

            const pinyinArray = Array.isArray(pinyinResult) ? pinyinResult : [String(pinyinResult)];

            // 获取首字母数组
            const initialsResult = pinyin(text, {
                pattern: 'first',
                toneType: 'none',
                type: 'array',
            });

            const initialsArray = Array.isArray(initialsResult) ? initialsResult : [String(initialsResult)];

            return {
                fullPinyin: pinyinArray.join(''),
                initials: initialsArray.join(''),
                pinyinArray,
                initialsArray,
            };
        } catch (error) {
            console.warn(`Failed to process pinyin for: ${text}`, error);
            return null;
        }
    }

    /**
     * 为搜索生成拼音变体
     * 包含完整拼音、首字母、部分匹配等多种形式
     */
    static generatePinyinVariants(text: string): string[] {
        const pinyinData = this.getPinyinData(text);
        if (!pinyinData) {
            return [];
        }

        const variants: string[] = [];

        // 1. 完整拼音
        variants.push(pinyinData.fullPinyin);

        // 2. 首字母缩写
        variants.push(pinyinData.initials);

        // 3. 部分拼音组合（支持部分输入）
        // 如 "项目" -> ["x", "xi", "xia", "xian", "xiang", "xm", "xim", ...]
        const {pinyinArray, initialsArray} = pinyinData;

        for (let i = 0; i < pinyinArray.length; i++) {
            // 完整拼音的前缀
            for (let j = 1; j <= pinyinArray[i].length; j++) {
                const prefix = pinyinArray.slice(0, i).join('') + pinyinArray[i].slice(0, j);
                variants.push(prefix);
            }

            // 首字母 + 部分拼音组合
            if (i > 0) {
                const initialPrefix = initialsArray.slice(0, i).join('');
                for (let j = 1; j <= pinyinArray[i].length; j++) {
                    const combined = initialPrefix + pinyinArray[i].slice(0, j);
                    variants.push(combined);
                }
            }
        }

        // 4. 递增的首字母组合
        // 如 "项目报告" -> ["x", "xm", "xmb", "xmbg"]
        for (let i = 1; i <= initialsArray.length; i++) {
            variants.push(initialsArray.slice(0, i).join(''));
        }

        // 去重并返回
        return [...new Set(variants)].filter(v => v.length > 0);
    }


    /**
     * 检查查询是否匹配拼音数据
     */
    static matchesPinyin(pinyinData: PinyinData, query: string): boolean {
        const queryLower = query.toLowerCase();

        // 完整拼音匹配
        if (pinyinData.fullPinyin.toLowerCase().includes(queryLower)) {
            return true;
        }

        // 首字母匹配
        if (pinyinData.initials.toLowerCase().includes(queryLower)) {
            return true;
        }

        // 部分拼音匹配
        for (const py of pinyinData.pinyinArray) {
            if (py.toLowerCase().includes(queryLower)) {
                return true;
            }
        }

        return false;
    }
}
