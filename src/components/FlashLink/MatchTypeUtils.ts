/**
 * 匹配类型工具函数
 *
 * 纯函数集合，用于判断不同类型的搜索匹配
 */

import {CommandTypeLabels, MatchTypes, HighlightSegment, CommandType} from './types';
import {PinyinData} from './PinyinProcessor';

/**
 * 拼音相关字段接口（集中管理所有拼音字段）
 */
export interface PinyinCommand {
    typeLabel: (typeof CommandTypeLabels)[CommandType];
    titlePinyin?: PinyinData;
    typeLabelPinyin?: PinyinData;
    descriptionPinyin?: PinyinData;
    // v2.2: 组合拼音字段
    combinedPinyinInitials?: string; // 组合首字母（类型+标题）
    combinedPinyinFull?: string; // 组合完整拼音（类型+标题）
}

/**
 * 基础命令接口
 */
interface BaseCommand {
    id: string;
    type: CommandType;
    title: string;
    description?: string;
}

/**
 * 预处理命令接口（用于类型约束）
 */
export type ProcessedCommand = BaseCommand & PinyinCommand;

/**
 * 检查精确文本匹配
 */
export function checkExactTextMatch(command: BaseCommand & {typeLabel: string}, queryLower: string): MatchTypes {
    // 标题匹配
    if (command.title.toLowerCase().includes(queryLower)) {
        return MatchTypes.EXACT;
    }

    // 类型标签匹配
    const typeLabel = CommandTypeLabels[command.type] || '未知';
    if (typeLabel.toLowerCase().includes(queryLower)) {
        return MatchTypes.EXACT;
    }

    return MatchTypes.FUZZY;
}

/**
 * 检查精确首字母匹配
 */
export function checkExactInitialsMatch(command: PinyinCommand, queryLower: string): MatchTypes {
    if (command.titlePinyin && command.titlePinyin.initials.toLowerCase() === queryLower) {
        return MatchTypes.PINYIN_ABBR;
    }
    if (command.typeLabelPinyin && command.typeLabelPinyin.initials.toLowerCase() === queryLower) {
        return MatchTypes.PINYIN_ABBR;
    }

    // 组合首字母精确匹配
    if (command.titlePinyin && command.typeLabelPinyin) {
        const combinedInitials = command.typeLabelPinyin.initials + command.titlePinyin.initials;
        if (combinedInitials.toLowerCase() === queryLower) {
            return MatchTypes.PINYIN_ABBR;
        }
    }

    return MatchTypes.FUZZY;
}

/**
 * 检查完整拼音匹配
 */
export function checkFullPinyinMatch(command: PinyinCommand, queryLower: string): MatchTypes {
    if (command.titlePinyin && command.titlePinyin.fullPinyin.toLowerCase().includes(queryLower)) {
        return MatchTypes.PINYIN;
    }
    if (command.typeLabelPinyin && command.typeLabelPinyin.fullPinyin.toLowerCase().includes(queryLower)) {
        return MatchTypes.PINYIN;
    }

    // 组合完整拼音匹配
    if (command.titlePinyin && command.typeLabelPinyin) {
        const combinedFullPinyin = command.typeLabelPinyin.fullPinyin + command.titlePinyin.fullPinyin;
        if (combinedFullPinyin.toLowerCase().includes(queryLower)) {
            return MatchTypes.PINYIN;
        }
    }

    return MatchTypes.FUZZY;
}

/**
 * 检查部分首字母匹配
 */
export function checkPartialInitialsMatch(command: PinyinCommand, queryLower: string): MatchTypes {
    if (command.titlePinyin && command.titlePinyin.initials.toLowerCase().includes(queryLower)) {
        return MatchTypes.PINYIN_ABBR;
    }
    if (command.typeLabelPinyin && command.typeLabelPinyin.initials.toLowerCase().includes(queryLower)) {
        return MatchTypes.PINYIN_ABBR;
    }

    return MatchTypes.FUZZY;
}

/**
 * 检查拼音匹配（组合所有拼音匹配逻辑）
 */
export function checkPinyinMatch(command: PinyinCommand, queryLower: string): MatchTypes {
    // 检查精确首字母匹配
    const exactInitialsMatch = checkExactInitialsMatch(command, queryLower);
    if (exactInitialsMatch !== MatchTypes.FUZZY) {
        return exactInitialsMatch;
    }

    // 检查完整拼音匹配
    const fullPinyinMatch = checkFullPinyinMatch(command, queryLower);
    if (fullPinyinMatch !== MatchTypes.FUZZY) {
        return fullPinyinMatch;
    }

    // 检查部分首字母匹配
    const partialInitialsMatch = checkPartialInitialsMatch(command, queryLower);
    if (partialInitialsMatch !== MatchTypes.FUZZY) {
        return partialInitialsMatch;
    }

    // 检查组合拼音匹配（v2.2 升级版）
    if (command.combinedPinyinInitials && command.combinedPinyinInitials.toLowerCase().includes(queryLower)) {
        return MatchTypes.PINYIN_ABBR;
    }

    if (command.combinedPinyinFull && command.combinedPinyinFull.toLowerCase().includes(queryLower)) {
        return MatchTypes.PINYIN;
    }

    return MatchTypes.FUZZY;
}

/**
 * 生成精确匹配的高亮片段
 */
export function generateExactHighlightSegments(title: string, query: string): HighlightSegment[] {
    const segments: HighlightSegment[] = [];
    const queryLower = query.toLowerCase();
    const titleLower = title.toLowerCase();

    let lastIndex = 0;
    let matchIndex = titleLower.indexOf(queryLower);

    while (matchIndex !== -1) {
        // 添加匹配前的文本
        if (matchIndex > lastIndex) {
            segments.push({
                text: title.slice(lastIndex, matchIndex),
                highlighted: false,
            });
        }

        // 添加匹配的文本
        segments.push({
            text: title.slice(matchIndex, matchIndex + query.length),
            highlighted: true,
        });

        lastIndex = matchIndex + query.length;
        matchIndex = titleLower.indexOf(queryLower, lastIndex);
    }

    // 添加剩余文本
    if (lastIndex < title.length) {
        segments.push({
            text: title.slice(lastIndex),
            highlighted: false,
        });
    }

    return segments.filter(seg => seg.text.length > 0);
}
