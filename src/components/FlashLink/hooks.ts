import React, {useMemo} from 'react';
import {useHistory} from 'react-router-dom';
import {omit} from 'lodash-es';
import {Shortcut} from '@baidu/light-ai-react';
import AdRoutes, {useAdRoute} from '@/modules/Ad/routes';
import {useGlobalProductContext} from '@/hooks/productLine';
import {sendMonitor} from '@/utils/logger';
import {createCommandRegistry} from './CommandRegistry';
import {getCommandHistoryManager} from './CommandHistoryManager';
import {createSearchEngine, FlashLinkShortcut} from './SearchEngine';
import {HighlightSegment, GLOBAL_SEARCH_CONSTANTS, QuickLinkCommand} from './types';


/**
 * FlashLink 配置选项
 */
export interface FlashLinkConfig {
    enableFallback?: boolean; // 是否启用 fallback 功能，默认 true
    adModule?: string;
}

/**
 * FlashLink 命令执行器 Hook
 * 在组件内部使用，自动获取所需的依赖
 */
export function useFlashLinkCommandExecutor(config?: FlashLinkConfig) {
    const history = useHistory();
    const {linkTo, linkToChat} = useAdRoute();
    const {product: globalProduct} = useGlobalProductContext();

    return useMemo(() => {
        const commandRegistry = createCommandRegistry(AdRoutes, globalProduct);
        const allCommands = commandRegistry.getAllCommands();
        const commandHistoryManager = getCommandHistoryManager();
        const searchEngine = createSearchEngine(commandRegistry, {
            enableFallback: config?.enableFallback ?? true, // 默认启用 fallback
        });

        // Helper function to render highlight segments
        const renderHighlightSegments = (segments: HighlightSegment[]): JSX.Element[] => {
            return segments.map((segment, index) => {
                // Create a unique key based on position and content
                const key = `${index}-${segment.text}-${segment.highlighted}`;
                // return segment.highlighted
                //     ? React.createElement('mark', {key}, segment.text)
                //     : React.createElement('span', {key}, segment.text);
                return React.createElement('span', {key}, segment.text);
            });
        };

        // 创建一个状态对象来管理当前输入状态
        const inputState = {
            selectedCommand: undefined as ReturnType<typeof commandRegistry.getAllCommands>[0] | undefined,
            inputValue: '', // 储存当前输入组件的状态
            originValue: '' as unknown,
            suggestions: [] as FlashLinkShortcut[], // 添加 suggestions 状态， 用于选中时执行suggestion带的命令
            // 触发execute时最后的query， 这里跟inputValue的区别是，当执行时， inputValue 已经通过 输入框的 onChange 改变过了， 无法最终到之前的搜索词
            query: '',
            // 埋点相关状态
            focusStartTime: 0, // focus 开始时间
        };

        const resetCurrentState = () => {
            inputState.inputValue = '';
            inputState.selectedCommand = undefined;
            inputState.originValue = '';
            inputState.suggestions = [];
            inputState.query = '';
            inputState.focusStartTime = 0;
        };


        // 辅助方法：从当前 suggestions 中查找匹配的命令
        const findCommandFromSuggestions = (value: string) => {
            const matchedSuggestion = inputState.suggestions.find(sug => sug.label === value);
            return matchedSuggestion?.command;
        };

        const executor = {
            executeCommand: (command: QuickLinkCommand) => {

                const commandId = command.id;

                if (!command) {
                    console.warn(`Command not found: ${commandId}`);
                    return false;
                }


                // 统一的历史记录处理
                const historyRecord = {
                    commandId: commandId.startsWith(GLOBAL_SEARCH_CONSTANTS.COMMAND_PREFIX)
                        ? GLOBAL_SEARCH_CONSTANTS.COMMAND_ID
                        : commandId,
                    title: command.title,
                    inputText: inputState.inputValue,
                    timestamp: Date.now(),
                    platform: globalProduct,
                };
                commandHistoryManager.addToHistory(historyRecord);

                // 执行命令
                try {
                    command.action.execute({
                        command,
                        inputText: inputState.inputValue,
                        linkTo,
                        linkToChat,
                        pushHistory: (path: string) => history.push(path),
                        product: globalProduct,
                    });

                    resetCurrentState();

                    return true;
                } catch (error) {
                    console.error(`Command execution failed: ${commandId}`, error);
                    return false;
                }
            },
            getAllCommands: () => allCommands,

            getSearchSuggestions: (inputValue: string): Promise<Array<Omit<FlashLinkShortcut, 'command'>>> => {
                let suggestionsPromise: Promise<FlashLinkShortcut[]>;

                // 模式三：物料搜索类型建议
                if (inputState.selectedCommand?.id === GLOBAL_SEARCH_CONSTANTS.COMMAND_ID && inputValue) {
                    suggestionsPromise = Promise.resolve(
                        searchEngine.getGlobalSearchSuggestions(inputValue)
                    );
                }
                // 模式二：常规命令建议
                else if (inputValue && (!inputState.selectedCommand || !inputState.selectedCommand.requiresInput)) {
                    suggestionsPromise = searchEngine.searchAndConvertToShortcutsDebounced(
                        inputValue,
                        renderHighlightSegments
                    );
                }
                // 其他情况返回空数组
                else {
                    suggestionsPromise = Promise.resolve([]);
                }

                // 更新 suggestions 状态并返回外部版本
                return suggestionsPromise.then(suggestions => {
                    // 保存完整的内部状态（包含 command 字段）
                    inputState.suggestions = suggestions;

                    // 返回给外部的简化版本（移除 command 字段）
                    return suggestions.map(sug => omit(sug, 'command'));
                });
            },
            // 输入处理方法
            handleInputChange: (e: {target: {value: unknown, rawValue?: unknown}}) => {
                let actualInputValue = '';
                let finalValue = e.target.value;
                // 检测 rawValue 结构来判断当前状态
                if (Array.isArray(e.target.rawValue)) {
                    // 查找是否有 shortcut 类型的项
                    const shortcutItem = e.target.rawValue.find(item =>
                        typeof item === 'object' && item.type === 'shortcut'
                    );

                    if (shortcutItem) {
                        // 有 shortcut 说明是二次输入状态（display tag 情况）
                        // 根据 shortcut key 获取对应的命令
                        const textParts = e.target.rawValue.filter(item => typeof item === 'string');
                        const currentInput = textParts.join('').trim();
                        const matchedCommand = commandRegistry.getCommandById(shortcutItem.key);

                        const shouldInherit = inputState.selectedCommand !== matchedCommand;

                        inputState.selectedCommand = matchedCommand;

                        actualInputValue = matchedCommand?.inheritInputValue && shouldInherit
                            ? inputState.inputValue : currentInput;

                        if (matchedCommand?.inheritInputValue) {
                            finalValue = [shortcutItem, actualInputValue];
                        }
                    } else {
                        // 没有 shortcut，重置命令选择状态
                        inputState.selectedCommand = undefined;
                        actualInputValue = typeof e.target.value === 'string' ? e.target.value : '';
                    }
                } else {
                    // 非数组情况，判断是否选择了命令
                    const valueStr = typeof e.target.value === 'string' ? e.target.value : '';

                    // 首先从当前 suggestions 中查找匹配的命令
                    let matchedCommand = findCommandFromSuggestions(valueStr);

                    // 如果没找到，再通过title匹配已有命令（非历史记录）
                    if (!matchedCommand) {
                        const allCommands = commandRegistry.getAllCommands();
                        matchedCommand = allCommands.find(cmd => cmd.title === valueStr);
                    }

                    if (matchedCommand) {
                        // 找到匹配的命令
                        inputState.selectedCommand = matchedCommand;
                        actualInputValue = valueStr;
                    } else {
                        // 正常的文本输入情况，重置命令选择状态
                        inputState.selectedCommand = undefined;
                        actualInputValue = valueStr;
                    }
                }

                inputState.inputValue = actualInputValue;
                inputState.originValue = finalValue;

                return inputState;
            },

            handleShortcutChange: (shortcut: Shortcut) => {
                inputState.query = getPlainTextFromOmniValue(inputState.inputValue);
            },

            // 处理回车键 - 统一处理所有命令执行
            // 需要二次输入的指令如果没有输入走不到这里， omni组件已经处理过了
            // 所以这里不用考虑二次输入的场景， 例如物料搜索
            // 输入为空也走不到这里
            handleEnterPress: () => {
                const duration = inputState.focusStartTime ? Date.now() - inputState.focusStartTime : 0;

                if (inputState.selectedCommand) {
                    const command = inputState.selectedCommand;

                    // 发送命令执行埋点
                    sendMonitor('flash_link', {
                        field: 'command_execute',
                        query: inputState.query,
                        'input_value': getPlainTextFromOmniValue(inputState.inputValue),
                        'result_count': inputState.suggestions.length,
                        'command_id': command.id,
                        'command_title': command.title,
                        'is_history': command.isHistory ? 1 : 0,
                        'has_input': command.requiresInput ? 1 : 0,
                        'is_chat': 0,
                        duration,
                        platform: globalProduct,
                    });

                    // 使用内部的 executeCommand 方法（已包含历史记录逻辑）
                    return {
                        isSuccess: executor.executeCommand(command),
                        isChat: false,
                    };
                }

                const promptForChat = inputState.inputValue;

                // 发送到聊天的埋点
                sendMonitor('flash_link', {
                    field: 'command_execute',
                    query: inputState.query,
                    'input_value': getPlainTextFromOmniValue(inputState.inputValue),
                    'result_count': inputState.suggestions.length,
                    'command_id': '',
                    'command_title': '',
                    'is_history': 0,
                    'has_input': 0,
                    'is_chat': 1,
                    duration,
                    platform: globalProduct,
                });

                resetCurrentState();
                return {
                    isSuccess: true,
                    isChat: true,
                    promptForChat,
                };
            },

            // 获取当前状态
            getCurrentState: () => inputState,
            // 获取平台信息
            getGlobalProduct: () => globalProduct,
            // 获取 shortcuts，直接使用 searchEngine 的方法
            getShortcuts: (): FlashLinkShortcut[] => {
                return searchEngine.getShortcuts(
                    commandHistoryManager,
                    globalProduct,
                    renderHighlightSegments,
                    config?.adModule
                );
            },
            // 事件处理方法
            handleFocus: () => {
                inputState.focusStartTime = Date.now();
                // 发送 focus 埋点
                sendMonitor('flash_link', {
                    field: 'focus',
                    platform: globalProduct,
                    'is_continue': (!!inputState.inputValue || !!inputState.selectedCommand) ? 1 : 0,
                });
            },
            handleBlur: () => {

                const duration = inputState.focusStartTime ? Date.now() - inputState.focusStartTime : 0;
                inputState.focusStartTime = 0;
                sendMonitor('flash_link', {
                    field: 'blur',
                    query: inputState.query,
                    'input_value': getPlainTextFromOmniValue(inputState.inputValue),
                    'result_count': inputState.suggestions.length,
                    'command_id': inputState.selectedCommand?.id || '',
                    'command_title': inputState.selectedCommand?.title || '',
                    'is_history': (inputState.selectedCommand?.isHistory || false) ? 1 : 0,
                    'has_input': (inputState.selectedCommand?.requiresInput || false) ? 1 : 0,
                    duration,
                    platform: globalProduct,
                    abandon: (!!inputState.inputValue || !!inputState.selectedCommand) ? 1 : 0,
                });
            },
            handleKeyboardShow: () => {
                sendMonitor('flash_link', {
                    field: 'show_keyboard',
                    platform: globalProduct,
                });
            },
            handleKeyboardHide: () => {
                // 展开状态才发送
                if (inputState.focusStartTime) {
                    sendMonitor('flash_link', {
                        field: 'hide_keyboard',
                        platform: globalProduct,
                    });
                }
            },
            handleExpand: () => {
                sendMonitor('flash_link', {
                    field: 'expand',
                    platform: globalProduct,
                });
            },
            handleCollapse: () => {
                sendMonitor('flash_link', {
                    field: 'collapse',
                    platform: globalProduct,
                });
            },
        };

        return executor;
    }, [globalProduct, linkTo, linkToChat, history, config?.enableFallback, config?.adModule]);
}


function getPlainTextFromOmniValue(inputValue: any) {
    if (Array.isArray(inputValue)) {
        return inputValue.filter(i => typeof i === 'string').join(' ');
    }
    return typeof inputValue === 'string' ? inputValue : '';
}
