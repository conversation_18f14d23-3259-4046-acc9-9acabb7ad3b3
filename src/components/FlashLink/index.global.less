.flash-link-container {
    position: fixed;
    bottom: 48px;
    left: 50%;
    transform: translateX(-50%);
    width: auto;
    border-radius: 8px;
    overflow: visible;
    display: flex;
    flex-direction: column;
    pointer-events: auto;
    transition: all 0.3s ease;

    // ! 这里设置为 999 下面附了一些one ui 比较大的 z-index 防止覆盖在这些上面
    z-index: 999;
    // @dls-dropdown-z-index: 1051;
    // @dls-tooltip-z-index: 1051;
    // @dls-menu-z-index: 1050;
    // @dls-select-z-index: 1051;
    // @dls-toast-z-index: 1052;
    // @dls-dialog-z-index: 1040;
    // @dls-popover-z-index: 1051;
    // @dls-time-picker-z-index: 1051;



    &.collapsed {
        .flash-link-input {
            display: none;
        }
    }

    // 用伪元素创建连接桥梁，保持hover状态连续性
    &:not(.collapsed)::after {
        content: '';
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 25px;
        z-index: -1;
    }

    .flash-link-expand-btn,
    .flash-link-collapse-btn {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        cursor: pointer;
        display: inline-flex;
        padding: 8px 12px 6px;
        align-items: center;
        justify-content: center;
        border-radius: 8px 8px 0 0;
        border: 1px solid transparent;
        transition: all 0.2s ease;
        opacity: 0;
        visibility: hidden;

        // 图标样式
        .flash-link-arrow-down,
        .flash-link-arrow-up {
            font-size: 20px;
            color: #1890ff;
        }
    }

    // 展开按钮始终可见，有hover效果
    .flash-link-expand-btn {
        opacity: 1;
        visibility: visible;
        background: #fff;
        border: 1px solid rgba(102, 146, 222, 0.15);
        box-shadow:
            0 4px 20px 1px rgba(0, 71, 194, 0.04),
            0 3px 16px 1px rgba(0, 71, 194, 0.03),
            0 2px 12px 1px rgba(0, 71, 194, 0.02);
        bottom: -47px;

        &:hover {
            transform: translateX(-50%) scale(1.05);
            box-shadow:
                0 6px 24px 2px rgba(0, 71, 194, 0.06),
                0 4px 20px 1px rgba(0, 71, 194, 0.05),
                0 3px 16px 1px rgba(0, 71, 194, 0.04);
        }
    }

    // 当页面有底部条时，向上移动40px
    &.has-bottom-bar {
        bottom: 88px;

        .flash-link-expand-btn {
            border-color: transparent;
            background: transparent;
            box-shadow: none;
        }
    }


    // 收起按钮的hover效果（无背景和阴影变化）
    .flash-link-collapse-btn {
        bottom: -40px;

        &:hover {
            opacity: 1;
            visibility: visible;
        }
    }

    // 容器hover时显示收起按钮
    &:hover {
        .flash-link-collapse-btn {
            opacity: 1;
            visibility: visible;
        }
    }

    .light-ai-omni-input-shortcuts-list {
        max-height: 200px;
    }
}

