import React, {useRef} from 'react';
import {OmniInputProps} from '@baidu/light-ai-react';
import aiChatProxy from '@/utils/aiChat';
import {useGlobalShortcut, GlobalShortcutName} from '@/hooks/keyboard/useGlobalShortcut';
import {useFlashLinkCommandExecutor} from './hooks';
import FlashLink, {getIsHomePageInput} from './index';


const FlashLinkTrigger: React.FC = () => {
    const inputRef: OmniInputProps['instance'] = useRef(null);
    const flashLinkRef = useRef<{handleExpand: () => void}>(null);
    const commandExecutor = useFlashLinkCommandExecutor();

    const focusAiChatInput = () => {
        aiChatProxy.instance.focusInput();
        aiChatProxy.instance.setShortcutVisible(true);
    };

    useGlobalShortcut(GlobalShortcutName.FlashLinkTrigger, () => {

        if (getIsHomePageInput(location.pathname)) {
            // 首页：聚焦现有输入框
            focusAiChatInput();
        } else {
            // 非首页：展开并聚焦FlashLink（内部已包含聚焦逻辑）
            flashLinkRef.current?.handleExpand();
        }

        commandExecutor.handleKeyboardShow();
    });

    return (
        <FlashLink
            inputRef={inputRef}
            ref={flashLinkRef}
        />
    );
};

export default FlashLinkTrigger;
