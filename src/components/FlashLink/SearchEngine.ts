/**
 * FlashLink 简化搜索引擎
 *
 * 基于 Fuse.js 的简单实用搜索实现，支持可选的拼音搜索
 */

import Fuse, {FuseResult} from 'fuse.js';
import React from 'react';
import {PLATFORM_ENUM} from '@/dicts';
import AdRoutes, {getAdModule} from '@/modules/Ad/routes';
import {
    QuickLinkCommand,
    SearchResult,
    CommandTypeLabels,
    MatchTypes,
    HighlightSegment,
    GLOBAL_SEARCH_CONSTANTS,
} from './types';
import {PinyinProcessor} from './PinyinProcessor';
import {
    PinyinCommand,
    checkExactTextMatch,
    checkPinyinMatch,
    generateExactHighlightSegments,
} from './MatchTypeUtils';
import {CommandHistoryManager} from './CommandHistoryManager';
import {CommandRegistry} from './CommandRegistry';

// 专用的取消错误类型
export class DebounceCancelledError extends Error {
    constructor() {
        super('Search request was cancelled by debounce');
        this.name = 'DebounceCancelledError';
    }
}

// FlashLinkShortcut 接口定义
export interface FlashLinkShortcut {
    label: string;
    value: string;
    desc?: string;
    autoSend?: boolean;
    display?: 'tag' | undefined;
    LabelRender?: () => JSX.Element;
    command: QuickLinkCommand; // 添加对应的命令对象
}

/**
 * 搜索引擎配置
 */
export interface SimpleSearchConfig {
  threshold: number; // 匹配阈值
  maxResults: number; // 最大结果数
  enablePinyin: boolean; // 是否启用拼音搜索
  enableFallback: boolean; // 是否启用 fallback 功能
}

/**
 * 默认搜索配置
 */
const DEFAULT_CONFIG: SimpleSearchConfig = {
    threshold: 0.48,
    maxResults: 8,
    enablePinyin: true,
    enableFallback: true, // 默认启用 fallback 功能
};

/**
 * 预处理命令（继承拼音字段）
 */
interface ProcessedCommand extends QuickLinkCommand, PinyinCommand {
  // 所有拼音相关字段已在 PinyinCommand 中定义
}


/**
 * FlashLink 简化搜索引擎
 */
export class FlashLinkSearchEngine {
    private commands: ProcessedCommand[] = [];
    private readonly config: SimpleSearchConfig;
    private fuseEngine!: Fuse<ProcessedCommand>;
    private readonly commandRegistry: CommandRegistry;

    // 防抖相关属性
    private debounceTimer: NodeJS.Timeout | null = null;
    private currentReject: ((reason?: any) => void) | null = null;

    constructor(
        commandRegistry: CommandRegistry,
        config?: Partial<SimpleSearchConfig>
    ) {
        this.commandRegistry = commandRegistry;
        this.config = {...DEFAULT_CONFIG, ...config};
        this.commands = commandRegistry.getAllCommands() as ProcessedCommand[];
        this.preprocessCommands(this.commands);
        this.setupFuseEngine();
    }

    // 已移除 getAllAvailableCommands 方法
    // 新架构使用 commandRegistry.getCommandById() 按需获取命令
    /**
   * 执行搜索并返回带高亮的结果
   */
    searchWithHighlight(query: string): SearchResult[] {
        if (!query || !query.trim()) {
            return this.commands.slice(0, this.config.maxResults).map(cmd => ({
                command: cmd,
                score: 100,
                matchType: MatchTypes.EXACT,
                matchedField: 'title',
                highlightSegments: [{
                    text: cmd.title,
                    highlighted: false,
                }],
            }));
        }

        const trimmedQuery = query.trim();
        const results = this.fuseEngine.search(trimmedQuery);


        // 先过滤掉全局搜索指令，然后处理常规搜索结果
        const filteredResults = results.filter(result =>
            result.item.id !== GLOBAL_SEARCH_CONSTANTS.COMMAND_ID
        );

        const searchResults: SearchResult[] = filteredResults
            .slice(0, this.config.maxResults)
            .map(result => {
                const matchType = this.determineMatchType(result.item, trimmedQuery);
                const highlightSegments = this.generateHighlightSegmentsFromFuse(result, trimmedQuery);
                return {
                    command: result.item,
                    score: (1 - (result.score || 0)) * 100,
                    matchType,
                    matchedField: this.getMatchedField(result) as 'title' | 'description' | 'keywords',
                    highlightSegments,
                };
            });

        // 添加全局搜索fallback（无论是否有搜索结果）
        const globalFallbackResult = this.createGlobalFallbackResult(trimmedQuery);
        if (globalFallbackResult) {
            searchResults.push(globalFallbackResult);
        }

        return searchResults;
    }

    /**
     * 将搜索结果转换为 FlashLinkShortcut 数组
     */
    convertSearchResultsToShortcuts(
        results: SearchResult[],
        renderHighlightSegments: (segments: HighlightSegment[]) => JSX.Element[]
    ): FlashLinkShortcut[] {
        return results.map(result => {
            const command = result.command;
            const segments = result.highlightSegments;

            // 预计算渲染元素，避免在每次调用时重新计算
            const renderedElements = renderHighlightSegments(segments);

            return {
                label: command.title,
                value: command.id,
                desc: command.description,
                autoSend: !command.requiresInput,
                display: command.requiresInput ? 'tag' : undefined,
                LabelRender: () => React.createElement('span', {}, ...renderedElements),
                command: command, // 添加对应的命令对象
            };
        });
    }

    /**
     * 搜索并直接转换为 FlashLinkShortcut 数组（组合方法）
     */
    searchAndConvertToShortcuts(
        query: string,
        renderHighlightSegments: (segments: HighlightSegment[]) => JSX.Element[]
    ): FlashLinkShortcut[] {
        const results = this.searchWithHighlight(query);
        return this.convertSearchResultsToShortcuts(results, renderHighlightSegments);
    }

    /**
     * 防抖版本的搜索方法
     */
    async searchAndConvertToShortcutsDebounced(
        query: string,
        renderHighlightSegments: (segments: HighlightSegment[]) => JSX.Element[],
        debounceMs: number = 300
    ): Promise<FlashLinkShortcut[]> {
        // reject 之前的 Promise
        if (this.currentReject) {
            this.currentReject(new DebounceCancelledError());
        }

        // 清除之前的定时器
        if (this.debounceTimer) {
            clearTimeout(this.debounceTimer);
        }

        return new Promise((resolve, reject) => {
            this.currentReject = reject;

            this.debounceTimer = setTimeout(() => {
                try {
                    const result = this.searchAndConvertToShortcuts(query, renderHighlightSegments);
                    resolve(result);
                } catch (error) {
                    reject(error);
                } finally {
                    this.debounceTimer = null;
                    this.currentReject = null;
                }
            }, debounceMs);
        });
    }

    /**
     * 获取 FlashLink shortcuts，支持历史记录和默认展示项
     */
    getShortcuts(
        commandHistoryManager: CommandHistoryManager,
        globalProduct: PLATFORM_ENUM,
        renderHighlightSegments: (segments: HighlightSegment[]) => JSX.Element[],
        adModule?: string
    ): FlashLinkShortcut[] {
        const MAX_SHORTCUTS = 8;
        const MAX_HISTORY_COUNT = 3;

        const recentCommands = commandHistoryManager.getRecentCommands(MAX_HISTORY_COUNT, globalProduct);
        const remainingDefaultSlots = MAX_SHORTCUTS - recentCommands.length;

        // 收集最终的命令列表
        const finalCommands: Array<{command: ProcessedCommand, isHistory: boolean, usageCount?: number}> = [];
        const usedCommandIds = new Set<string>();

        // 优先添加历史记录
        recentCommands.forEach(history => {
            const command = this.commands.find(cmd => cmd.id === history.commandId);
            if (command) {
                usedCommandIds.add(command.id);
                finalCommands.push({
                    command,
                    isHistory: true,
                    usageCount: history.count,
                });
            }
        });

        const pageAdModule = adModule || getAdModule();
        const pageType = AdRoutes.getItemByRouteFullPath?.(location.pathname)?.name;


        // 添加默认展示项（避免重复）
        let addedDefaultCount = 0;

        // 分离匹配和非匹配的命令，减少重复遍历
        const defaultDisplayCommands: ProcessedCommand[] = [];
        const matchingCommands: ProcessedCommand[] = [];
        const otherCommands: ProcessedCommand[] = [];

        for (const command of this.commands) {
            if (!usedCommandIds.has(command.id)) {
                // 优先处理 defaultDisplay 命令
                if (command.defaultDisplay) {
                    defaultDisplayCommands.push(command);
                } else if (command.adModule === pageAdModule) {
                    // 过滤掉当前页面， 例如在方案列表页，就不展示方案列表页的command
                    pageType !== command.pageType && matchingCommands.push(command);
                } else {
                    otherCommands.push(command);
                }
            }
        }

        // 添加匹配当前页面模块的命令
        for (const command of matchingCommands) {
            if (addedDefaultCount >= remainingDefaultSlots) {
                break;
            }
            finalCommands.push({
                command,
                isHistory: false,
            });
            addedDefaultCount++;
        }

        // 添加 defaultDisplay 命令
        for (const command of defaultDisplayCommands) {
            finalCommands.push({
                command,
                isHistory: false,
            });
            addedDefaultCount++;
        }

        // 最后添加其他命令
        for (const command of otherCommands) {
            if (addedDefaultCount >= remainingDefaultSlots) {
                break;
            }
            finalCommands.push({
                command,
                isHistory: false,
            });
            addedDefaultCount++;
        }

        const sortedFinalCommands = finalCommands.sort((a, b) => {
            if (a.isHistory && !b.isHistory) {
                return -1;
            }
            if (!a.isHistory && b.isHistory) {
                return 1;
            }
            if (a.isHistory === b.isHistory) {
                if (!a.isHistory) {
                    return (a.command.order ?? 999) - (b.command.order ?? 999);
                }
                return 0;
            }
            return 0;
        });

        // 检查前N个位置是否已经有全局搜索（通过历史记录）
        const finalResult = [...sortedFinalCommands];
        const globalSearchIndex = finalResult.findIndex(item =>
            item.command.id === GLOBAL_SEARCH_CONSTANTS.COMMAND_ID
        );

        // 如果全局搜索不在前5个位置，则添加到最后一个位置（不超过第5个）
        if (globalSearchIndex === -1) {
            // 没有全局搜索，需要添加
            const globalSearchCmd = this.commands.find(cmd =>
                cmd.id === GLOBAL_SEARCH_CONSTANTS.COMMAND_ID
            );

            if (globalSearchCmd) {
                const globalSearchItem = {
                    command: globalSearchCmd,
                    isHistory: false,
                };

                // 添加到最后一个位置，但不超过第5个
                if (finalResult.length < 4) {
                    finalResult.push(globalSearchItem);
                } else {
                    // 插入到第5位（索引4）
                    finalResult.splice(4, 0, globalSearchItem);
                }
            }
        }
        // 如果全局搜索已经在列表中（通过历史记录），保持原来的顺序，不做任何处理

        return finalResult.map(item => {
            const command = item.command;
            const segments = [{
                text: command.title,
                highlighted: false,
            }];

            // 预计算渲染元素，避免在每次调用时重新计算
            const renderedElements = renderHighlightSegments(segments);

            return {
                label: command.title,
                value: command.id,
                desc: item.isHistory
                    ? '最近常用'
                    : command.description,
                autoSend: !command.requiresInput,
                display: command.requiresInput ? 'tag' : undefined,
                LabelRender: () => React.createElement('span', {}, ...renderedElements),
                command: command, // 添加对应的命令对象
                isHistory: item.isHistory, // 添加历史记录标记
            };
        });
    }

    /**
     * 获取物料搜索建议
     */
    getGlobalSearchSuggestions(
        query: string,
    ): FlashLinkShortcut[] {
        const searchableCommands = this.commandRegistry.generateFallbackSearchCommands(query);

        return searchableCommands.map(baseCommand => {
            return {
                label: baseCommand.title,
                value: baseCommand.id,
                desc: baseCommand.description,
                autoSend: true,
                display: undefined,
                command: baseCommand, // 添加动态生成的命令对象
            };
        });
    }

    /**
     * 创建全局搜索fallback结果
     */
    private createGlobalFallbackResult(query: string): SearchResult | null {
        const globalFallbackCmd = this.commands.find(i => {
            return i.id === GLOBAL_SEARCH_CONSTANTS.COMMAND_ID;
        });

        if (!globalFallbackCmd) {
            return null;
        }

        return {
            command: globalFallbackCmd,
            score: 1,
            matchType: MatchTypes.FALLBACK,
            matchedField: 'title' as const,
            highlightSegments: [
                {text: `${globalFallbackCmd.title}“${query}”`, highlighted: false},
            ],
        };
    }

    /**
     * 获取匹配的字段
     */
    private getMatchedField(result: FuseResult<ProcessedCommand>): string {
        if (result.matches && result.matches.length > 0) {
            return result.matches[0].key || 'title';
        }
        return 'title';
    }

    /**
     * 使用 Fuse.js 匹配结果生成高亮片段
     */
    private generateHighlightSegmentsFromFuse(
        result: any,
        query: string
    ): HighlightSegment[] {
        return this.generateHighlightSegments(result.item, query);
    }

    /**
   * 预处理命令数据
   */
    private preprocessCommands(commands: QuickLinkCommand[]): void {
        this.commands = commands.map(command => {
            const typeLabel = CommandTypeLabels[command.type];

            const processed: ProcessedCommand = {
                ...command,
                typeLabel,
            };

            // 生成拼音数据（如果启用）
            if (this.config.enablePinyin) {
                // 标题拼音
                processed.titlePinyin = PinyinProcessor.getPinyinData(command.title) || undefined;

                // 类型标签拼音
                processed.typeLabelPinyin = PinyinProcessor.getPinyinData(typeLabel) || undefined;

                // 描述拼音
                if (command.description) {
                    processed.descriptionPinyin = PinyinProcessor.getPinyinData(command.description) || undefined;
                }

                // 生成组合拼音数据（类型+标题）
                const typePinyin = processed.typeLabelPinyin;
                const titlePinyin = processed.titlePinyin;

                if (typePinyin && titlePinyin) {
                    // 组合首字母：tz + ccb = tzccb
                    processed.combinedPinyinInitials = typePinyin.initials + titlePinyin.initials;

                    // 组合完整拼音：tiaozhuan + chaochengben = tiaozhuanchaochengben
                    processed.combinedPinyinFull = typePinyin.fullPinyin + titlePinyin.fullPinyin;
                }
            }

            return processed;
        });
    }

    /**
   * 设置 Fuse.js 搜索引擎
   */
    private setupFuseEngine(): void {
        const baseKeys = [
            {name: 'title', weight: 0.7},
            {name: 'keywords', weight: 0.6},
            {name: 'description', weight: 0.4},
        ];

        // 如果启用拼音，添加拼音搜索字段
        const pinyinKeys = this.config.enablePinyin ? [
            {name: 'titlePinyin.fullPinyin', weight: 0.5}, // 标题完整拼音
            {name: 'combinedPinyinFull', weight: 0.48}, // 组合完整拼音（类型+标题）
            {name: 'titlePinyin.initials', weight: 0.45}, // 标题首字母
            {name: 'combinedPinyinInitials', weight: 0.43}, // 组合首字母（类型+标题）
            {name: 'descriptionPinyin.fullPinyin', weight: 0.25}, // 描述完整拼音
            {name: 'descriptionPinyin.initials', weight: 0.22}, // 描述首字母
        ] : [];

        const fuseOptions = {
            keys: [...baseKeys, ...pinyinKeys],
            threshold: this.config.threshold,
            includeScore: true,
            includeMatches: true, // 启用 Fuse.js 内置匹配信息
            minMatchCharLength: 1,
            shouldSort: true,
        };

        this.fuseEngine = new Fuse(this.commands, fuseOptions);
    }


    /**
     * 匹配类型判断（包含拼音匹配）
     */
    private determineMatchType(command: ProcessedCommand, query: string): MatchTypes {
        const queryLower = query.toLowerCase();

        // 1. 检查精确文本匹配
        const exactMatch = checkExactTextMatch(command, queryLower);
        if (exactMatch !== MatchTypes.FUZZY) {
            return exactMatch;
        }

        // 2. 检查拼音匹配
        if (this.config.enablePinyin) {
            const pinyinMatch = checkPinyinMatch(command, queryLower);
            if (pinyinMatch !== MatchTypes.FUZZY) {
                return pinyinMatch;
            }
        }

        return MatchTypes.FUZZY;
    }


    /**
   * 生成结构化高亮片段（简化版：只支持字面完全匹配高亮）
   */
    private generateHighlightSegments(
        command: ProcessedCommand,
        query: string
    ): HighlightSegment[] {
        if (!query) {
            return [{text: command.title, highlighted: false}];
        }

        const queryLower = query.toLowerCase();

        // 检查命令标题是否也有匹配（字面匹配）
        const titleLower = command.title.toLowerCase();
        const isTitleMatch = titleLower.includes(queryLower);

        // 生成显示标题段落，只对字面匹配的部分进行高亮
        const segments: HighlightSegment[] = [];

        // 添加命令标题部分
        if (isTitleMatch) {
            // 命令标题有字面匹配，生成高亮片段
            const titleSegments = generateExactHighlightSegments(command.title, query);
            segments.push(...titleSegments);
        } else {
            // 无匹配，整体不高亮
            segments.push({text: command.title, highlighted: false});
        }

        return segments;
    }


}

/**
 * 创建搜索引擎实例
 */
export function createSearchEngine(
    commandRegistry: CommandRegistry,
    config?: Partial<SimpleSearchConfig>
): FlashLinkSearchEngine {
    return new FlashLinkSearchEngine(commandRegistry, config);
}

