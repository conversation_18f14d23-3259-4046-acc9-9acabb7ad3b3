import {Component, ReactNode} from 'react';
import {PageTips} from '@baidu/one-ui-pro';
import * as <PERSON><PERSON> from '@baidu/weirwood-sdk';
import {getOperatorId, getUserId} from '@/utils';


import WeirwoodOptions from '../../weirwood.json';


const {Error} = PageTips;

const options = {
    common: {
        buildid: WeirwoodOptions.buildid,
        token: WeirwoodOptions.token,
        tarAddress: WeirwoodOptions.tarAddress,
    },
    error: {
        collectWindowErrors: true,
        // fix: weirwood资源加载异常为空
        // 参考：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/gv0WKdV3ZF/60incnF1Zd/DulGcS3TdxNBfY
        collectResourceLoadErrors: true,
    },
    perf: {
        ttiWaitForXHRDone: true,
        ttiWaitForXHRIgnore: [ // 修正后的TTI计算起点之前需要忽略的XHR
            'fclog.baidu.com',
            'log.im.baidu.com',
            'bj.bcebos.com/papfeedback',
            'getAgileToBUrl',
            'DomScreenModService',
            'getProphetTobList',
            'getCourseToBList',
            'getHelpInformation',
            'getReportConfigs',
            'getQuickInstructions',
            'weirwoodIgnored',
        ],
        afterOnload: true,
        initiatorTypes: ['spa_hard', 'spa', 'xhr', 'interaction'],
    },
};

export const weirwood = Weirwood.init(options);
(window as any).__Weirwood.perf.subscribe(
    'route_change',
    ({initiator}: {initiator?: string} = {}) => {
        (window as any).currentSpaNav = initiator;
    }
);


interface ErrorBoundaryProps {
    children?: ReactNode;
}
export class WeirwoodErrorBoundary extends Component<ErrorBoundaryProps, {hasError: boolean}> {

    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = {hasError: false};
        const userId = getUserId();
        const optId = getOperatorId();
        // 分别设置js和api异常上报用户id
        weirwood.error.setContext({userId});
        weirwood.error.setContext({optId});
        weirwood.perf.addVar('userId', userId);
        weirwood.perf.addVar('optId', optId);

    }

    static getDerivedStateFromError() {
        return {hasError: true};
    }

    componentDidCatch(error: Error) {
        weirwood.error.captureException(error);
    }

    render() {
        if (this.state.hasError) {
            return <Error />;
        }
        return this.props.children;
    }
}
