import React, {useRef, useCallback} from 'react';
import classNames from 'classnames';
import {useSingletonIframe} from '@/hooks/useSingletonIframe';

interface SingletonIframeProps {
    iframePath: string;
    width?: string;
    height?: string;
    extraSearch?: Record<string, any>;
    style?: React.CSSProperties;
    autoShow?: boolean;
    className?: string;
    iframeStyle?: string;
    containerStyle?: string;
    iframeClassName?: string;
}

const SingletonIframe: React.FC<SingletonIframeProps> = ({
    iframePath,
    width = '100%',
    height = '100%',
    extraSearch = {},
    style = {},
    autoShow = true,
    className,
    iframeStyle,
    containerStyle,
    iframeClassName,
}) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const getContainer = useCallback(() => {
        return containerRef.current || undefined;
    }, []);

    useSingletonIframe({
        iframePath,
        extraSearch,
        getContainer,
        autoShow,
        iframeStyle,
        containerStyle,
        iframeClassName,
    });

    const cls = classNames('singleton-iframe', className);
    return (
        <div
            ref={containerRef}
            className={cls}
            style={{
                width,
                height,
                ...style,
            }}
        />
    );
};

export default SingletonIframe;
