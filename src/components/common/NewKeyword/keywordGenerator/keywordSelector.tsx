import {useCallback, useEffect, useMemo, useState} from 'react';
import {But<PERSON>} from '@baidu/light-ai-react';
import {IconAdjust, IconDownload} from 'dls-icons-react';
import {KEYWORD_RECOMMEND} from 'commonLibs/config/idType';
import {MultiLabelInput, BatchToolbar} from '@baidu/one-ui-pro';
import {
    Button as OneButton, Tabs,
    TextArea, Table, TabsProps, TableProps,
    Toast, Tooltip,
} from '@baidu/one-ui';
import CustomFieldsSelector from 'commonLibs/materialList/CustomFieldsSelector';
import {useShowKeywordGroupGuide} from 'toolsCenter/kr/util';
import {getLengthInBytes} from '@/utils/string';
import {useColumns} from '@/hooks/tableList/columns';
import {getUserId} from '@/utils';
import {useRegister} from '@/hooks/tableList/register';
import {getKeywordPromptByUrl} from '@/api/fcThreeLevel/keywords';
import {useTableSort} from '@/hooks/tableList/sorter';
import {sendMonitor} from '@/utils/logger';
import {toOneUIRowSelectionProps} from '@/utils/selection';
import FilterList from '../../materialList/FilterList';
import {AcceptFunnelItem, sendKeywordAcceptFunnel} from '../util';
import {useKeywordSharedInfo} from '../context';
import {
    GENERATOR_KEYWORD_TYPE,
    GENERATOR_KEYWORD_TYPE_TEXT,
    keywordAddFromMap,
    FcKeywordType,
    TableKeywordItem,
    MAX_KEYWORD_COUNT,
} from './config';
import {keywordTableFieldsMap, keywordColumnConfigsV2, defaultColumns} from './keywordTableConfig';
import {useKeywordList, getKey} from './hooks/useKeywordList';
import {generateItem} from './util';
import {BufferMethodsType} from './hooks/useKeywordBufferRows';
import {useColumnConfiguration} from './hooks/useColumnConfiguration';

const getLength = (str: string) => {
    // 1中文字符2个字节，1英文字母1个字节
    return getLengthInBytes(str, [], []);
};


const DEFAULT_PAGE_SIZE = 20;

const MAX_PROMPT_LENGTH = 1000;
const MIN_PROMPT_LENGTH = 20;

const MAX_SEED_WORD_COUNT = 10;

const MAX_SEED_WORD_LENGTH = 64;

// eslint-disable-next-line complexity, max-statements
export function KeywordSelector({
    rows,
    bufferMethods,
    url,
    levelInfo,
}: {
    rows: TableKeywordItem[];
    bufferMethods: BufferMethodsType;
    url?: string;
    levelInfo: {
        campaignId?: number;
        adgroupId?: number;
    };
}) {
    const userId = getUserId();
    const [showGuide] = useShowKeywordGroupGuide({userId});
    const {columnConfiguration, modConfiguration} = useColumnConfiguration(
        KEYWORD_RECOMMEND, keywordColumnConfigsV2, defaultColumns,
    );

    // 拓词类型（prompt、种子词）
    const [generatorType, setGeneratorType] = useState<GENERATOR_KEYWORD_TYPE>(GENERATOR_KEYWORD_TYPE.SEEDWORD);
    // prompt
    const [prompt, setPrompt] = useState('');
    // 种子词
    const [querys, setQuerys] = useState<string[]>([]);
    // 只用来处理按钮的disbaled
    const [queryText, setQueryText] = useState('');
    // 文本域错误信息
    const [textAreaErrorMsg, setTextAreaErrorMsg] = useState('');

    const changePrompt = useCallback((value = '') => {
        setPrompt(value);
        const l = getLengthInBytes(value);
        const msg = l < MIN_PROMPT_LENGTH
            ? '为确保关键词生成效果，请至少输入20个字符'
            : l > MAX_PROMPT_LENGTH ? '不能超过1000个字符' : '';
        setTextAreaErrorMsg(msg);
    }, []);

    useEffect(() => {
        if (url) {
            getKeywordPromptByUrl({url}).then(res => {
                !prompt && changePrompt(res);
                res && sendMonitor('click', {
                    field: 'gen_kw_url_prompt',
                    'extra_params': res,
                });
            });
        }
    }, [url]);

    const {spliceRows, fetchBidAndUpdate} = bufferMethods;

    // 推荐的关键词列表相关
    const {
        filters,
        columns,
        handleColumnAction,
        filterMethods,
        getFilterContentByField,
    } = useColumns({
        columnConfiguration: columnConfiguration,
        tableFieldsMap: keywordTableFieldsMap,
    });
    const {deleteFilterByIndex, changeFilterByIndex, resetFilters} = filterMethods;

    // 重置
    const onTabChange = useCallback((value: string) => {
        setPrompt('');
        setTextAreaErrorMsg('');
        setQuerys([]);
        setGeneratorType(value as GENERATOR_KEYWORD_TYPE);
        resetFilters();
    }, [resetFilters]);


    // 推荐的关键词列表
    const tabsProps: TabsProps = {
        activeKey: generatorType,
        onChange: onTabChange,
        bordered: false,
        style: {
            '--dls-tab-menu-padding': '0px',
        } as object,
    };
    const [sorter, onSort] = useTableSort();
    const {transMonitorId} = useKeywordSharedInfo();
    const {
        getRecommendList,
        getRecommendListLoading,
        recommendList: recommendListRaw,

        onDownload,
        downloadLoading,

        suggestWords,
        getSuggestionWords,

        selection,
        selectionOperations,
        recommendType,
        recommendStrategy,
        addFroms,
    } = useKeywordList({
        generatorType,
        querys,
        prompt,
        filters,
        sorter,
        campaignId: levelInfo.campaignId,
        adgroupId: levelInfo.adgroupId,
    });
    const addToBuffer = useCallback((items: Array<Partial<FcKeywordType>>) => {
        let itemsToAdd = items.filter(item => !rows.some(row => row.keyValue === item.keyword));

        if (itemsToAdd.length > MAX_KEYWORD_COUNT) {
            // 如果超限了可以回填前3000个，并做toast提示。
            Toast.error({
                content: `最多添加${MAX_KEYWORD_COUNT}个关键词`,
            });
            itemsToAdd = itemsToAdd.slice(0, MAX_KEYWORD_COUNT);
        }

        const newItems = itemsToAdd.map(item => {
            return generateItem({
                keyword: item.keyword,
                addFrom: keywordAddFromMap[generatorType],
                suggestSource: item.suggestSource,
                recommendStrategy,
            });
        });

        const {changedRowIndexes} = spliceRows({
            spliceParams: [rows.length, 0, ...newItems],
        });

        const keywords = newItems.map(item => item.keyValue);
        sendKeywordAcceptFunnel({
            transMonitorId,
            action: AcceptFunnelItem.acceptBtnClick,
            count: keywords.length,
            recommendType,
            recommendStrategy,
            addFroms,
        });
        fetchBidAndUpdate({indexs: changedRowIndexes, keywords});
    }, [
        rows,
        spliceRows,
        generatorType,
        fetchBidAndUpdate,
        transMonitorId,
        recommendType,
        recommendStrategy,
        addFroms,
    ]);


    const registerDrawer = useCallback(() => {
        return handleColumnAction('addToBuffer', addToBuffer);
    }, [handleColumnAction, addToBuffer]);
    useRegister(registerDrawer);


    useEffect((
    ) => {
        getRecommendList();
    }, [filters, sorter, levelInfo]);

    // 如果rows已有了，那么给data加上disabled字段
    const recommendList = useMemo(() => {
        return recommendListRaw.map(item => {
            return {
                ...item,
                disabled: rows.some(row => row.keyValue === item.keyword),
                generatorType,
            };
        });
    }, [recommendListRaw, rows, generatorType]);

    // 种子词的填写
    const onMultiLabelInputChange = useCallback(e => {
        const newLabels = e.value;
        if (e.errorMsg?.length && !e.errorMessage.includes(';')) {
            return;
        }
        setQuerys(newLabels.map((i: {label: string}) => i.label));

        // 如果是新增querys，则需要把queryText清空
        if (newLabels.length > querys.length) {
            setQueryText('');
        }

    }, [querys]);

    const onAddSeedWord = useCallback((seedWord: string) => {
        if (getLengthInBytes(seedWord) > MAX_SEED_WORD_LENGTH) {
            Toast.error({
                content: `种子词长度不能超过${MAX_SEED_WORD_LENGTH}个字符`,
            });
            return;
        }

        if (querys.length >= MAX_SEED_WORD_COUNT) {
            Toast.error({
                content: `最多添加${MAX_SEED_WORD_COUNT}个种子词`,
            });
            return;
        }

        if (querys.includes(seedWord)) {
            Toast.error({
                content: '已添加该词，请勿重复添加',
            });
            return;
        }

        setQuerys([...querys, seedWord]);
    }, [querys]);

    const labels = useMemo(() => querys.map(label => ({label})), [querys]);

    const [pageNo, setPageNo] = useState(1);
    const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE);

    useEffect(() => {
        setPageNo(1);
    }, [recommendListRaw]);


    const filterListProps = {
        filters,
        deleteFilterByIndex,
        changeFilterByIndex,
        getFilterContentByField,
        className: 'keyword-generator-filter-list',
    };

    const {customColumns, columnConfigs, columnCategories} = columnConfiguration;
    const customFieldsProps = {
        customColumns,
        summary: {},
        defaultColumns,
        columnConfigs,
        columnCategories,
        modConfiguration,
        isShowTransSelect: false,
        resetProps: {
            buttonProps: {
                size: 'small',
                icon: <IconAdjust />,
            },
            isUseMarkFields: false,
            buttonTitle: '',
        },
    };

    const hasSuggestWords = !!suggestWords.length && generatorType === GENERATOR_KEYWORD_TYPE.SEEDWORD;

    // 计算表格应该减去的高度
    const otherContentHeight = 360 // 表格顶部操作区的高度
        + (hasSuggestWords ? 28 : 0) // 搜索的sug词
        + (filters.length ? 52 : 0) // 筛选器
        // + (recommendList?.length ? 38 : 0) // 底部的添加按钮
        + (recommendList.length > pageSize ? 40 : 0) // 分页器
        + 12; // 底部滚动条
    const tableHeightCss = `calc(100vh - ${otherContentHeight}px)`;

    const emptyText = generatorType === GENERATOR_KEYWORD_TYPE.SEEDWORD
        ? '未能帮您找到关键词，请输入种子词重新拓词'
        : '暂未帮您生成关键词，请修改业务描述重新生成';
    const {onSelectChange, selectAll, getSelectedInfo, resetRowSelection} = selectionOperations;
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange, selectAll},
            recommendList,
            {getId: getKey, multiPageSelection: false}
        ),
        [selection, onSelectChange, selectAll, rows],
    );

    const tableProps: TableProps = {
        className: 'generator-table',
        columns: columns as TableProps['columns'],
        dataSource: recommendList,
        pagination: {
            showPageJumper: false, showTotal: false,
            pageNo,
            pageSize,
            size: 'small',
            total: recommendList.length,
            onPageNoChange: e => {
                setPageNo(e.target.value);
            },
            onPageSizeChange(e) {
                setPageSize(e.target.value);
                setPageNo(1);
            },
        },
        style: {
            marginTop: 12,
            borderTopLeftRadius: 6,
            borderTopRightRadius: 6,
            overflowX: 'hidden',
        },
        size: 'small',
        loading: getRecommendListLoading,
        locale: {
            emptyText,
        } as TableProps['locale'],
        scroll: {
            y: tableHeightCss,
        },
        onSortClick: onSort as TableProps['onSortClick'],
        rowSelection: rowSelectionProps,
        rowKey: 'keyword',
    };

    const currentPaginationList = useMemo(() => {
        return recommendList.filter(item => selection.value.includes(item.keyword));
    }, [recommendList, selection.value]);

    const validator = useCallback(value => {
        if (querys.length && querys.includes(value)) {
            return '已添加该词';
        }
        return false;
    }, [querys]);

    const onQueryTextChange = useCallback((e: {value: string}) => {
        setQueryText(e.value);
    }, []);


    const isAllDisabled = recommendList.every(item => item.disabled);
    const isCurrentPageDisabled = currentPaginationList.every(item => item.disabled);

    const isGenerateBtnDisabled = generatorType === GENERATOR_KEYWORD_TYPE.PROMPT
        ? (!prompt || !!textAreaErrorMsg)
        : (!querys.length && !queryText);
    const {selectedCount} = getSelectedInfo();

    return (
        <div className="keyword-generator-left">
            <div className="keyword-generator-left-top">
                <Tabs {...tabsProps}>
                    <Tabs.TabPane
                        tab={GENERATOR_KEYWORD_TYPE_TEXT[GENERATOR_KEYWORD_TYPE.SEEDWORD]}
                        key={GENERATOR_KEYWORD_TYPE.SEEDWORD}
                    >
                        <MultiLabelInput
                            className="seed-word-input"
                            labels={labels}
                            placeholder="请输入种子词后回车确认，多个词以；分隔"
                            withSearchBtn={false}
                            style={{
                                height: 58,
                                borderRadius: 5,
                            }}
                            validater={validator}
                            tagMaxLen={MAX_SEED_WORD_LENGTH}
                            getLength={getLength}
                            maxLabelNum={MAX_SEED_WORD_COUNT}
                            onChange={onMultiLabelInputChange}
                            onInputChange={onQueryTextChange}
                        />
                    </Tabs.TabPane>
                    <Tabs.TabPane
                        tab={GENERATOR_KEYWORD_TYPE_TEXT[GENERATOR_KEYWORD_TYPE.PROMPT]}
                        key={GENERATOR_KEYWORD_TYPE.PROMPT}
                    >
                        <div className="prompt-input" style={{fontSize: 0}}>
                            <TextArea
                                minRows={2}
                                maxRows={2}
                                countMode="cn"
                                value={prompt}
                                maxLen={MAX_PROMPT_LENGTH}
                                minLen={MIN_PROMPT_LENGTH}
                                errorMessage={textAreaErrorMsg}
                                onChange={e => changePrompt(e.value)}
                                placeholder="请描述您本次推广业务的重点信息，如：我要推广的是新能源汽车，这款汽车的类型、品牌和厂商是什么。"
                                style={{
                                    width: '100%',
                                    resize: 'none',
                                }}
                                errorLocation="layer"
                            />
                        </div>
                    </Tabs.TabPane>
                </Tabs>
                <div className="operations">
                    <Tooltip title="自定义列">
                        <div>
                            <CustomFieldsSelector {...customFieldsProps} />
                        </div>
                    </Tooltip>
                    <Tooltip title="下载所有关键词">
                        <div>
                            <Button
                                onClick={() => {
                                    onDownload();
                                    showGuide();
                                    sendMonitor('click', {
                                        field: `gen_kw_${generatorType}_download_all`,
                                    });
                                }}
                                size="small"
                                id="new-kr-download"
                                icon={<IconDownload />}
                                disabled={!recommendList.length}
                                loading={downloadLoading}
                            />
                        </div>
                    </Tooltip>
                    <OneButton
                        type="text-strong"
                        size="small"
                        disabled={isAllDisabled}
                        onClick={() => {
                            sendMonitor('click', {field: `gen_kw_${generatorType}_accept_all`});
                            addToBuffer(recommendList);
                        }}
                    >
                        {isAllDisabled ? '已添加全部' : '添加全部'}{recommendList.length}个关键词
                    </OneButton>
                </div>
            </div>
            <Button
                variant="cta"
                style={{width: '100%', marginTop: 12}}
                onClick={() => {
                    sendMonitor('click', {
                        field: `gen_kw_${generatorType}`,
                        'extra_params': generatorType === GENERATOR_KEYWORD_TYPE.PROMPT ? prompt : querys.join(','),
                    });
                    getRecommendList();
                    getSuggestionWords();
                }}
                loading={getRecommendListLoading}
                disabled={isGenerateBtnDisabled}
            >
                开始{generatorType === GENERATOR_KEYWORD_TYPE.PROMPT ? '生成' : '拓词'}
            </Button>
            {
                hasSuggestWords ? (
                    <div className="seed-recommand-container">
                        相关搜索：{
                            suggestWords.map(item => {
                                return (
                                    <span
                                        className="seed-recommand-word"
                                        key={item.keyword}
                                        onClick={() => onAddSeedWord(item.keyword)}
                                    >
                                        +{item.keyword}
                                    </span>
                                );
                            })
                        }
                    </div>
                ) : null
            }
            <div style={{position: 'relative'}}>
                <FilterList {...filterListProps}>
                    <OneButton type="text-strong" onClick={resetFilters}>清空</OneButton>
                </FilterList>
                {/* 批量条 */}
                {
                    !!selectedCount && (
                        <BatchToolbar
                            className="batch-operation-bar"
                            selectedCount={selectedCount}
                            totalCount={recommendList.length}
                            onSelectAll={selectAll}
                            onClear={resetRowSelection}
                            size="small"
                            tools={(
                                <div className="batch-operations">
                                    <OneButton
                                        size="small"
                                        onClick={() => {
                                            sendMonitor('click', {field: `new_kw_${generatorType}_accept_page`});
                                            addToBuffer(currentPaginationList);
                                            resetRowSelection();
                                        }}
                                        disabled={isCurrentPageDisabled}
                                    >
                                        添加
                                    </OneButton>
                                    <OneButton
                                        size="small"
                                        onClick={() => {
                                            sendMonitor('click', {field: `new_kw_${generatorType}_accept_all`});
                                            addToBuffer(recommendList);
                                            resetRowSelection();
                                        }}
                                        disabled={isAllDisabled}
                                    >
                                        添加全部
                                    </OneButton>
                                </div>
                            )}
                        />
                    )
                }
                <Table {...tableProps} />
            </div>
        </div>
    );
}
