import {useCallback} from 'react';
import {useDebouncedCallback} from 'huse';
import {Toast} from '@baidu/one-ui';
import {useKeyOrientedArray} from '@/hooks/collection/array';
import {fetchKeywordSugBid, preValidateKeywords} from '@/api/campaignOrAdgroupSelector';
import {createError, ErrorFromEnum} from '@/utils/error';
import {KeywordApiItem, MAX_KEYWORD_COUNT, TableKeywordItem} from '../config';
import {generateItem, getKey} from '../util';

interface UseKeywordBufferRowsProps {
    initialValue: TableKeywordItem[];
    campaignId?: number;
    adgroupId?: number;
}
export function useKeywordBufferRows(props: UseKeywordBufferRowsProps) {

    const {initialValue, campaignId, adgroupId} = props;

    // 已选择关键词列表
    const [rows, rowsMethods] = useKeyOrientedArray(initialValue, {getKey});

    // 已选择关键词列表
    const {
        updateAt,
        set,
        splice,
        batchUpdateAt,
        removeItemsByKeys,
        updateItems,
    } = rowsMethods;

    // 预校验
    const preValidate = useCallback(async () => {
        const keywordTypes: Array<Partial<KeywordApiItem>> = [];
        const indexArray: number[] = [];
        for (let i = 0; i < rows.length; i++) {
            const {keyValue: keyword, price, mixMatchType} = rows[i];
            if (keyword) {
                const [matchType, phraseType] = mixMatchType?.split('-');
                const param = {
                    keyword,
                    price,
                    matchType,
                    phraseType,
                    campaignId,
                    adgroupId,
                };
                keywordTypes.push(param);
                indexArray.push(i);
            }
        }
        if (keywordTypes.length) {
            try {
                await preValidateKeywords(keywordTypes);
                // 预校验成功清空所有错误
                set(rows.map(item => ({
                    ...item,
                    error: '',
                })));
            }
            catch (e: any) {
                const error = createError(e, {errorFrom: ErrorFromEnum.FC});
                const errors = error._normalized?.errors || [];

                const errorList: Array<{
                        index: number;
                        errorCode: string;
                        message: string;
                    }> = errors.map((err: {
                        id: number;
                        code: string;
                        message: string;
                    }) => {
                        const {id, code, message} = err;
                        return {
                            index: indexArray[id],
                            errorCode: code,
                            message,
                        };
                    });
                    // 先清空错误 防止部分失败时成功的数据报错不消失
                updateItems(rows.map(i => ({id: i.id, error: ''})) as TableKeywordItem[]);
                // set(rows.map(item => ({
                //     ...item,
                //     error: '',
                // })));
                batchUpdateAt(
                    errorList.map(err => err.index),
                    errorList.map(err => ({
                        errorCode: +err.errorCode,
                        error: err.message,
                    })));

                throw new Error('预校验失败');
            }
        }
    }, [batchUpdateAt, rows, campaignId, adgroupId, set]);


    const fetchBidAndUpdate = useCallback(async ({
        keywords,
        indexs,
    }: {
            keywords: string[];
            indexs: number[];
        }) => {
        try {
            if (!keywords.length) {
                return;
            }
            const bids = await fetchKeywordSugBid(keywords);
            const normalizedBids = bids.map(value => ({
                price: value,
            }));
                // 减少不必要的渲染触发
            if (bids.length > 0) {
                batchUpdateAt(indexs, normalizedBids);
            }
        }
        catch (error) {
            console.error(error);
        }
    }, [batchUpdateAt]);

    const fetchBidAndUpdateDebounce = useDebouncedCallback(fetchBidAndUpdate, 500);

    const spliceRows = useCallback(({
        spliceParams,
    }: {
            spliceParams: [number, number, ...TableKeywordItem[]];
        }) => {
        const [originStartIndex, originDeleteCount, ...itemsToAdd] = spliceParams;

        const {startIndex, deleteCount, items} = getKeywordSpliceParams({
            rows,
            startIndex: originStartIndex,
            deleteCount: originDeleteCount,
            items: itemsToAdd,
        });

        // 如果超出了最大长度，直接提醒
        const newRowCount = rows.length - deleteCount + items.length;
        if (newRowCount > MAX_KEYWORD_COUNT && deleteCount === 0) {
            Toast.error({
                content: `最多添加${MAX_KEYWORD_COUNT}个关键词`,
            });
            return {
                changedRowIndexes: [],
            };
        }


        // 都删了要留个默认值
        if (items.length === 0 && deleteCount === rows.length) {
            items.push(generateItem());
        }

        splice(startIndex, deleteCount, items);

        const changedRowIndexes = items.map((_, index) => startIndex + index);
        return {
            changedRowIndexes,
        };

    }, [splice, rows]);

    // 把错误的index前置到数组前面
    const onToppingError = useCallback(({errorIndexArray}: {errorIndexArray: number[]}) => {
        const reset = rows.filter((_, index) => !errorIndexArray.includes(index));
        const prev = rows.filter((_, index) => errorIndexArray.includes(index));
        set(prev.concat(reset));
    }, [rows, set]);

    const onBatchDelete = useCallback((index: number[]) => {
        removeItemsByKeys(index.map(item => getKey(rows[item])));
    }, [removeItemsByKeys, rows]);

    const updateRow = useCallback(({
        index,
        value,
    }: {
            index: number;
            value: Partial<TableKeywordItem>;
        }) => {
        updateAt(index, value);
    }, [updateAt]);

    const onSelectChange = (index, e) => {
        updateAt(index, {selected: e.target.checked});
    };

    const onCheckAll = e => {
        const selected = typeof e === 'boolean' ? e : e.target.checked;
        const allSelected = rows
            .filter(item => !item.disabled)
            .map(item => ({
                id: item.id,
                selected
            }));
        updateItems(allSelected);
    };

    const onModValueByIndex = (indexs, value) => {
        const newValue = rows.map((item, index) => {
            if (indexs.includes(index)) {
                return {
                    ...item,
                    ...value
                };
            }
            return item;
        });
        set(newValue);
    };

    return {
        rows,
        bufferMethods: {
            updateRow,
            fetchBidAndUpdate,
            preValidate,
            spliceRows,
            onToppingError,
            fetchBidAndUpdateDebounce,
            onSelectChange,
            onCheckAll,
            onModValueByIndex,
            onBatchDelete,
        },
    };
}


export type BufferMethodsType = ReturnType<typeof useKeywordBufferRows>['bufferMethods'];


/**
 * rows 中可能存在 keyValue 为空的行，这种情况在添加时需要特殊处理 splice 的参数
 * 这里只处理添加， 并且只处理最后一行的情况， 如果最后一行是空的，直接替换掉
 */
function getKeywordSpliceParams({
    rows,
    startIndex,
    deleteCount,
    items: itemsToAdd,
}: {
    rows: TableKeywordItem[];
    startIndex: number;
    deleteCount: number;
    items: TableKeywordItem[];
}) {

    // 只处理添加的情况， 不处理替换或者删除的情况
    const shouldBreak = deleteCount || !itemsToAdd.length;

    const lastItem = rows[rows.length - 1];

    // 这里加这个判断是为了让用户可以在最后一行输入新的空行
    const hasNonEmptyAddItem = itemsToAdd.some(item => item.keyValue);

    // 如果最后一行是空的, 并且新增的项有值，直接替换掉
    if (!lastItem?.keyValue && !shouldBreak && hasNonEmptyAddItem) {
        return {
            startIndex: rows.length - 1,
            deleteCount: 1,
            items: itemsToAdd,
        };
    }
    return {
        startIndex,
        deleteCount,
        items: itemsToAdd,
    };
}
