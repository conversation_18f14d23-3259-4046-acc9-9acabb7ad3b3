.keyword-buffer-text-table() {
    .text-table {
        input {
            font-size: 12px;
        }
    }

    .text-table-match-type-container {
        width: 90px;
        height: 44px;
        line-height: 44px;
        position: relative;

        .content {
            position: absolute;
            top: 9px;
            left: 0;
            line-height: 26px;
            padding-left: 12px;
        }
    }

    .text-table-match-type {
        width: 100px;
        height: 44px;
        line-height: 44px;
        position: relative;
    }

    .text-table-price {
        width: 80px;
        height: 44px;
        line-height: 44px;
        position: relative;
    }

    .text-table-price-container {
        width: 60px;
        height: 44px;
        line-height: 44px;
        position: relative;

        .price-content {
            position: absolute;
            top: 9px;
            left: 0;
            line-height: 26px;
            padding-left: 12px;
        }
    }

    .one-ai-ui-pro-text-table-item:hover {
        .select-inline-operation {
            .select-inline-match-type {
                display: inline-block;
            }

            .text-table-number-input {
                display: inline-block;
            }
        }
    }

    .one-ai-ui-pro-text-table-content {
        border: 0;
    }

    .one-ai-ui-pro-text-table-item {
        border: 0;
    }

    .select-inline-operation {
        .one-ai-select:not(.one-ai-select-open) {
            display: none;
        }

        .text-table-number-input {
            display: none;
        }
    }

    .one-ai-ui-pro-text-table-title .one-ai-ui-pro-text-table-item {
        height: 36px;
    }
}

.keyword-generator {
    &-container {
        border: 1px solid var(--Translucent-3, #6692de26);
        border-radius: 10px;
        display: flex;
        max-height: 100%;

        .divider {
            height: 100%;
            width: 1px;
            background-color: var(--Translucent-3, #6692de26);
        }


        .keyword-generator-filter-list {
            margin-top: 12px;
            margin-bottom: 12px;
        }
    }

    &-left {
        padding: 8px 16px 0;
        width: calc(100vw - 568px);

        &-top {
            position: relative;

            .one-ai-tabs-bar {
                margin-bottom: 12px;
            }

            .prompt-input {
                .one-ai-textarea-wrapper {
                    width: 100%;
                }
            }

            .operations {
                display: flex;
                gap: 12px;
                position: absolute;
                right: 0;
                top: 6px;
            }
        }

        .batch-operation-bar {
            top: -48px;
            height: 42px;
            position: absolute;
            width: 100%;
            z-index: 1;
            background: linear-gradient(227deg, #9d6dff 0%, #5f82ff 48.58%, #67a4ff 100%);

            .batch-operations {
                display: flex;

                button + button {
                    margin-left: 8px;
                }
            }
        }

        .generator-table {
            .one-ai-table-variant-normal .one-ai-table-tbody > tr > td {
                border-width: 0;
                padding-top: 0;
                padding-bottom: 0;
                height: 32px;
                line-height: 32px;
            }

            .one-ai-table-variant-normal .one-ai-table-thead > tr > th:first-child {
                padding-left: 16px;
            }

            .one-ai-table-variant-normal .one-ai-table-thead > tr > th {
                padding-top: 0;
                padding-bottom: 0;
                height: 36px;
                line-height: 36px;
            }

            .one-ai-table-variant-normal .one-ai-table-tbody > tr:last-child > td {
                border-width: 1px;
            }

            .one-ai-table-pagination {
                margin-top: 8px;
            }
        }

        .batch-operation-container {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            margin-top: 8px;
            margin-bottom: 8px;

            &-tip {
                font-size: 12px;
                font-weight: 400;
                line-height: 16px;
                color: #848b99;
            }
        }
    }

    &-right {
        padding: 16px 16px 0;
        width: 518px;
        display: flex;
        flex-direction: column;
        height: 100%;

        .keyword-buffer-text-table();

        &-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            &-title {
                font-size: 14px;
                font-weight: 500;
                line-height: 20px;
                color: #0e0f11;
            }

            &-operations {
                display: flex;
                gap: 12px;
                font-size: 0;
            }
        }

        .one-ai-ui-pro-text-table-title {
            .one-ai-ui-pro-text-table-input {
                flex-grow: 0;
                flex-basis: 312px;

                &:hover {
                    border-top-left-radius: 6px;
                }
            }
        }

        .one-ai-ui-pro-text-table-line {
            .one-ai-ui-pro-text-table-data-index {
                width: 40px;
            }

            .one-ai-ui-pro-text-table-input {
                flex-grow: 0;
                flex-basis: 302px;
            }
        }

        &.is-show-price {
            .one-ai-ui-pro-text-table-title {
                .one-ai-ui-pro-text-table-input {
                    flex-grow: 0;
                    flex-basis: 236px;

                    &:hover {
                        border-top-left-radius: 6px;
                    }
                }
            }

            .one-ai-ui-pro-text-table-input {
                flex-grow: 0;
                flex-basis: 224px;
            }
        }
    }

    &-download {
        position: absolute;
        top: 12px;
        right: 0;
    }
}

.kw-footer {
    display: flex;
    gap: 12px;
    height: 84px;
    align-items: center;
}

.seed-word-input {
    width: 100%;

    .one-ai-ui-pro-multiLabel-input-area-count {
        display: none;
    }
}

.seed-recommand-container {
    margin-top: 12px;
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
    color: #545b66;

    .seed-recommand-word {
        cursor: pointer;
        margin-right: 8px;
    }
}

.keyword-batch-editor-title {
    color: #191b1e;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 12px;
}

.keyword-batch-editor-footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.kr-keyword-buffer-enlarge-dialog {
    &-content {
        .keyword-buffer-text-table();

        .one-ai-ui-pro-text-table-title {
            .one-ai-ui-pro-text-table-input {
                flex-grow: 0;
                flex-basis: 892px;

                &:hover {
                    border-top-left-radius: 6px;
                }
            }
        }

        .one-ai-ui-pro-text-table-line {
            .one-ai-ui-pro-text-table-data-index {
                width: 40px;
            }

            .one-ai-ui-pro-text-table-input {
                flex-grow: 0;
                flex-basis: 882px;
            }
        }

        .shrink {
            position: absolute;
            right: 32px;
            top: 25px;
            cursor: pointer;
            font-size: 14px;
            color: #0052cc;

            .shut-down-icon {
                margin-right: 4px;
            }
        }
    }
}

// hack: 把引导的弹窗位置调整到合适的位置，circular平台预览不了我没办法改出弹窗位置了，只能通过hack的方式调整了
#hm-circular [class*="index__step-hideModal"] {
    transform: translateY(120px);

    [class*="index__arrow-inner-right"],
    [class*="index__arrow-inner-left"] {
        transform: translateY(-120px);
    }
}
