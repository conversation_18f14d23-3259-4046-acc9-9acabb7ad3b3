import {Form, NumberInput, Button as OneButton, Popover, ProviderConfig, Dropdown, Dialog} from '@baidu/one-ui';
import {TextTable} from '@baidu/one-ui-pro';
import {useMemo, useCallback} from 'react';
import {IconZoomIn, IconZoomOut} from 'dls-icons-react';
import {getNewMatchTypeDataSource} from 'commonLibs/config/matchType';
import {useBoolean, useElementSize} from 'huse';
import classNames from 'classnames';
import {sendMonitor} from '@/utils/logger';
import {UploadKeywordsButton} from '../uploadKeywords';
import {bufferValidator, MAX_KEYWORD_COUNT, MIX_MATCH_TYPE_ENUM, TableKeywordItem} from './config';
import EditBid from './editBid';
import {generateItem} from './util';
import {BufferMethodsType} from './hooks/useKeywordBufferRows';
import MatchTypeRender from './columns/matchType';

const dataSource = getNewMatchTypeDataSource();

const getTextTableProps = ({
    isShowPrice, spliceRows, updateRow, onToppingError,
    fetchBidAndUpdateDebounce, preValidate,
    fetchBidAndUpdate, onSelectChange, onCheckAll, onModValueByIndex,
    seletedIndex, onBatchDelete: onBatchDeleteFn,
}: {
    isShowPrice: boolean;
    seletedIndex: number[];
} & BufferMethodsType) => {
    const onBatchMatchType = (e: {key: number | string}) => {
        const value = e.key;
        onModValueByIndex(seletedIndex, {mixMatchType: value, error: ''});
        onCheckAll(false);
    };
    const onBatchDelete = () => {
        onBatchDeleteFn(seletedIndex);
        onCheckAll(false);
    };
    return {
        title: '关键词',
        placeholder: '请输入或粘贴',
        selectable: true,
        onSelectChange,
        onCheckAll,
        // selectedRowKeys: [],
        maxLen: 40,
        maxLine: MAX_KEYWORD_COUNT,
        width: 100,
        customColumns: [
            {
                title: '匹配模式',
                className: 'text-table-match-type',
                render: (record: TableKeywordItem, index: number) => {
                    const {keyValue, mixMatchType} = record;
                    return (
                        <MatchTypeRender
                            keyValue={keyValue}
                            mixMatchType={mixMatchType}
                            onModMatchType={({index, value}: {index: number, value: any}) => {
                                updateRow({
                                    index,
                                    value: {
                                        mixMatchType: value,
                                        error: ''
                                    },
                                });
                            }}
                            index={index}
                        />
                    );
                }
            },
            ...(
                isShowPrice ? [{
                    title: '点击出价',
                    className: 'text-table-price',
                    render: (record: TableKeywordItem, index: number) => {

                        if (!record.keyValue) {
                            return null;
                        }

                        return (
                            <EditBid
                                price={record.price}
                                index={index}
                                onModBid={({index, value}) => {
                                    const error = bufferValidator.price(value);
                                    updateRow({
                                        index,
                                        value: {
                                            price: value,
                                            error,
                                        },
                                    });
                                }}
                            />
                        );
                    },
                }] : []
            )
        ],
        onInputChange: (values: string[], options: [
            deleteIndex: number,
            deleteCount: number,
            ...addValues: string[]
        ]) => {
            const [index, count, value] = options;
            const error = bufferValidator.keyword(value);
            updateRow({
                index,
                value: {
                    keyValue: value,
                    mixMatchType: MIX_MATCH_TYPE_ENUM.intelligenceCore,
                    error,
                },
            });
            isShowPrice && fetchBidAndUpdateDebounce({indexs: [index], keywords: [value]});
        },
        // 敲回车的回调，作用是加入新的一行
        onRowChange: async (values: string[], options: [
            deleteIndex: number,
            deleteCount: number,
            ...addValues: string[]
        ]) => {

            const [deleteIndex, deleteCount, ...addValues] = options;
            const indexArray: number[] = [];
            const items = addValues.map((text, i) => {
                indexArray.push(deleteIndex + i);
                const newRows = generateItem({keyword: text});
                return newRows;
            });

            spliceRows({
                spliceParams: [deleteIndex, deleteCount, ...items],
            });

        },
        batchOperation: [
            <Dropdown.Button
                key={'matchType'}
                options={dataSource}
                title="匹配模式"
                size="small"
                onHandleMenuClick={onBatchMatchType}
                split
                dropdownMatchSelectWidth
                buttonType="normal"
            />,
            <OneButton
                key="delete"
                size="small"
                type="normal"
                onClick={onBatchDelete}
            >
                删除
            </OneButton>,
        ],
        // 粘贴
        onPaste: async (
            _: string[], options: [
                deleteIndex: number,
                deleteCount: number,
                ...addValues: string[]
            ]
        ) => {
            const [deleteIndex, deleteCount, ...rows] = options;
            const indexArray: number[] = [];
            const items = rows.map((text: string, i) => {
                indexArray.push(deleteIndex + i);
                return generateItem({
                    keyword: text,
                });
            });
            const {changedRowIndexes} = spliceRows({
                spliceParams: [deleteIndex, deleteCount, ...items],
            });
            const keywords = items.map(item => item.keyValue);
            fetchBidAndUpdate({indexs: changedRowIndexes, keywords});
            sendMonitor('click', {source: 'new_keyword', target: 'on_paste_keywords_to_buffer', count: items.length});
        },
        // 删除
        onDelete: (_: string[], deleteIndex: number[]) => {
            spliceRows({
                spliceParams: [deleteIndex[0], deleteIndex.length],
            });
        },
        // 查看错误
        onToppingError: (
            values: string[],
            errorIndexArray: number[],
        ) => {
            onToppingError({errorIndexArray});
        },
        // 失去焦点
        onBlur: (
            e: React.FocusEvent<HTMLInputElement> & {target: HTMLInputElement & {dataset: {index: string}}},
            hasChanged: boolean) => {
            hasChanged && preValidate();
        },
    };
};


interface KeywordBufferProps {
    rows: TableKeywordItem[];
    bufferMethods: BufferMethodsType;
    campaignId?: number;
    adgroupId?: number;
    isShowPrice: boolean;
}

export function KeywordBuffer({
    bufferMethods,
    rows,
    isShowPrice,
}: KeywordBufferProps) {

    const [ref, size] = useElementSize();
    const textTableHeight = size?.height ? size.height - 37 : 300;

    // 已选择关键词列表
    const {
        preValidate,
        fetchBidAndUpdate,
        fetchBidAndUpdateDebounce,
        spliceRows,
        onToppingError,
        updateRow,
        onSelectChange,
        onBatchDelete,
        onCheckAll,
        onModValueByIndex,
    } = bufferMethods;

    const seletedIndex = useMemo(() => {
        return rows.reduce<number[]>((acc, item, index) => {
            if (item.selected) {
                acc.push(index);
            }
            return acc;
        }, []);
    }, [rows]);

    const textTableProps = useMemo(() => getTextTableProps({
        spliceRows,
        updateRow,
        onToppingError,
        fetchBidAndUpdate,
        fetchBidAndUpdateDebounce,
        preValidate,
        isShowPrice,
        onSelectChange,
        onCheckAll,
        onModValueByIndex,
        seletedIndex,
        onBatchDelete,
    }), [
        updateRow, spliceRows, onToppingError, onSelectChange, onCheckAll,
        fetchBidAndUpdate, preValidate, isShowPrice, fetchBidAndUpdateDebounce,
        onModValueByIndex, seletedIndex, onBatchDelete,
    ]);

    const [batchEditVisible, {on, off}] = useBoolean(false);
    const [keywordBufferVisible, {on: onOpenKeywordBuffer, off: onOffKeywordBuffer}] = useBoolean(false);
    const PopoverContent = (
        <div>
            <div className="keyword-batch-editor-title">统一修改出价</div>
            <ProviderConfig theme="light-ai">
                <Form
                    scrollToFirstError
                    onFinish={values => {
                        // 给所有rows设置price
                        const newRows = rows.map(row => ({
                            ...row,
                            price: values.bid as number,
                        }));
                        spliceRows({
                            spliceParams: [0, rows.length, ...newRows],
                        });
                        off();
                    }}
                >

                    <Form.Field
                        name="bid"
                        rules={[
                            {required: true, message: '请输入出价'},
                            {pattern: /\d+/, message: '请输入数字'},
                        ]}
                    >
                        <NumberInput
                            width={288}
                            placeholder="请输入出价"
                            min={0.01}
                            max={999.99}
                            showErrorMessage={false}
                            suffix="元"
                        />
                    </Form.Field>
                    <div className="keyword-batch-editor-footer">
                        <OneButton
                            type="text"
                            onClick={off}
                        >
                            取消
                        </OneButton>
                        <OneButton
                            type="text-strong"
                            htmlType="submit"
                            style={{marginLeft: 8}}
                        >
                            确认
                        </OneButton>
                    </div>
                </Form>
            </ProviderConfig>
        </div>
    );

    const onClickClearAll = () => {
        spliceRows({
            spliceParams: [0, rows.length, generateItem()],
        });
    };

    const isBatchBtnDisabled = rows.length === 1 && !rows[0].keyValue || !rows.length;

    const cls = classNames('keyword-generator-right', {'is-show-price': isShowPrice});

    const addUploadKeywordsToBuffer = useCallback(
        (keywords: any[] = []) => {
            // let itemsToAdd = keywords.filter(item => !rows.some(row => row.keyValue === item.keyword));
            const newItems = keywords.map(generateItem).map(item => ({...item, isFromUpload: true}));
            spliceRows({spliceParams: [rows.length, 0, ...newItems]});
        },
        [rows, spliceRows]
    );

    return (
        <div className={cls} ref={ref}>
            <div className="keyword-generator-right-top">
                <div>
                    <span className="keyword-generator-right-top-title">
                        已选（{rows.length}/{MAX_KEYWORD_COUNT}）
                    </span>
                    <UploadKeywordsButton
                        addUploadKeywordsToBuffer={addUploadKeywordsToBuffer}
                        isShowPrice={isShowPrice}
                    />
                </div>
                <div className="keyword-generator-right-top-operations" style={{position: 'relative'}}>
                    {
                        isShowPrice && (
                            <Popover content={PopoverContent} placement="bottomRight" visible={batchEditVisible}>
                                <OneButton
                                    disabled={isBatchBtnDisabled}
                                    type="text-strong"
                                    size="small"
                                    onClick={on}
                                >统一修改出价
                                </OneButton>
                            </Popover>
                        )
                    }
                    <OneButton
                        disabled={isBatchBtnDisabled}
                        type="text-strong"
                        size="small"
                        onClick={onClickClearAll}
                    >
                        清空
                    </OneButton>
                    <OneButton
                        icon={IconZoomIn}
                        type="text-strong"
                        size="small"
                        onClick={onOpenKeywordBuffer}
                    >
                        放大
                    </OneButton>
                </div>
            </div>
            {/* 先注释，后面的需求马上还要加上height: textTableHeight */}
            <div style={{overflowY: 'auto'}}>
                <TextTable
                    {...textTableProps}
                    dataSource={rows}
                    className="text-table"
                    contentHeight={textTableHeight - 85}
                />
            </div>
            <Dialog
                title={
                    <div>
                        已选关键词（{rows.length}/{MAX_KEYWORD_COUNT}）
                    </div>
                }
                visible={keywordBufferVisible}
                footer={[]}
                destroyOnClose
                width={1110}
                needCloseIcon={false}
                className="kr-keyword-buffer-enlarge-dialog"
            >
                <div className="kr-keyword-buffer-enlarge-dialog-content">
                    <OneButton
                        icon={IconZoomOut}
                        type="text-strong"
                        size="small"
                        onClick={onOffKeywordBuffer}
                        className="shrink"
                    >缩小
                    </OneButton>
                    <div style={{overflowY: 'auto'}}>
                        <TextTable
                            {...textTableProps}
                            dataSource={rows}
                            className="text-table"
                            contentHeight={textTableHeight - 85}
                        />
                    </div>
                </div>
            </Dialog>
        </div>
    );
}


