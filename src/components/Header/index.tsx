import {useCallback, useMemo, useEffect} from 'react';
import {Nav} from '@baidu/one-ui-pro';
import {Link, ProviderConfig, Tooltip} from '@baidu/one-ui';
import {CCProfile} from '@baidu/cc-nav';
import classNames from 'classnames';
import {IconQuestionCircle, IconLinkAlt, IconSignOut, IconCaretDown} from 'dls-icons-react';
import useUserInfo from 'commonLibs/hooks/useUserInfo';
import {useLocation} from 'react-router-dom';
import {Avatar} from '@baidu/light-ai-react';
import {isHasFeedAuth, PLATFORM_ENUM} from '@/dicts';
import {APP_ID} from '@/utils/config';
import {sendScreenRecord, useAccountInfo} from '@/hooks';
import {useLocalStorage} from '@/hooks/storage';
import {getCurrentTime} from '@/utils/date';
import globalData from '@/utils/globalData';
import {getEnvPort, getUserId, isOnlineStage} from '@/utils';
import Address from '@/components/common/springActivity/address';
import {sendMonitor} from '@/utils/logger';
import {Instructions} from '@/utils/instructions';
import {appendQuery} from '@/utils/route';
import {directToOldPlatform} from '@/utils/link';
import {
    getFcUrl,
    OLD_VERSION_URL_ENUMS, FEED_OLD_VERSION_URL_ENUMS, FEED_OLD_VERSION_DEFAULT_URL, getFeedUrl,
} from '@/utils/format/url';
import {formatString} from '@/utils/string';
import {AdModuleType, PageType} from '@/dicts/pageType';
import AdRoutes, {useAdRoute} from '@/modules/Ad/routes';
import {useGlobalProductContext} from '@/hooks/productLine';
import {useURLQuery} from '@/hooks/tableList/query';
import {OptimizeAdviceSource} from '@/dicts/optimizeAdvice';
import IconLogo from '../../styles/assets/Logo.svg';
import GoBackOldVersionButton from './GoBackOldVersionButton';
import './index.global.less';
import FeedbackPage from './Feedback';
import PlatformSelect from './PlatformSelect';
import {isShowPlatformSelect} from './PlatformSelect/config';
import QuestionnaireAlert from './questionnaireAlert';
import {getFriendLinkConfig, getMenuDataSource} from './config';
import {NavigatorLogType, sendNavigatorLog} from './util';

const Help = ({helpUrl = 'http://yingxiao.baidu.com/home/<USER>/index'}) => (
    <Tooltip
        title="帮助"
        placement="bottom"
        overlayClassName="nav-header-help"
    >
        <a href={helpUrl} target="_blank" className="help-icon" rel="noreferrer">
            <IconQuestionCircle />
        </a>
    </Tooltip>
);

interface IProps {
    instructions?: Instructions;
    mode?: 'normal' | 'liteFormPage';
    onClose?: () => void;
    title?: string;
    showPlatformSwitch?: boolean;
}
const goBackStorageKeys: [string, string, string] = ['go-back', 'memory-user-already-write', 'move'];
// eslint-disable-next-line complexity
export default function Header({instructions, mode = 'normal', onClose, title, showPlatformSwitch}: IProps = {}) {
    const {userName} = useUserInfo();
    const feedBasicInfo = globalData.get('feedBasicInfo');
    const [{optAccountName}] = useAccountInfo();
    const loginUrl = `${location.origin}/login`;
    const casOrigin = isOnlineStage ? 'cas.baidu.com' : 'cas-off.baidu.com';
    const userId = getUserId();
    const isOnline = (location.origin === 'https://qingge.baidu.com' && process.env.STAGE === 'ONLINE');
    const currentLocation = useLocation();
    const {product, setProduct} = useGlobalProductContext();
    const showPlatform = (
        showPlatformSwitch
        && isShowPlatformSelect(currentLocation)
        && isHasFeedAuth()
    );

    const {adModule, adPageType, fromGuiPathname, linkTo, linkToChat} = useAdRoute();
    const [currentQuery] = useURLQuery();

    const navProps = {
        appID: APP_ID,
        userID: userId,
        logo: {
            img: (
                <div className="left-items">
                    {/* {onClose && <IconTimes className="close-icon" onClick={onClose} />} */}
                    <Link
                        // disabled={!!onClose}
                        toUrl={`/ad/overview?userId=${userId}`}
                        onClick={() => {
                            sendMonitor('action', {target: 'click_qingge_icon'});
                        }}
                    >
                        <img src={IconLogo} alt="logo" className="logo-img" />
                    </Link>
                    {title && <div className="title">{title}</div>}
                </div>
            ),
        },
        toolbox: {
            dataSource: [
                ...(isOnline ? [] : [{
                    key: 'environmentTip',
                    customContent: <div className="environment-tip">当前为非正常线上环境</div>,
                }]),
                ...(product === PLATFORM_ENUM.FC ? [{
                    key: 'questionnaire',
                    customContent: <QuestionnaireAlert />,
                }] : []),
                {
                    key: 'address',
                    customContent: <Address />,
                },
                {
                    key: 'help',
                    icon: <Help />,
                },
                ...((product === PLATFORM_ENUM.FEED && feedBasicInfo?.acctAuth?.readdls) ? [] : [{
                    key: 'friendLink',
                    icon: <IconLinkAlt />,
                    children: getFriendLinkConfig(),
                }]),
                ...(showPlatform ? [{
                    key: 'platform',
                    customContent: (
                        <PlatformSelect
                            className="header-platform-select"
                            value={product}
                            onChange={setProduct}
                        />
                    ),
                }] : []),
            ],
        },
        menu: {
            dataSource: getMenuDataSource({product}),
            value: adModule === AdModuleType.PromotionMain ? AdModuleType.ManageCenter : adModule,
            subValue: adPageType,
            onClick: (e: any) => {
                sendNavigatorLog({field: e.target.value, params: NavigatorLogType.Header, info: product});
            },
            onSubListClick: ({item}: {item: any}) => {
                sendNavigatorLog({field: item.key, params: NavigatorLogType.Header, info: product});
            },
        },
    };
    const ccnavProps = {
        ...navProps,
        key: 'nav',
        ...(
            optAccountName
                ? {
                    profile: {
                        customContent: (
                            <div className="user-name-container-wrapper">
                                <CCProfile
                                    {...navProps}
                                    key="nav-profile"
                                    logoutUrl={`http://${casOrigin}?action=logout&u=${loginUrl}`}
                                    onSwitchUser={
                                        (nextUserID: string) => {
                                            const currentURL = window.location.href;
                                            window.location.href = appendQuery(currentURL, {userId: nextUserID});
                                        }
                                    }
                                    dropdownIndependentWidth={false}
                                    renderUserName={() => {
                                        return (
                                            <div className="user-name-container">
                                                <div className="user-name-container-left">
                                                    <Avatar fillStyle="solid" shape="circle" size="small">
                                                        {optAccountName.slice(0, 1)}
                                                    </Avatar>
                                                    <span>{optAccountName}</span>
                                                    <IconCaretDown className="arrow-down-icon" />
                                                </div>
                                            </div>
                                        );
                                    }}
                                />
                                {
                                    userName !== optAccountName && (
                                        <Tooltip
                                            title={userName}
                                            placement="bottom"
                                            onVisibleChange={visible => {
                                                if (visible) {
                                                    sendMonitor('click', {
                                                        target: 'user_name_tooltip',
                                                        params: userName,
                                                        count: userName.length,
                                                    });
                                                }
                                            }}
                                        >
                                            <div className="user-name-container-right">
                                                {userName}
                                            </div>
                                        </Tooltip>
                                    )
                                }
                            </div>

                        ),
                    },
                }
                : {
                    profile: {
                        name: (
                            <Tooltip title="退出登录" placement="bottom">
                                <IconSignOut
                                    style={{marginLeft: 4}}
                                    onClick={() => {
                                        window.location.href = `http://${casOrigin}?action=logout&u=${loginUrl}`;
                                    }}
                                />
                            </Tooltip>
                        ),
                    },
                }
        ),
        className: 'uc-cc-nav',
        type: 'ghost',
    };

    const keyPath = useMemo(() => ['header', '', `sessionStartTime_${userId}`], [userId]);
    const [sessionStartTime, setSessionStartTime, reset] = useLocalStorage(keyPath, null);
    useEffect(
        () => {
            if (!sessionStartTime) {
                setSessionStartTime(getCurrentTime());
            }
            window.addEventListener('beforeunload', reset);
            return () => {
                window.addEventListener('beforeunload', reset);
            };
        },
        [sessionStartTime, setSessionStartTime, reset]
    );

    const goBackOldVersionFc = useCallback(() => {
        sendScreenRecord('go_back_old');
        let templateUrl;
        const pageType = AdRoutes.getItemByRouteFullPath?.(location.pathname)?.name;
        if (location.pathname.split('/')[2] === 'dataCenter') {
            templateUrl = getFcUrl('/fc/datacenter/dashboard/account/user/${userId}');
        } else {
            templateUrl = pageType && pageType in OLD_VERSION_URL_ENUMS
                ? getFcUrl(OLD_VERSION_URL_ENUMS[pageType as keyof typeof OLD_VERSION_URL_ENUMS])
                : getFcUrl('/fc/managecenter/dashboard/overview/user/${userId}');
        }
        const port = getEnvPort();
        const url = formatString(templateUrl, {port, userId: getUserId()});
        sendMonitor('click', {
            target: 'back_to_old_version', info: getCurrentTime(),
            level: pageType, 'extra_params': location.pathname,
            item: sessionStartTime, field: 'fc_go_back_button',
        });
        directToOldPlatform(url);
    }, [sessionStartTime]);

    const goBackOldVersionFeed = useCallback(() => {
        sendScreenRecord('go_back_old');
        const adModuleType = location.pathname.split('/')[2] as AdModuleType;
        const pageType = AdRoutes.getItemByRouteFullPath?.(location.pathname)?.name as PageType;
        const templateUrl = typeof FEED_OLD_VERSION_URL_ENUMS[adModuleType] === 'string'
            ? FEED_OLD_VERSION_URL_ENUMS[adModuleType]
            : FEED_OLD_VERSION_URL_ENUMS[adModuleType]?.[pageType] || FEED_OLD_VERSION_DEFAULT_URL;
        const port = getEnvPort();
        const url = formatString(getFeedUrl(templateUrl), {port, userId: getUserId()});
        sendMonitor('click', {
            target: 'back_to_old_version', info: getCurrentTime(),
            level: pageType, 'extra_params': location.pathname,
            item: sessionStartTime, field: 'feed_go_back_button',
        });
        directToOldPlatform(url);
    }, [sessionStartTime]);

    const cls = classNames('ai-x-header', {[`mode-${mode}`]: mode});
    const {basicInfo} = globalData.get('fcBasicData');

    return (
        <div className={cls}>
            <ProviderConfig theme="light-ai">
                <Nav {...ccnavProps} />
                {
                    basicInfo?.accountInfo?.isJump2qingge || (
                        <GoBackOldVersionButton
                            goBackOldVersionFc={goBackOldVersionFc}
                            goBackOldVersionFeed={goBackOldVersionFeed}
                        />
                    )
                }
            </ProviderConfig>
        </div>
    );
}
