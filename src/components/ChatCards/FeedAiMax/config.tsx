/* eslint-disable complexity */
import {nanoid} from 'nanoid';
import dayjs from 'dayjs';
import {isEmpty} from 'lodash-es';
// @ts-ignore
import {getVisibleRatioTypeOptions} from 'feedCommonLibs/AiMax';
// @ts-ignore
import {ageTypeMap, validateOcpcBidRatio, crowdIdentifyValue} from 'feedCommonLibs/config/bid/ocpcBidRatio';
import {IconQuestionCircle} from 'dls-icons-react';
import {FormConfig, FormFieldConfig} from '@baidu/react-formulator';
import {Link, LinkProps, Tooltip, TimePicker} from '@baidu/one-ui';
import {useFieldValue, useFormContext} from '@baidu/react-formulator';
import {Fragment} from 'react';
import {AllOcpcBidRatioOptions} from '@/components/List/ProjectList/AiAgent/ocpcBidRatio/feed';
import {GroupWithBorder} from '@/components/common/AiMaxGroup';
import {
    FEED_BASE_INITIAL_RATIO_VALUE, getFeedOcpcBidRatioTypeInitialValue,
} from '@/components/List/ProjectList/AiAgent/ocpcBidRatio/util';
import {FeedProjectBudget} from '@/components/Project/FeedEditor/formItems/projectBudget';
import {
    FEED_PROJECT_BUDGET_MAX, getFeedProjectBudgetMin,
} from '@/components/Project/FeedEditor/formItems/projectBudget/config';
import {FeedLiftBudgetStatus, ScheduleModel} from '@/config/feed/liftBudget';
import {FeedOcpcBidRatioTypeEnum} from '@/dicts/ocpcBidRatio';
import {AutoIdeaSwitchEnum, LiftSwitchEnum, ReopenAutoIdeaSwitchEnum, UseProjectBudgetEnum} from '@/dicts/project';
import {getUserId} from '@/utils';
import {getConfigFromDataSource} from '@/utils/type/config';
import globalData from '@/utils/globalData';
import {CommonDivGroup} from '@/components/common/formItems/CommonDivGroup';
import {
    FeedProjectFormData, AILiftModel, FeedAIMaxEduLevel, SmartControlEnum,
    FeedSubjectType,
    FeedAIMaxTradeVersionType,
} from '@/interface/aixProject/feedProject';
import {BJHType} from '@/components/Project/FeedEditor/BJHType/config';
import RadioGroupWithDisabledTip from '@/components/common/formItems/RadioGroupWithDisabledTip';
import {
    getMedecalIndustry, getShortPlayIndustry, getEducationIndustry
} from '@/components/List/ProjectList/AiAgent/util';
import {
    isFeedProjectMainflowUser, isFeedShareBudgetUser,
    isProjectAiLiftUser, isSoftLinkFeedProjectUser, isFeedAiLiftMoreStartTimeUser,
    isFeedProjectMainflowDefaultCrowdUser,
} from '@/utils/getFlag';
import {isFeedSmartControl} from '@/utils/getFeedFlag';
import {AiMaxSwitch, CommonCardGroup, LiftBudgetWeekday, LiftBudgetTimeLabel} from '../FcAiMax/comp';
import {
    CrowdQualityOcpcBid, CustomAgeOcpcBidEditor,
    OcpcBidRatioSubRadio, RegionBidRatioEditor, ScheduleBidRatioEditor,
    SpecifiedAgeOcpcBidEditor, AiMaxFeedSwitch,
} from './comp';
import {LiftBudgetTimeList, MIN_HOUR_INTERVAL} from './comp';
import {AiMaxModeSwitch, IndustryAiAgent} from './edu';
import AutoCreativeStatus from './autoCreative';
import {aiMaxOptionMap, FEEDAiMaxOptionEnum} from './pureConfig';

const NO_LIMIT_BUDGET = 0;

function SmartControlDesc() {
    const {formData} = useFormContext();
    const [initialData] = useFieldValue(formData, 'initialData');
    const isInitialOpen = initialData?.smartControlSwitch;

    const link = `/ad/toolsCenter/autoAdviceRules?userId=${getUserId()}&platform=2`;

    const linkProps: LinkProps = {
        toUrl: link,
        target: '_blank',
        type: 'strong',
    };
    return (
        <div className="ai-max-smart-control-desc">
            开启完成后，可前往<Link {...linkProps}>盯盘助手</Link>查看规则详情。
        </div>
    );
}

export enum FeedAILiftBudgetEnum {
    STOP = 0,
    NORMAL = 1,
    CONSERVATIVE = 2,
}

export * from './pureConfig';


export const AIMAX_SMART_LIFT_BUGET_MAP = {
    [AILiftModel.aggressive]: '积极探索',
    [AILiftModel.conservative]: '稳中求进',
} as const;

export const AIMAX_SMART_LIFT_BUGET_DETAIL = {
    [AILiftModel.aggressive]: '调用30%左右预算进行一键起量',
    [AILiftModel.conservative]: '调用10%左右预算进行一键起量',
} as const;

const highQualityCrowdCoef = '3';
export const eduCrowdCoefConfigs = {
    [FeedAIMaxEduLevel.Aggressive]: {
        [highQualityCrowdCoef]: {
            ratio: '1.1',
            value: highQualityCrowdCoef,
        },
    },
    [FeedAIMaxEduLevel.Conservative]: {
        [highQualityCrowdCoef]: {
            ratio: '1.05',
            value: highQualityCrowdCoef,
        },
    },
};

const ocpcBidRatioModeSource = [
    [1, 'autoOptimization', '自动优选'],
    [2, 'customRules', '自定义规则'],
] as const;

const liftBudgetModeSource = [
    [1, 'autoOptimization', '自动优选'],
    [0, 'customRules', '自定义规则'],
] as const;

export const {
    valueMapByKey: liftBudgetModeRadioMap,
    dataSource: liftBudgetModeOptions,
} = getConfigFromDataSource(liftBudgetModeSource);

function checkIsIncreasingTimes(times: string[]) {
    return times.every((time, index) => {
        return !index || dayjs(time).isAfter(dayjs(times[index - 1]));
    });
}

function checkIsValidTimeInterval(times: string[]) {
    const daysToHoursMap = times.reduce((res, item) => {
        const [date, time] = item.split(' ');
        const [hour] = time.split(':');
        res[date] = res[date] ? [...res[date], +hour] : [+hour];
        return res;
    }, {} as Record<string, number[]>);
    return times.every(item => {
        const [date] = item.split(' ');
        const hours = (daysToHoursMap[date] || []).sort((a, b) => a - b);
        return hours.every((hour, index) => {
            return !index || (hour - hours[index - 1] >= MIN_HOUR_INTERVAL);
        });
    });
}

function validateStartTimeListField(time: string[], formData?: any) {
    const {liftStatus} = formData;
    const needValidateTimeIsAfterCurrent = liftStatus !== FeedLiftBudgetStatus.RUNNING;
    const notBlankTime = time.filter(v => v); // 至少包含日期或者时分秒的起量时间
    const completedTime = notBlankTime.filter(v => { // 包含日期和时分秒的完整时间
        const [date, time] = v.split(' ');
        return date && time;
    });
    const errorMessages = [];
    if (completedTime.length < notBlankTime.length) {
        errorMessages.push('起量时间必须包含日期和时间');
    }
    else if (needValidateTimeIsAfterCurrent && !completedTime.every(v => dayjs(v).isAfter(dayjs()))) {
        errorMessages.push('起量时间必须大于当前时间');
    }

    const isIncreasingTimes = checkIsIncreasingTimes(completedTime);
    const isValidTimeInterval = checkIsValidTimeInterval(completedTime);
    if (!isIncreasingTimes && !isValidTimeInterval) {
        errorMessages.push(`未按照递增顺序添加或生效时间间隔小于${MIN_HOUR_INTERVAL}小时`);
    }
    else if (!isIncreasingTimes) {
        errorMessages.push('生效时间未按照递增顺序添加');
    }
    else if (!isValidTimeInterval) {
        errorMessages.push(`生效时间间隔需大于等于${MIN_HOUR_INTERVAL}小时，或者跨天`);
    }
    return errorMessages.join('；');
}

const getLiftBudgetModeOptionsWithIcon = ({
    disabled,
    aiLift,
    useImmediateLiftBudget,
    liftStatus,
    lift,
    scheduleModelOptions,
}: any) => {
    let disabledTip = '';
    const isRunning = liftStatus === FeedLiftBudgetStatus.RUNNING;
    const useImmediateLiftBudgetValue = useImmediateLiftBudget
        && !scheduleModelOptions.find(item => item.value === ScheduleModel.JUST);
    if (!!aiLift && (lift && isRunning)) {
        disabledTip = '智能起量中，若要使用一键起量功能，请等待智能起量结束后再进行设置。';
    }
    else if (useImmediateLiftBudgetValue) {
        disabledTip = '您已设置启用了一键起量功能，暂时无法规划起量任务，保存项目后将为您即刻生效一键起量，起量结束后您可以配置AI MAX任务。';
    }

    let aiDisabledTip = '';
    if (!aiLift && isRunning) {
        aiDisabledTip = '一键起量生效中，若要使用智能起量功能，请等待一键起量结束后再进行设置。';
    }

    return [
        {
            label: (
                <>
                    自动优选
                    <Tooltip
                        title="智能判断项目缺量导致投放稳定性出现风险时，系统将在短时间内调用预算进行一键起量，
                        为您积极探索目标用户。起量期间项目的转化成本可能稍有涨幅，但拉长周期观察会恢复成本平稳并有助于持续获量。"
                        placement="top"
                    >
                        <IconQuestionCircle style={{marginLeft: '4px'}} />
                    </Tooltip>
                </>
            ),
            value: liftBudgetModeRadioMap.autoOptimization,
            key: liftBudgetModeRadioMap.autoOptimization,
            disabled: disabled || (!aiLift && lift && isRunning),
            disabledTip: aiDisabledTip,
        },
        {
            label: '自定义规则',
            value: liftBudgetModeRadioMap.customRules,
            key: liftBudgetModeRadioMap.customRules,
            disabled: disabled
                || useImmediateLiftBudgetValue
                || (!!aiLift && isRunning),
            disabledTip: disabledTip,
        },
    ];
};


export const {
    valueMapByKey: ocpcBidRatioModeRadioMap,
    dataSource: ocpcBidRatioModeOptions,
} = getConfigFromDataSource(ocpcBidRatioModeSource);

export function getAiMaxFields({
    disabled,
    scheduleModelOptions = [
        {label: '预约生效时段', value: ScheduleModel.SET},
        {label: '每周循环起量', value: ScheduleModel.REPEAT},
    ],
    changeAiMaxTradeVersionTypeNeedValidate = true,
}: {
    disabled: boolean;
    scheduleModelOptions?: Record<string, any>;
    changeAiMaxTradeVersionTypeNeedValidate?: boolean;
}): Array<FormFieldConfig<FeedProjectFormData>> {
    const {trade1} = globalData.get('tradeInfo');
    return [
        {
            field: 'aiMaxTradeVersionType',
            label: null,
            use: [AiMaxModeSwitch, {disabled, changeAiMaxTradeVersionTypeNeedValidate}],
            componentProps: ({subject, bjhType, bidMode}) => {
                return {
                    subject,
                    bjhType,
                    bidMode,
                };
            },
        },
        {
            field: 'aiMaxTradeVersionLevel',
            label: null,
            visible: formData => !!formData.aiMaxTradeVersionType,
            use: [IndustryAiAgent, {disabled: disabled, isAiMax: true}],
            componentProps: props => {
                const {
                    ocpcBidRatioType, initialData, stockSmartLift,
                    subject, bidMode, bjhType, crowdCoefConfigs,
                    ocpcBid, ocpcBidRatioSubType
                } = props;
                return {
                    ocpcBidRatioType,
                    initialData,
                    stockSmartLift,
                    subject,
                    bidMode,
                    bjhType,
                    crowdCoefConfigs,
                    ocpcBid,
                    ocpcBidRatioSubType,
                };
            },
            validators: (value, {ocpcBidRatioType, crowdCoefConfigs}) => {
                const isEdu = getEducationIndustry();
                const originalCrowdCoefConfigs = JSON.parse(JSON.stringify(crowdCoefConfigs));
                if (!isEmpty(originalCrowdCoefConfigs) && isEdu) {
                    return validateOcpcBidRatio({
                        ocpcBidRatioType,
                        crowdCoefConfigs: originalCrowdCoefConfigs,
                    });
                }
            },
        },
        {
            group: 'feedCommonAIMax',
            use: [Fragment],
            visible: formData => !formData.aiMaxTradeVersionType,
            fields: [
                {
                    group: 'feedAILiftSwitch',
                    use: [GroupWithBorder],
                    fields: [
                        {
                            field: 'useLiftBudget',
                            label: null,
                            use: [AiMaxSwitch, {
                                aiMaxOption: FEEDAiMaxOptionEnum.AI_LIFT,
                            }],
                            componentProps: formData => {
                                const {
                                    liftStatus, aiMaxTradeVersionLevel,
                                    useImmediateLiftBudget, subject, bjhType,
                                } = formData;
                                const isMed = getMedecalIndustry()
                                    && aiMaxTradeVersionLevel === FeedAIMaxEduLevel.Aggressive;
                                return {
                                    valueMap: liftStatus === FeedLiftBudgetStatus.FINISHED
                                        ? {
                                            ...aiMaxOptionMap,
                                            [FEEDAiMaxOptionEnum.AI_LIFT]: {
                                                ...aiMaxOptionMap[FEEDAiMaxOptionEnum.AI_LIFT],
                                                desc: '智能起量已完成，如有需要您可以自行修改设置并再次开启。',
                                            },
                                        }
                                        : aiMaxOptionMap,
                                    disabledTip: isMed
                                        ? '当前您启用的行业版档位能力中已包含该任务，无需重复开启'
                                        : (
                                            useImmediateLiftBudget
                                                ? '您已设置启用了一键起量功能，暂时无法规划起量任务'
                                                : ''
                                        ),
                                    disabled: isMed
                                        || (subject === FeedSubjectType.bjh && bjhType !== BJHType.video)
                                        || useImmediateLiftBudget
                                        || disabled,
                                };
                            },
                        },
                        {
                            // 包括【智能起量】和【一键起量】
                            field: 'aiLift',
                            label: null,
                            use: [RadioGroupWithDisabledTip, {
                                style: {'--one-checkbox-strong-min-width': '178px', marginTop: '16px'},
                            }],
                            visible: formData => {
                                return formData.useLiftBudget;
                            },
                            componentProps: formData => {
                                const {useImmediateLiftBudget, liftStatus, aiLift, lift, subject, bjhType} = formData;
                                return {
                                    options: getLiftBudgetModeOptionsWithIcon({
                                        disabled, liftStatus, useImmediateLiftBudget,
                                        aiLift, lift, scheduleModelOptions,
                                    }),
                                    disabled: subject === FeedSubjectType.bjh && bjhType !== BJHType.video,
                                };
                            },
                            validators: value => {
                                if (value === undefined) {
                                    return '请选择智能起量模式';
                                }
                                return '';
                            },
                        },
                        {
                            field: 'aiLiftModel',
                            label: null,
                            use: [CommonCardGroup, {
                                options: [
                                    AILiftModel.aggressive,
                                    AILiftModel.conservative,
                                ].map(value => ({
                                    value,
                                    title: AIMAX_SMART_LIFT_BUGET_MAP[value],
                                    desc: AIMAX_SMART_LIFT_BUGET_DETAIL[value],
                                })),
                            }],
                            rules: [
                                ['required', '请选择智能起量模式'],
                            ],
                            visible: formData => {
                                return formData.aiLift === liftBudgetModeRadioMap.autoOptimization
                                    && formData.useLiftBudget;
                            },
                            componentProps: formData => {
                                const {liftStatus, subject, bjhType} = formData;
                                return {
                                    disabled: disabled
                                        || liftStatus === FeedLiftBudgetStatus.RUNNING
                                        || (subject === FeedSubjectType.bjh && bjhType !== BJHType.video),
                                };
                            },
                        },
                        {
                            field: 'lift.scheduleModel',
                            label: '起量模式',
                            use: ['RadioGroup', {
                                options: scheduleModelOptions,
                                style: {
                                    '--one-checkbox-strong-min-width': '142px',
                                    'width': scheduleModelOptions.length * 142,
                                },
                            }],
                            rules: [
                                ['required', '请选择起量模式'],
                            ],
                            visible: formData => {
                                return formData.aiLift === liftBudgetModeRadioMap.customRules
                                    && formData.useLiftBudget;
                            },
                            componentProps: formData => {
                                const {liftStatus, subject, bjhType} = formData;
                                return {
                                    disabled: disabled
                                        || liftStatus === FeedLiftBudgetStatus.RUNNING
                                        || (subject === FeedSubjectType.bjh && bjhType !== BJHType.video),
                                };
                            },
                        },
                        {
                            field: 'lift.eventWeek',
                            label: '生效时间',
                            use: [LiftBudgetWeekday],
                            validators(v, formData) {
                                if (!v?.length) {
                                    return '请选择每周循环起量时间';
                                }
                                return '';
                            },
                            visible: formData => {
                                return formData.aiLift === liftBudgetModeRadioMap.customRules
                                    && formData.useLiftBudget
                                    && formData.lift.scheduleModel === ScheduleModel.REPEAT;
                            },
                            componentProps: formData => {
                                const {liftStatus, subject, bjhType} = formData;
                                return {
                                    disabled: disabled
                                        || liftStatus === FeedLiftBudgetStatus.RUNNING
                                        || (subject === FeedSubjectType.bjh && bjhType !== BJHType.video),
                                };
                            },
                        },
                        {
                            field: 'lift.eventHour',
                            label: '',
                            use: [TimePicker, {showSecond: false, format: 'HH:00', showMinute: false}],
                            visible: formData => {
                                return formData.aiLift === liftBudgetModeRadioMap.customRules
                                    && formData.useLiftBudget
                                    && formData.lift.scheduleModel === ScheduleModel.REPEAT;
                            },
                            componentProps: ({lift, liftStatus, subject, bjhType}) => {
                                return {
                                    liftBudgetMode: lift.scheduleModel,
                                    disabled: disabled
                                        || liftStatus === FeedLiftBudgetStatus.RUNNING
                                        || (subject === FeedSubjectType.bjh && bjhType !== BJHType.video),
                                };
                            },
                        },
                        {
                            field: 'lift.startTimeList',
                            label: <LiftBudgetTimeLabel />,
                            use: [LiftBudgetTimeList, {disabled: disabled}],
                            visible: formData => {
                                return formData.aiLift === liftBudgetModeRadioMap.customRules
                                    && formData.useLiftBudget
                                    && formData.lift.scheduleModel === ScheduleModel.SET;
                            },
                            componentProps: ({lift, liftStatus, subject, bjhType}) => {
                                return {
                                    liftBudgetMode: lift.scheduleModel,
                                    disabled: disabled
                                        || liftStatus === FeedLiftBudgetStatus.RUNNING
                                        || (subject === FeedSubjectType.bjh && bjhType !== BJHType.video),
                                    isShowOnlyOneStartTime: !isFeedAiLiftMoreStartTimeUser(),
                                };
                            },
                            validators: validateStartTimeListField,
                        },
                        {
                            field: 'lift.liftBudget',
                            label: '起量预算',
                            use: ['NumberInput', {
                                width: 284,
                                suffix: <span>元</span>,
                                fixed: 2,
                            }],
                            rules: [
                                ['required', '请填写起量预算'],
                            ],
                            visible: formData => {
                                return formData.aiLift === liftBudgetModeRadioMap.customRules
                                    && formData.useLiftBudget;
                            },
                            validators(v, formData) {
                                const {budget, deepOcpcBid, ocpcBid} = formData || {};
                                if (deepOcpcBid && v <= deepOcpcBid) {
                                    return '起量预算必须高于出价';
                                }
                                if (ocpcBid && v <= ocpcBid) {
                                    return '起量预算必须高于出价';
                                }
                                if (budget && v > budget) {
                                    return '起量预算不得大于预算值';
                                }
                                return '';
                            },
                            componentProps: formData => {
                                const {liftStatus, subject, bjhType} = formData;
                                return {
                                    disabled: disabled
                                        || liftStatus === FeedLiftBudgetStatus.RUNNING
                                        || (subject === FeedSubjectType.bjh && bjhType !== BJHType.video),
                                };
                            },
                        },
                    ],
                    visible: () => isSoftLinkFeedProjectUser() && isProjectAiLiftUser(),
                },
                {
                    group: 'feedSmartControl',
                    use: [GroupWithBorder],
                    fields: [
                        {
                            field: 'smartControlSwitch',
                            label: null,
                            use: [AiMaxFeedSwitch, {aiMaxType: FEEDAiMaxOptionEnum.SMART_CONTROL}],
                            componentProps: formData => {
                                const {aiMaxTradeVersionLevel} = formData;
                                const isShortPlay = getShortPlayIndustry()
                                    && aiMaxTradeVersionLevel === FeedAIMaxEduLevel.Aggressive;
                                const isMed = getMedecalIndustry() && !!aiMaxTradeVersionLevel;
                                return {
                                    disabledTip: (isMed || isShortPlay)
                                        ? '当前您启用的行业版档位能力中已包含该任务，无需重复开启'
                                        : '',
                                    disabled: isShortPlay || isMed || disabled,
                                };
                            },
                        },
                        {
                            field: 'smartControl',
                            label: null,
                            use: [CommonCardGroup, {
                                disabled,
                                options: [
                                    {
                                        title: '积极探索',
                                        value: SmartControlEnum.aggressive,
                                        desc: '拓量能力强，并且更积极地探索潜力创意',
                                    },
                                    {
                                        title: '稳中求进',
                                        value: SmartControlEnum.conservative,
                                        desc: '自动新建单元时，单次新增单元数少于积极探索档位',
                                    },
                                ],
                                style: {marginTop: 16},
                                prefix: <SmartControlDesc />,
                            }],
                            visible: ({smartControlSwitch}: FeedProjectFormData) => {
                                return !!smartControlSwitch;
                            },
                            componentProps: ({initialData}) => {
                                return {initialData};
                            },
                        },
                    ],
                    visible: isFeedSmartControl,
                },
                {
                    group: 'feedLiftSwitch',
                    use: [GroupWithBorder],
                    fields: [{
                        field: 'liftSwitch',
                        label: null,
                        use: [AiMaxSwitch, {
                            aiMaxOption: FEEDAiMaxOptionEnum.LIFT_SWITCH,
                            disabled,
                            valueMap: aiMaxOptionMap,
                        }],
                    }],
                    visible: isSoftLinkFeedProjectUser,
                },
                {
                    group: 'feedRefined',
                    use: [GroupWithBorder],
                    visible: ({bjhType}) => (isFeedProjectMainflowUser() && bjhType !== BJHType.video),
                    fields: [
                        {
                            field: 'refinedBidSwitch',
                            label: null,
                            use: [AiMaxSwitch, {
                                aiMaxOption: FEEDAiMaxOptionEnum.REFINED_BID,
                                valueMap: aiMaxOptionMap,
                            }],
                            componentProps: formData => ({
                                disabled: disabled || (
                                    formData.subject === FeedSubjectType.bjh && (
                                        !formData.projectFeedId
                                        || (!formData.refinedBidSwitch && formData.projectFeedId)
                                    )
                                ),
                            }),
                        },
                        {
                            field: 'ocpcBidRatioMode',
                            label: null,
                            use: ['RadioGroup', {
                                options: ocpcBidRatioModeOptions.map(i => ({
                                    ...i,
                                    disabled: i.value === ocpcBidRatioModeRadioMap.autoOptimization,
                                })),
                                disabled,
                                style: {'--one-checkbox-strong-min-width': '178px', 'marginTop': '24px'},
                            }],
                            visible: (formData: Record<string, any>) => {
                                return formData.refinedBidSwitch;
                            },
                        },
                        {
                            field: 'ocpcBidRatioType',
                            label: null,
                            use: ['RadioGroup', {
                                disabled,
                                style: {'--one-checkbox-strong-min-width': '87px'},
                            }],
                            visible: (formData: Record<string, any>) => {
                                return formData.ocpcBidRatioMode === ocpcBidRatioModeRadioMap.customRules
                                    && formData.refinedBidSwitch;
                            },
                            componentProps: ({
                                transFrom,
                                transType,
                            }: Partial<FeedProjectFormData>) => {
                                return {
                                    options: getVisibleRatioTypeOptions({
                                        tradeId1st: trade1,
                                        transType,
                                        transFrom,
                                    }, AllOcpcBidRatioOptions),
                                };
                            },
                        },
                        {
                            field: 'ocpcBidRatioSubType',
                            label: null,
                            use: [OcpcBidRatioSubRadio],
                            visible: (formData: Record<string, any>) => {
                                const {ocpcBidRatioType, refinedBidSwitch} = formData;
                                return ![FeedOcpcBidRatioTypeEnum.schedule, FeedOcpcBidRatioTypeEnum.region]
                                    .includes(ocpcBidRatioType) && !!refinedBidSwitch;
                            },
                            componentProps: ({
                                transFrom,
                                transType,
                                ocpcBidRatioType,
                            }: Partial<FeedProjectFormData>) => {
                                return {
                                    options: getVisibleRatioTypeOptions({
                                        tradeId1st: trade1,
                                        transType,
                                        transFrom,
                                    }, AllOcpcBidRatioOptions),
                                    ocpcBidRatioType,
                                };
                            },
                        },
                        {
                            field: 'crowdCoefConfigs',
                            label: null,
                            use: [CrowdQualityOcpcBid, {tradeId1st: trade1}],
                            componentProps: [
                                'ocpcBidRatioSubType', 'ocpcBid', 'aiMaxTradeVersionType', 'aiMaxTradeVersionLevel',
                            ],
                            visible: (formData: Record<string, any>) => {
                                const {ocpcBidRatioType, refinedBidSwitch} = formData;
                                return ocpcBidRatioType === FeedOcpcBidRatioTypeEnum.crowdQualityAll
                                    && !!refinedBidSwitch;
                            },
                            validators: (
                                v: Record<string, any>,
                                {ocpcBidRatioType}: {ocpcBidRatioType: FeedOcpcBidRatioTypeEnum}
                            ) => {
                                return validateOcpcBidRatio({
                                    ocpcBidRatioType,
                                    crowdCoefConfigs: v,
                                });
                            },
                        },
                        {
                            field: 'cycOcpcBidRatio',
                            label: null,
                            use: [ScheduleBidRatioEditor],
                            visible: (formData: Record<string, any>) => {
                                const {ocpcBidRatioType, refinedBidSwitch} = formData;
                                return ocpcBidRatioType === FeedOcpcBidRatioTypeEnum.schedule && !!refinedBidSwitch;
                            },
                            validators: (
                                v: Record<string, any>,
                                {ocpcBidRatioType}: {ocpcBidRatioType: FeedOcpcBidRatioTypeEnum}
                            ) => {
                                return validateOcpcBidRatio({
                                    ocpcBidRatioType,
                                    cycOcpcBidRatio: v,
                                });
                            },
                        },
                        {
                            field: 'customAgeRangeConfig',
                            label: null,
                            use: [CustomAgeOcpcBidEditor],
                            componentProps: ['ocpcBid'],
                            visible: (formData: Record<string, any>) => {
                                const {ocpcBidRatioType, ocpcBidRatioSubType, refinedBidSwitch} = formData;
                                return ocpcBidRatioType === FeedOcpcBidRatioTypeEnum.age
                                    && ocpcBidRatioSubType === ageTypeMap.custom
                                    && !!refinedBidSwitch;
                            },
                            validators: (
                                v: Record<string, any>,
                                {ocpcBidRatioType}: {ocpcBidRatioType: FeedOcpcBidRatioTypeEnum}
                            ) => {
                                return validateOcpcBidRatio({
                                    ocpcBidRatioType,
                                    ageRangeConfig: v,
                                });
                            },
                        },
                        {
                            field: 'specifiedAgeRangeConfig',
                            label: null,
                            use: [SpecifiedAgeOcpcBidEditor],
                            componentProps: ['ocpcBid'],
                            visible: (formatData: Record<string, any>) => {
                                const {ocpcBidRatioType, ocpcBidRatioSubType, refinedBidSwitch} = formatData;
                                return ocpcBidRatioType === FeedOcpcBidRatioTypeEnum.age
                                    && ocpcBidRatioSubType === ageTypeMap.specify
                                    && !!refinedBidSwitch;
                            },
                            validators: (
                                v: Record<string, any>, {ocpcBidRatioType}:
                                    {ocpcBidRatioType: FeedOcpcBidRatioTypeEnum}
                            ) => {
                                return validateOcpcBidRatio({
                                    ocpcBidRatioType,
                                    ageRangeConfig: v,
                                });
                            },
                        },
                        {
                            field: 'regionConfig',
                            label: null,
                            use: [RegionBidRatioEditor],
                            componentProps: ['ocpcBid'],
                            visible: (formatData: Record<string, any>) => {
                                const {ocpcBidRatioType, refinedBidSwitch} = formatData;
                                return ocpcBidRatioType === FeedOcpcBidRatioTypeEnum.region && !!refinedBidSwitch;
                            },
                            validators: (
                                v: Record<string, any>,
                                {ocpcBidRatioType}: {ocpcBidRatioType: FeedOcpcBidRatioTypeEnum}
                            ) => {
                                return validateOcpcBidRatio({
                                    ocpcBidRatioType,
                                    regionConfig: v,
                                });
                            },
                        },
                    ],
                },
                {
                    group: 'feedAiMaxAutoCreativeSwitch',
                    use: [GroupWithBorder],
                    visible: ({bjhType}) => bjhType !== BJHType.video,
                    fields: [
                        {
                            field: 'autoIdeaSwitch',
                            label: null,
                            use: [AiMaxFeedSwitch, {disabled, aiMaxType: FEEDAiMaxOptionEnum.AUTO_CREATIVE}],
                            componentProps: ({initialData}) => {
                                return {
                                    initialData,
                                    disabled,
                                };
                            },
                        },
                        {
                            field: 'reopenAutoIdeaSwitch',
                            label: null,
                            use: [AutoCreativeStatus],
                            visible: ({autoIdeaSwitch}: FeedProjectFormData) => {
                                return !!autoIdeaSwitch;
                            },
                            componentProps: ({initialData}) => {
                                return {initialData};
                            },
                        },
                    ],
                },
            ],
        },
    ];
}

export const getWatch$$AiMax = () => {
    const {trade1} = globalData.get('tradeInfo');
    return {
        aiMaxTradeVersionLevel: (value: FeedAIMaxEduLevel, formData) => {
            if (value && value !== FeedAIMaxEduLevel.NoUse) {
                formData.liftSwitch = LiftSwitchEnum.ON;
                if (formData?.bjhType !== BJHType.video) {
                    formData.autoIdeaSwitch = AutoIdeaSwitchEnum.ON;
                }
                const {
                    ocpcBidRatioType,
                } = formData.initialData;
                if (isFeedProjectMainflowDefaultCrowdUser()
                    && (!ocpcBidRatioType || ocpcBidRatioType === FeedOcpcBidRatioTypeEnum.noUse)
                    && (value !== FeedAIMaxEduLevel.Expansion)
                    && formData.subject !== FeedSubjectType.bjh
                ) {
                    formData.refinedBidSwitch = true;
                    formData.ocpcBidRatioMode = 2;
                    formData.ocpcBidRatioType = FeedOcpcBidRatioTypeEnum.crowdQualityAll;
                    formData.ocpcBidRatioSubType = crowdIdentifyValue.default;
                    formData.crowdCoefConfigs = eduCrowdCoefConfigs[value];
                }

                if (getShortPlayIndustry()) {
                    formData.smartControl = SmartControlEnum.noop;
                    formData.smartControlSwitch = false;
                }

                if (getMedecalIndustry()) {
                    if (value === FeedAIMaxEduLevel.Aggressive) {
                        formData.useLiftBudget = false;
                        formData.smartControl = SmartControlEnum.aggressive;
                    }
                    else {
                        formData.smartControl = SmartControlEnum.conservative;
                    }

                    // formData.ocpcBidRatioMode = ocpcBidRatioModeRadioMap.customRules;
                    // formData.lift = {
                    //     scheduleModel: ScheduleModel.JUST,
                    //     eventWeek: [],
                    //     eventHour: '',
                    //     startTime: '',
                    //     startTime1: '',
                    //     startTime2: '',
                    //     startTime3: '',
                    //     startTimeList: [],
                    //     liftBudget: null,
                    // };
                }
            }
        },
        'lift.scheduleModel': (value: ScheduleModel, formData) => {
            if (value !== ScheduleModel.JUST) {
                formData.useImmediateLiftBudget = false;
            }
        },
        refinedBidSwitch: (value: boolean, formData: Record<string, any>) => {
            if (value) {
                const {transType, transFrom} = formData;
                formData.ocpcBidRatioMode = ocpcBidRatioModeRadioMap.customRules;
                const fieldsInitialObj = getFeedOcpcBidRatioTypeInitialValue({
                    transFrom,
                    transType,
                }) as Record<string, any>;
                Object.keys(fieldsInitialObj).forEach(key => {
                    formData[key] = fieldsInitialObj[key];
                });
            } else {
                formData.ocpcBidRatioType = FeedOcpcBidRatioTypeEnum.noUse;
            }
        },
        ocpcBidRatioType: (value: FeedOcpcBidRatioTypeEnum, formData: Record<string, any>) => {
            if (value && value !== FeedOcpcBidRatioTypeEnum.noUse) {
                const {transType, transFrom} = formData;
                const visibleOptions = getVisibleRatioTypeOptions({
                    tradeId1st: trade1,
                    transType,
                    transFrom,
                }, AllOcpcBidRatioOptions);
                const selectedOption = visibleOptions.find((option: Record<string, any>) => option.value === value);
                Object.keys(FEED_BASE_INITIAL_RATIO_VALUE).forEach(key => {
                    formData[key] = FEED_BASE_INITIAL_RATIO_VALUE[key];
                });
                formData.ocpcBidRatioSubType = selectedOption.children ? selectedOption.children[0].value : null;
            }
        },
        ocpcBidRatioSubType: (value: string | number, formData: Record<string, any>) => {
            if (formData.ocpcBidRatioType === FeedOcpcBidRatioTypeEnum.age) {
                formData.specifiedAgeRangeConfig = [];
                formData.customAgeRangeConfig = [{start: 0, end: 0, value: '', id: nanoid()}];
            } else {
                formData.crowdCoefConfigs = {};
            }
        },
        smartControlSwitch: (value: string | number | boolean, formData: Record<string, any>) => {
            if (value) {
                formData.smartControl = SmartControlEnum.aggressive;
            }
            else {
                formData.smartControl = SmartControlEnum.noop;
            }
        },
        aiMaxTradeVersionType: (value: FeedAIMaxTradeVersionType, formData: Record<string, any>) => {
            if (value === FeedAIMaxTradeVersionType.OFF && formData?.bjhType !== BJHType.video) {
                formData.autoIdeaSwitch = AutoIdeaSwitchEnum.ON;
            } else {
                formData.autoIdeaSwitch = AutoIdeaSwitchEnum.OFF;
            }
        },
        autoIdeaSwitch: (value: AutoIdeaSwitchEnum, formData: Record<string, any>) => {
            const initialData = formData.initialData;
            // 判断是否需要记录aimax自动创意开启时间
            if (value === AutoIdeaSwitchEnum.ON && initialData.autoIdeaSwitch !== value) {
                formData.trigerAutoIdeaSwitch = true;
            } else {
                formData.trigerAutoIdeaSwitch = false;
            }
        },
        reopenAutoIdeaSwitch: (value: ReopenAutoIdeaSwitchEnum, formData: Record<string, any>) => {
            if (value === ReopenAutoIdeaSwitchEnum.ON) {
                formData.trigerAutoIdeaSwitch = true;
            } else {
                formData.trigerAutoIdeaSwitch = false;
            }
        },

    };
};

export function getConfig({
    disabled = false,
    fromAimaxCard = false,
    isShowBudget,
}: {
    disabled?: boolean;
    fromAimaxCard?: boolean;
    isShowBudget?: boolean;
}): FormConfig<FeedProjectFormData> {
    return {
        fields: [
            {
                field: 'budget',
                label: '项目预算',
                use: [FeedProjectBudget],
                componentProps: ({projectFeedId, isUseProjectBudget, aixCampaignItems}) => {
                    return {
                        projectFeedId,
                        isUseProjectBudget,
                        width: '388px',
                        aixCampaignItems,
                    };
                },
                visible: () => {
                    return isFeedShareBudgetUser() && !!isShowBudget && !fromAimaxCard;
                },
                validators: (value, formData) => {
                    // !TODO 目前validators实现有隐性bug，多个同名field的validators只有一个会生效，暂时通过使用完全相同的validators规避
                    const {
                        isUseProjectBudget, deepOcpcBid, ocpcBid,
                        useLiftBudget, useImmediateLiftBudget,
                    } = formData;

                    if ((useLiftBudget || useImmediateLiftBudget)
                        && isUseProjectBudget === UseProjectBudgetEnum.OFF
                    ) {
                        return '如果您要使用智能起量AIMax能力，则需要设置项目预算';
                    }
                    if (isUseProjectBudget === UseProjectBudgetEnum.ON && !value && value !== NO_LIMIT_BUDGET) {
                        return '请输入项目预算';
                    }
                    const min = getFeedProjectBudgetMin();
                    if (
                        typeof value === 'number'
                        && value !== NO_LIMIT_BUDGET
                        && (value < min || value > FEED_PROJECT_BUDGET_MAX)
                    ) {
                        return `范围为${min}~${FEED_PROJECT_BUDGET_MAX}`;
                    }
                    if (value !== NO_LIMIT_BUDGET) {
                        if (deepOcpcBid && value < deepOcpcBid) {
                            return '您的预算要大于等于深度目标转化成本';
                        }
                        if (ocpcBid && value < ocpcBid) {
                            return '您的预算要大于等于目标转化成本';
                        }
                    }
                    // 当 isUseProjectBudget 关闭时， 不需要校验
                },
            },
            {
                group: 'feedAiMax',
                use: [
                    CommonDivGroup, {className: 'feed-aimax-group'},
                ],
                fields: getAiMaxFields({
                    disabled,
                }),
            },
        ],
        watch: getWatch$$AiMax(),
    };
}
