import {FormConfig, useFieldValue, useFormContext, FormFieldConfig, FieldConfig} from '@baidu/react-formulator';
import {regionPriceMapFactory} from 'commonLibs/utils/handleRegion';
import {getIsSupportThirdRegion} from 'commonLibs/region';
import {isUndefined} from 'lodash-es';
import {Checkbox, Input} from '@baidu/one-ui';
import {TextLine} from '@baidu/one-ui-pro';
import {useCallback, useMemo} from 'react';
import {
    getDefaultPriceFactorsFromSchedule,
    getFormateValuesFromBackend,
    getBackendDataFromUI,
    getAllDaySelected,
} from 'manageCenter/schedule/utils';
import {isBatchRefreshUser} from '@/utils/getFlag';
import {TransType} from '@/config/transType';
import MultiLabelInput from '@/components/common/formItems/MultiLabelInput';
import RadioGroupWithDisabledTip from '@/components/common/formItems/RadioGroupWithDisabledTip';
import {FcBidTypeEnum, FcBidTypeOptionsForAiBuild, FcOcpcBidTypeEnum, TransManagerModeType} from '@/dicts/ocpc';
import {TransTypesWithBoundary} from '@/components/Project/FcEditor/transEditor/TransTypes';
import {CampaignBidLimit, CampaignBudgetLimit, CampaignOcpcBidLimit} from '@/components/newFcAd/campaign/dict';
import {FINAL_URL_LIMIT} from '@/dicts/finalUrl';
import {validateFcUrl} from '@/api/fcThreeLevel';
import {FcIdType} from '@/dicts/idType';
import {FC_MARKET_TARGET} from '@/dicts/marketingTarget';
import {FcRegionTargetTypeEnum} from '@/interface/campaign';
import {REGION_TYPE} from '@/dicts/region';
import {RegionValue} from '@/components/common/regionEditor/types';
import {FcLocationOptionsEnum} from '@/dicts/getLocations';
import RegionWithBoundary from '@/components/common/regionEditor';
import {getIsOcpcCampaign} from '@/components/common/regionEditor/util';
import WithDrawerInput from '@/components/common/formItems/WithDrawerInput';
import {SchedulePriceFactor} from '@/interface/fcThreeAdForm/campaign';
import {
    scheduleFieldConfig,
} from '@/modules/Ad/ManageCenter/campaignList/FC/tableList/editor/schedule/inlineEditor';
import {parseScheduleToNL, Schedule} from '@/utils/schedule';
import {
    formatScheduleToBackend, initialValue, ScheduleValue,
} from '@/modules/Ad/ManageCenter/campaignList/FC/tableList/editor/schedule/utils';
import {ReadOnlyText} from '@/components/common/formItems/text';
import FinalUrl from '@/components/FinalUrl';
import {getRegionText} from '../FcTargetCrowd/config';
import './index.global.less';

const INPUT_WIDTH = 376;

const filterEmptyStrings = (value: string[]): string[] => {
    return value.map(item => item.trim()).filter(item => item.length > 0);
};

const commonTextLineProps = {
    maxLine: 10,
    minLine: 1,
    maxLen: 40,
};

const commonTextLineValidators = (config?: Partial<typeof commonTextLineProps>) => (value: string[]) => {
    const {
        maxLen,
        minLine,
        maxLine,
    } = {
        ...commonTextLineProps,
        ...config,
    };

    const finialValue = filterEmptyStrings(value);

    if (finialValue.find(item => item.length > maxLen)) {
        return `每项的长度不能超过${maxLen}个字`;
    }
    if (finialValue.length < minLine) {
        return `请至少输入${minLine}个`;
    }
    if (finialValue.length > maxLine) {
        return `最多输入${maxLine}个`;
    }
};


export enum AiBuildType {
    /* 普通搭建 */
    NORMAL = 0,
    /* 品牌计划搭建 */
    BRAND = 1,
    /* 医疗内容页搭建 */
    MEDICAL = 2,
    /* 课效通搭建 */
    COURSE = 3,
    /* 账户翻修搭建 */
    REFRESH = 4,
    /* 极简投项目搭建 */
    LITE = 5,
}

export interface AIBuildParams {
    buildType: AiBuildType;
    /** 营销目标id */
    marketingTargetId: FC_MARKET_TARGET;
    /** 推广业务 */
    business: string[];
    /** 业务优势 */
    businessAdvantages: string[];
    /** 移动落地页 */
    mobileUrls: string[];
    /** 计算机落地页 */
    pcUrls: string[];
    /** 推广关键词 */
    keywords: string[];
    /** 排除业务 */
    avoidBusiness: string[];
    /** 日预算 */
    budget: number;
    /** 出价类型：0-点击，1-转化 */
    bidType: FcBidTypeEnum;
    /** OCPC出价类型：0-cpc,1-ocpc */
    ocpcBidType?: FcOcpcBidTypeEnum;
    /** 优化目标 */
    transTypes?: TransType[];
    /** 转化出价 */
    ocpcBid?: number;
    /** 点击出价 */
    campaignBid?: number;
    /** 地域 */
    regionTarget: number[];
    /** 地域类型 */
    regionTargetType: FcRegionTargetTypeEnum;
    /** 地域系数 */
    regionPriceFactor: Array<{
        regionId: number;
        priceFactor: number;
    }>;
    /** 地理位置状态 */
    geoLocationStatus: FcLocationOptionsEnum;
    /** 品牌名称 */
    brandName?: string;
    /** 用于生成关键词 */
    useBrand2Keywords?: boolean;
    /** 用于生成创意文案 */
    useBrand2CreativeText?: boolean;
    /** 用于生成高级样式 */
    useBrand2Danube?: boolean;

    schedulePriceFactors: SchedulePriceFactor[];
    schedule: Schedule;
    keyInformationId?: number; // 关键信息ID（一期暂无该字段）
    images: Array<{
        imageId: number;
        picUrl: string; // 设置图库返回的molaUrl
        rawPicUrl: string; // 设置图库返回的url
        desc: string; // 固定值
        width: number;
        height: number;
    }>;
}

export function formatBackendToFormData(data: AIBuildParams) {
    const {
        keyInformationId,
        business = [],
        businessAdvantages = [],
        mobileUrls = [],
        pcUrls = [],
        keywords = [],
        avoidBusiness = [],
        budget,
        bidType = FcBidTypeEnum.CPC,
        ocpcBidType,
        transTypes = [],
        ocpcBid,
        images,
        marketingTargetId = FC_MARKET_TARGET.CPQL,
        campaignBid,
        regionTarget = [],
        regionPriceFactor = [],
        geoLocationStatus = FcLocationOptionsEnum.all,
        brandName,
        useBrand2Keywords = true,
        useBrand2CreativeText = true,
        useBrand2Danube = true,
        buildType,
        schedulePriceFactors = [],
        schedule = [],
        ...restData
    } = data;


    const region: RegionValue = {
        regionMainType: regionTarget.length > 0
            ? REGION_TYPE.CAMPAIGN_REGION
            : REGION_TYPE.ACCOUNT_REGION,
        regionTarget,
        regionPriceFactor,
        regionMap: regionPriceMapFactory({
            regionTarget,
            regionPriceFactor,
            isSupportThirdRegion: getIsSupportThirdRegion({marketingTargetId}),
        }),
        geoLocationStatus,
    };

    const showBasic = getIsOcpcCampaign({
        campaignBidType: bidType,
        campaignOcpcBidType: bidType === FcBidTypeEnum.OCPC ? FcOcpcBidTypeEnum.OCPC : FcOcpcBidTypeEnum.CPC,
    });

    const formattedSchedulePriceFactors = (!schedulePriceFactors || schedulePriceFactors.length === 0)
        ? getDefaultPriceFactorsFromSchedule(
            schedule
        )
        : schedulePriceFactors;

    const scheduleValue = {
        schedule: {
            ...initialValue,
            scheduleValue: getFormateValuesFromBackend(formattedSchedulePriceFactors, showBasic),
        } as ScheduleValue,
        showBasic,
        showTemplate: true,
    };


    return {
        buildType,
        keyInformationId,
        mobileUrl: mobileUrls[0],
        pcUrl: pcUrls[0],
        businessAdvantages: businessAdvantages[0],
        budget,
        budgetMock: undefined, // 无实际作用， 让ts不报错
        bidType,
        ocpcBidType,
        transTypes,
        ocpcBid: ocpcBid === 0 ? undefined : ocpcBid,
        images,
        marketingTargetId,
        campaignBid,
        keywords,
        business,
        avoidBusiness,
        disabled: false,
        region,
        restData,
        scheduleValue,
        brandInfo: {
            brandName,
            useBrand2Keywords,
            useBrand2CreativeText,
            useBrand2Danube,
        },
    };
}

export type AIBuildFormData = ReturnType<typeof formatBackendToFormData>;

export function formatFormDataToBackend(data: AIBuildFormData): AIBuildParams {
    const {
        keyInformationId,
        business = [],
        businessAdvantages,
        mobileUrl,
        pcUrl,
        keywords = [],
        avoidBusiness = [],
        budget,
        bidType,
        ocpcBidType,
        transTypes = [],
        ocpcBid,
        images = [],
        marketingTargetId,
        campaignBid,
        region,
        restData,
        scheduleValue,
        buildType,
        brandInfo: {
            brandName,
            useBrand2Keywords,
            useBrand2CreativeText,
            useBrand2Danube,
        },
    } = data;


    const isOcpc = bidType === FcBidTypeEnum.OCPC;
    const showBasic = getIsOcpcCampaign({
        campaignBidType: bidType,
        campaignOcpcBidType: bidType === FcBidTypeEnum.OCPC ? FcOcpcBidTypeEnum.OCPC : FcOcpcBidTypeEnum.CPC,
    });
    const {
        schedule,
        schedulePriceFactors,
    } = getBackendDataFromUI(scheduleValue.schedule.scheduleValue, showBasic) as {
        schedule: Schedule;
        schedulePriceFactors: SchedulePriceFactor[];
    };


    return {
        ...restData,
        schedule,
        schedulePriceFactors,
        buildType,
        keyInformationId,
        business: filterEmptyStrings(business),
        businessAdvantages: [businessAdvantages],
        mobileUrls: mobileUrl ? [mobileUrl] : [],
        pcUrls: pcUrl ? [pcUrl] : [],
        keywords: filterEmptyStrings(keywords),
        avoidBusiness: filterEmptyStrings(avoidBusiness),
        budget,
        bidType,
        images,
        marketingTargetId,
        // 这里暂时根据bidType来判断ocpcBidType，后续如果有的话需要展示ocpcBidType表单来选择
        ...(
            isOcpc ? {
                ocpcBidType: FcOcpcBidTypeEnum.OCPC,
                transTypes,
                ocpcBid,
            } : {
                campaignBid,
            }
        ),
        regionTarget: region.regionTarget,
        regionTargetType: region.regionMainType === REGION_TYPE.ACCOUNT_REGION
            ? FcRegionTargetTypeEnum.ACCOUNT
            : FcRegionTargetTypeEnum.CUSTOM,
        regionPriceFactor: region.regionPriceFactor,
        geoLocationStatus: region.geoLocationStatus,
        brandName,
        useBrand2Keywords: Boolean(brandName && useBrand2Keywords),
        useBrand2CreativeText: Boolean(brandName && useBrand2CreativeText),
        useBrand2Danube: Boolean(brandName && useBrand2Danube),
    };
}

const BRAND_NAME_USAGE_LIST = [{
    label: '品牌用于生成关键词',
    value: 'useBrand2Keywords',
},
{
    label: '品牌用于生成创意文案',
    value: 'useBrand2CreativeText',
},
{
    label: '品牌用于生成高级样式',
    value: 'useBrand2Danube',
}] as const;

const BRAND_NAMES = Object.values(BRAND_NAME_USAGE_LIST).map(usage => usage.value);

function BrandName({value: brandInfo, onChange}: {
    value: AIBuildFormData['brandInfo'];
    onChange: (value: AIBuildFormData['brandInfo']) => void;
}) {
    const usageValue = BRAND_NAMES.filter(i => !!brandInfo[i]);


    const setBrandNameUsage = useCallback(usages => {
        const newBrandInfo = BRAND_NAME_USAGE_LIST.reduce((acc, {value}) => {
            acc[value] = usages.includes(value);
            return acc;
        }, {brandName: brandInfo.brandName} as AIBuildFormData['brandInfo']);
        onChange(newBrandInfo);
    }, [brandInfo.brandName]);

    const setBrandName = useCallback(brandName => {
        onChange({
            ...brandInfo,
            brandName,
        });
    }, [brandInfo]);

    return (
        <div>
            <Input
                width={INPUT_WIDTH}
                value={brandInfo.brandName}
                onChange={e => setBrandName(e.target.value)}
                placeholder="请输入品牌名称"
            />
            <Checkbox.Group
                className="brand-name-usage-list"
                options={BRAND_NAME_USAGE_LIST as any}
                size="small"
                onChange={setBrandNameUsage}
                value={usageValue}
            />
        </div>
    );
}

function BudgetLabel() {
    const {formData} = useFormContext();
    const [bidType] = useFieldValue<FcBidTypeEnum>(formData, 'bidType');
    return (
        <span>
            {
                bidType === FcBidTypeEnum.OCPC
                    ? '项目预算'
                    : '方案预算'
            }
        </span>
    );
}


const regionCommonFieldConfig: Omit<FieldConfig<{
    region: RegionValue;
}>, 'use'> = {
    field: 'region',
    label: (
        <>
            地域 <span style={{color: '#848B99', fontSize: 12}}>（指定具体投放地域后，系统会自动过滤非投放地域关键词）</span>
        </>
    ),

    componentProps: ({marketingTargetId, bidType, region, disabled}: {
        marketingTargetId: number;
        bidType: FcBidTypeEnum;
        region: RegionValue;
        disabled: boolean;
    }) => ({
        isShowRegionBag: false,
        isSupportThirdRegion: getIsSupportThirdRegion({marketingTargetId}),
        isShowPriceFactors: !getIsOcpcCampaign({
            campaignBidType: bidType,
            campaignOcpcBidType: bidType === FcBidTypeEnum.OCPC ? FcOcpcBidTypeEnum.OCPC : FcOcpcBidTypeEnum.CPC,
        }),
        disabled,
        initialData: {
            region,
            bidType,
            marketingTargetId,
        },
    }),
    validators: (value: RegionValue) => {
        const {
            regionTarget, regionMainType, geoLocationStatus,
            regionPriceFactor,
        } = value;
        if (isUndefined(geoLocationStatus)) {
            return '请选择地域意图';
        }
        if (!regionTarget.length && regionMainType !== REGION_TYPE.ACCOUNT_REGION) {
            return '请选择地域';
        }
        if (regionPriceFactor?.length && regionPriceFactor.some((i: {
            regionId: number; priceFactor: number | string;
        }) => !i.priceFactor)) {
            return '请输入地域出价系数';
        }
    },
};

function WrappedTextLine(props: {
    onChange: (value: object) => void;
}) {
    const {
        onChange,
    } = props;

    const textLineProps = {
        ...props,
        className: 'ai-build-form-textline',
        style: {
            height: window.innerHeight - 200,
        },
        onChange: (e: {value: string[]}) => {
            onChange(e.value);
        },
    };
    return (
        <TextLine {...textLineProps} />
    );
}

function getInputText({value, showTextNum}: {
    value: string[];
    showTextNum: number;
}) {
    const finialValue = filterEmptyStrings(value);
    if (!finialValue.length) {
        return '';
    }
    else if (finialValue.length < showTextNum) {
        return finialValue.join('、');
    }
    return `${finialValue.slice(0, showTextNum).join('、')}等${finialValue.length}个`;
}

const fields: Array<FormFieldConfig<AIBuildFormData>> = [
    {
        field: 'business',
        rules: [['required', '请选择经营业务']],
        label: '经营业务',
        use: [WithDrawerInput, {
            formConfig: {
                fields: [{
                    field: 'business',
                    label: null,
                    use: [WrappedTextLine, {
                        ...commonTextLineProps,
                        title: '经营业务',
                        placeholder: '请输入经营业务',
                    }],
                    validators: commonTextLineValidators(),
                }],
            },
            transForm: (value: string[]) => {
                return getInputText({
                    value,
                    showTextNum: 3,
                });
            },
            readOnlyTextProps: {
                width: INPUT_WIDTH,
                btnVisible: true,
                placeholder: '请输入经营业务',
            },
            drawerProps: {
                width: 800,
                title: '修改推广业务',
            },
        }],
        validators: commonTextLineValidators(),
        componentProps: ({disabled, business}) => ({
            disabled,
            initialData: {business},
        }),
        transform: {
            parse: ({business}) => business,
        },
    },
    {
        field: 'businessAdvantages',
        rules: [['required', '请输入产品/服务优势']],
        label: '产品/服务优势',
        use: ['TextArea', {
            placeholder: '请输入产品/服务优势',
            minRows: 2,
            maxRows: 2,
            width: INPUT_WIDTH,
            originTextAreaProps: {
                // 禁止拖拽
                resize: 'none',
            },
        }],
        validators: [
            {
                validator: value => {
                    if (!value || value.trim().length === 0) {
                        return '请输入产品/服务优势';
                    }
                },
            },
        ],
        componentProps: ({disabled}) => ({disabled}),
    },
    {
        field: 'mobileUrl',
        rules: [['legacyIsURL'], ['textRange', [0, FINAL_URL_LIMIT.max]]],
        label: '移动端落地页',
        use: [FinalUrl, {placeholder: '请输入移动落地页URL', width: INPUT_WIDTH, showType: 'mobile'}],
        validators: [
            {
                validator: (value, formData) => {
                    if (!formData.pcUrl && !value) {
                        return '请至少输入一个落地页';
                    }
                },
            },
            {
                validator: (value, formData) => validateFcUrl({
                    url: value,
                    field: 'mobileFinalUrl',
                    idType: FcIdType.UNIT_LEVEL,
                    marketingTargetId: formData.marketingTargetId,
                }),
                triggers: ['onBlur', '@final'],
            },
        ],
        componentProps: ({disabled, buildType, marketingTargetId}) => ({
            marketingTargetId: marketingTargetId,
            disabled: disabled || (!isBatchRefreshUser() && buildType === AiBuildType.REFRESH),
        }),
    },
    {
        field: 'pcUrl',
        rules: [['legacyIsURL'], ['textRange', [0, FINAL_URL_LIMIT.max]]],
        label: '计算机端落地页',
        use: [FinalUrl, {placeholder: '请输入计算机端落地页URL', width: INPUT_WIDTH, showType: 'pc'}],
        validators: [
            {
                validator: (value, formData) => {
                    if (!formData.mobileUrl && !value) {
                        return '请至少输入一个落地页';
                    }
                },
            },
            {
                validator: (value, formData) => validateFcUrl({
                    url: value,
                    field: 'pcFinalUrl',
                    idType: FcIdType.UNIT_LEVEL,
                    marketingTargetId: formData.marketingTargetId,
                }),
                triggers: ['onBlur', '@final'],
            },
        ],
        componentProps: ({disabled, buildType, marketingTargetId}) => ({
            disabled: disabled || (!isBatchRefreshUser() && buildType === AiBuildType.REFRESH),
            marketingTargetId,
        }),
    },
    {
        field: 'keywords',
        rules: [['required', '请输入种子词']],
        label: '种子词',
        use: [WithDrawerInput, {
            formConfig: {
                fields: [{
                    field: 'keywords',
                    label: null,
                    use: [WrappedTextLine, {
                        ...commonTextLineProps,
                        maxLine: 100,
                        title: '种子词',
                        placeholder: '请输入种子词',
                    }],
                    validators: commonTextLineValidators({maxLine: 100}),
                }],
            },
            transForm: (value: string[]) => {
                return getInputText({
                    value,
                    showTextNum: 3,
                });
            },
            readOnlyTextProps: {
                width: INPUT_WIDTH,
                btnVisible: true,
                placeholder: '请输入种子词',
            },
            drawerProps: {
                width: 800,
                title: '修改种子词',
            },
        }],
        validators: commonTextLineValidators({maxLine: 100}),
        componentProps: ({disabled, keywords}) => ({
            disabled,
            initialData: {keywords},
        }),
        transform: {
            parse: ({keywords}) => keywords,
        },
    },
    {
        field: 'avoidBusiness',
        label: '排除业务（选填）',
        use: [WithDrawerInput, {
            formConfig: {
                fields: [{
                    field: 'avoidBusiness',
                    label: null,
                    use: [WrappedTextLine, {
                        ...commonTextLineProps,
                        minLine: 0,
                        title: '排除业务',
                        placeholder: '请输入排除业务',
                    }],
                    validators: commonTextLineValidators({minLine: 0}),
                }],
            },
            transForm: (value: string[]) => {
                return getInputText({
                    value,
                    showTextNum: 3,
                });
            },
            readOnlyTextProps: {
                width: INPUT_WIDTH,
                btnVisible: true,
                placeholder: '请输入排除业务',
            },
            drawerProps: {
                width: 800,
                title: '修改排除业务',
            },
        }],
        validators: commonTextLineValidators({minLine: 0}),
        componentProps: ({disabled, avoidBusiness}) => ({
            disabled,
            initialData: {avoidBusiness},
        }),
        transform: {
            parse: ({avoidBusiness}) => avoidBusiness,
        },
    },
    {
        field: 'bidType',
        label: '出价方式',
        showRequiredMark: true,
        use: [RadioGroupWithDisabledTip, {
            options: FcBidTypeOptionsForAiBuild,
        }],
        componentProps: ({disabled, buildType}) => ({
            disabled: disabled || buildType === AiBuildType.REFRESH,
        }),
    },
    {
        field: 'budget',
        rules: [['required', '请输入日预算']],
        label: <BudgetLabel />,
        use: ['NumberInput', {
            placeholder: '请输入日预算',
            suffix: '元/日',
            width: INPUT_WIDTH,
            type: 'float',
            fixed: 2,
        }],
        // ! 这里没有预算不限的选项，所以校验比较少
        validators: (value, formData) => {

            const [min, max] = CampaignBudgetLimit;
            if (!value || value < min || value > max) {
                return `预算范围：${min} ～ ${max}`;
            }

            // ocpc下, 预算必须大于目标转化出价
            if (formData.bidType === FcBidTypeEnum.OCPC
                && formData.ocpcBid && value <= formData.ocpcBid) {
                return '预算必须大于目标转化出价';
            }
            // cpc下, 预算必须大于出价
            if (formData.bidType === FcBidTypeEnum.CPC
                && formData.campaignBid && value <= formData.campaignBid) {
                return '预算必须大于点击出价';
            }

            return '';
        },
        componentProps: ({disabled, buildType, bidType}) => ({
            disabled: disabled || (buildType === AiBuildType.REFRESH && bidType === FcBidTypeEnum.OCPC),
        }),
        visible: ({buildType, bidType}) => (bidType === FcBidTypeEnum.CPC || buildType !== AiBuildType.REFRESH),
    },
    {
        field: 'budgetMock',
        label: '预算',
        use: [ReadOnlyText, {
            transForm: () => '参照原项目/方案预算',
            disabled: true,
        }],
        visible: ({buildType, bidType}) => (bidType === FcBidTypeEnum.OCPC && buildType === AiBuildType.REFRESH),
    },
    {
        field: 'transTypes',
        label: '优化目标',
        rules: [['required', '请选择优化目标']],
        use: [TransTypesWithBoundary, {
            // * 这里全部先写死
            transManagerMode: TransManagerModeType.unlimited,
            // transAsset,
            cvSources: [],
            width: INPUT_WIDTH,
            bindIds: [],
            isEdit: false,
        }],
        componentProps: ({bidType, marketingTargetId, disabled, buildType}) => ({
            ocpcBidType: bidType === FcBidTypeEnum.OCPC ? FcOcpcBidTypeEnum.OCPC : FcOcpcBidTypeEnum.CPC,
            marketingTargetId,
            disabled: disabled || buildType === AiBuildType.REFRESH,
        }),
        visible: ({bidType}) => bidType === FcBidTypeEnum.OCPC,
        validators: value => {
            if (!value.length) {
                return '请选择优化目标';
            }
        },
    },
    {
        field: 'ocpcBid',
        rules: [['required', '请输入目标转化成本']],
        label: '目标转化成本',
        use: ['NumberInput', {
            placeholder: '请输入目标转化成本',
            min: 0,
            step: 0.01,
            suffix: '元/转化',
            width: INPUT_WIDTH,
            type: 'float',
            fixed: 2,
        }],
        visible: ({bidType}) => bidType === FcBidTypeEnum.OCPC,
        validators: (v, formData) => {
            const [min, max] = CampaignOcpcBidLimit;
            if (!v || v < min || v > max) {
                return `范围：${min} ～ ${max}`;
            }
            if (v > formData.budget && formData.buildType !== AiBuildType.REFRESH) {
                return '目标转化成本不能大于预算';
            }
        },
        componentProps: ({disabled, buildType}) => ({
            disabled: disabled || buildType === AiBuildType.REFRESH,
        }),
    },
    {
        field: 'campaignBid',
        rules: [['required', '请输入点击出价']],
        label: '点击出价',
        use: ['NumberInput', {
            placeholder: '请输入点击出价',
            step: 0.01,
            suffix: '元/点击',
            width: INPUT_WIDTH,
            type: 'float',
            fixed: 2,
        }],
        validators: (v, formData) => {
            const [min, max] = CampaignBidLimit;
            if (!v || v < min || v > max) {
                return `范围：${min} ～ ${max}`;
            }
            if (v > formData.budget) {
                return '点击出价不能大于预算';
            }
        },
        visible: ({bidType}) => bidType === FcBidTypeEnum.CPC,
        componentProps: ({disabled}) => ({disabled}),
    },
    {
        ...regionCommonFieldConfig,
        use: [WithDrawerInput, {
            formConfig: {
                fields: [{
                    ...regionCommonFieldConfig,
                    label: null,
                    use: [RegionWithBoundary],
                }],
            },
            transForm: (
                region: RegionValue, {isSupportThirdRegion}: {isSupportThirdRegion: boolean}
            ) => {
                return getRegionText({region, isSupportThirdRegion});
            },
            readOnlyTextProps: {
                width: INPUT_WIDTH,
                btnVisible: true,
            },
            drawerProps: {
                width: 800,
                title: '修改推广地域',
            },
        }],
        transform: {
            parse: ({region}) => region,
        },
    },
    {
        field: 'scheduleValue',
        label: '推广时段',
        use: [WithDrawerInput, {
            formConfig: {
                fields: [{
                    ...scheduleFieldConfig,
                    label: null,
                }],
            },
            transForm: (
                {schedule, showBasic}: {
                    schedule: ScheduleValue;
                    showBasic: boolean;
                }
            ) => {
                const {schedule: backendSchedule} = formatScheduleToBackend(
                    schedule?.scheduleValue || {}
                    , showBasic);
                const segments = parseScheduleToNL(backendSchedule);
                return segments.length ? segments.join('；') : '您还未选择任何时段';
            },
            readOnlyTextProps: {
                width: INPUT_WIDTH,
                btnVisible: true,
            },
            drawerProps: {
                width: 800,
                title: '修改推广时段',
            },
        }],
        componentProps({scheduleValue, bidType, disabled}) {

            const showBasic = getIsOcpcCampaign({
                campaignBidType: bidType,
                campaignOcpcBidType: bidType === FcBidTypeEnum.OCPC ? FcOcpcBidTypeEnum.OCPC : FcOcpcBidTypeEnum.CPC,
            });

            return {
                initialData: {
                    ...scheduleValue,
                    showBasic,
                    showAssets: false,
                },
                disabled,
            };
        },
    },
    {
        field: 'brandInfo',
        label: '品牌名称',
        use: [BrandName],
        componentProps: ({disabled}) => ({disabled}),
    },
];


export const formConfig: FormConfig<AIBuildFormData> = {
    fields,
    watch: {
        bidType: (value, formData) => {
            formData.scheduleValue = {
                ...formData.scheduleValue,
                schedule: {
                    ...formData.scheduleValue.schedule,
                    scheduleValue: getAllDaySelected(value === FcBidTypeEnum.OCPC),
                },
            };
        },
    },
};
