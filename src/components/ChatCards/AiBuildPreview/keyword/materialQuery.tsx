import {useEffect, useCallback, useMemo, ComponentProps} from 'react';
import {PaginationProps, TableProps} from '@baidu/one-ui';
import {useKeyOrientedArray} from '@/hooks/collection/array';
import {OneUiSortParams, useTableSort} from '@/hooks/tableList/sorter';
import {RowSelection, RowSelectionMethodsProps, useRowSelection} from '@/hooks/selection';
import {Pagination, toOneUIPaginationProps, useTablePagination} from '@/hooks/pagination';
import {Sorter} from '@/interface/report';
import {useColumnsWithoutQuery, useSortableColumns} from '@/hooks/tableList/columns';
import {MaterialList} from '@/interface/tableList';
import {toOneUIRowSelectionProps} from '@/utils/selection';
import FilterList from '@/components/common/materialList/FilterList';
import {FcBidTypeEnum} from '@/dicts/ocpc';
import {useLog} from '@/logger';
import {CommonDelete, DrawerTriggerInfo, KeywordRow, AccountAdContentType} from '../type';
import {filterAndSortRows, getCommonTableFieldsMap, getCurrentPageAfterDelete, useFilterLog} from '../util';
import {columnConfiguration, filtersFormatter, getTableFieldsMap} from './tableConfig';

const FIELD_KEYWORD = 'keyword';

export const getKeywordRowKey = (record: any) => record._id;

interface IProps {
    keywords: KeywordRow[];
    setKeywords: (keywords: KeywordRow[]) => void;
    triggerInfo: DrawerTriggerInfo;
    ocpcBidType: FcBidTypeEnum;
}

type IReturn = [
    {
        sorter: Sorter;
        pagination: Pagination;
        data: {rows: KeywordRow[], totalCount: number, filteredRows: KeywordRow[]};
        selection: RowSelection;
        columns: TableProps['columns'];
        filters: MaterialList.FilterItem[];
        rowSelectionProps: TableProps['rowSelection'];
        paginationProps: PaginationProps;
        filterListProps: ComponentProps<typeof FilterList>;
    },
    {
        setPageNo: (pageNo: number) => void;
        setPageSize: (pageSize: number) => void;
        getKeywordById: (key: string | number) => KeywordRow | undefined;
        onSort: (sorter: OneUiSortParams) => void;
        selectionOperations: RowSelectionMethodsProps;
        batchDeleteKeywords: CommonDelete;
        filterMethods: MaterialList.FilterMethods;
    }
];

const initialFilters: MaterialList.FilterItem[] = [];

export function useKeywordMaterialList({keywords, ocpcBidType, setKeywords, triggerInfo}: IProps): IReturn {
    const [sorter, onSort] = useTableSort({sortField: 'keywordRelevance', sortType: 'descend'});
    const [pagination, {setPageNo, setPageSize}] = useTablePagination();

    const {pageNo, pageSize} = pagination;


    const {campaignOptions, adgroupOptions, businessWordOptions} = useMemo(() => {
        return getCommonTableFieldsMap(keywords, triggerInfo);
    }, []);

    const {onFilterConfirm, onFilterCancel} = useFilterLog({
        tab: FIELD_KEYWORD,
    });

    const tableFieldsMap = getTableFieldsMap({
        campaignOptions,
        adgroupOptions,
        businessWordOptions,
        ocpcBidType,
    });
    const {
        filters,
        columns: columns_,
        handleColumnAction,
        filterMethods,
        getFilterContentByField,
    } = useColumnsWithoutQuery({
        columnConfiguration, tableFieldsMap, extraConfig: {initialFilters},
        onFilterConfirm,
        onFilterCancel,
    });

    const columns = useSortableColumns(columns_, sorter);

    const {rows: rawRows, totalRowCount: totalCount, filteredRows} = useMemo(() => {
        return filterAndSortRows({
            rows: keywords,
            filters,
            pageNo,
            pageSize,
            sorter,
            filtersFormatter,
        });
    }, [keywords, filters, sorter, pageNo, pageSize]);

    const [rows, {
        getItemByKey: getKeywordById,
        removeItemsByKeys: removeKeywords,
        set: setRows,
        getItemsByKeys: getKeywordsByIds,
    }] = useKeyOrientedArray(rawRows, {getKey: getKeywordRowKey});

    useEffect(() => {
        setRows(rawRows);
    }, [rawRows]);

    const listIds = useMemo(() => rows.map(getKeywordRowKey), [rows]);
    const [selection, selectionOperations] = useRowSelection({ids: listIds, totalCount});

    useEffect(
        () => {
            selectionOperations.resetRowSelection();
        },
        [filters, sorter]
    );

    useEffect(() => {
        setPageNo(1);
    }, [filters, sorter, setPageNo]);

    const log = useLog();

    const batchDeleteKeywords: CommonDelete = useCallback(
        async ({
            isForAllCampaigns,
            isForBusinessWord,
            isCheckAll,
            excludeIds,
            selectedIds,
        }) => {

            const idsToDelete = isCheckAll
                ? filteredRows.map(i => i._id).filter(id => !excludeIds.includes(id))
                : selectedIds;

            const keywordsToDelete = getKeywordsByIds(idsToDelete).map(i => i.keyword);

            const newKeywords = keywords.filter(i => {
                if (isForAllCampaigns) {
                    return !keywordsToDelete.includes(i.keyword);
                }

                if (isForBusinessWord) {
                    const businessWords = getKeywordsByIds(idsToDelete).map(i => i.businessWord);
                    return !businessWords.includes(i.businessWord) || !keywordsToDelete.includes(i.keyword);
                }

                return !idsToDelete.includes(i._id);
            });

            setKeywords(newKeywords);
            selectionOperations.resetRowSelection();
            setPageNo(getCurrentPageAfterDelete(totalCount, pageSize, pageNo, idsToDelete.length));
            log('click', {
                level: 'ai-build-one-edit-delete',
                field: FIELD_KEYWORD,
                'extra_params': JSON.stringify({
                    isForAllCampaigns: !!isForAllCampaigns,
                    isForBusinessWord: !!isForBusinessWord,
                }),
            });
        },
        [
            setKeywords, getKeywordsByIds, selectionOperations, setPageNo, keywords, filteredRows,
            totalCount, pageSize, pageNo,
        ]
    );

    // to one-ui props
    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {
                selection,
                onSelectChange: selectionOperations.onSelectChange,
                selectAll: selectionOperations.selectAll,
                showSelectAll: true,
            },
            rows,
            {getId: getKeywordRowKey, multiPageSelection: true}
        ),
        [selection, selectionOperations, rows],
    );
    const paginationProps = useMemo(
        () => toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount),
        [pagination, setPageNo, setPageSize, totalCount],
    );

    const filterListProps = {
        filters,
        deleteFilterByIndex: filterMethods.deleteFilterByIndex,
        changeFilterByIndex: filterMethods.changeFilterByIndex,
        getFilterContentByField,
    };

    const onSortWithLog = useCallback(
        (sorter: OneUiSortParams) => {
            onSort(sorter);
            log('click', {
                level: 'ai-build-one-edit-sort',
                field: FIELD_KEYWORD,
                'extra_params': JSON.stringify(sorter),
            });
        },
        [onSort, log]
    );

    return [
        {
            data: {rows, totalCount, filteredRows},
            sorter,
            pagination,
            selection,
            columns,
            filters,
            rowSelectionProps,
            paginationProps,
            filterListProps,
        },
        {
            setPageNo,
            setPageSize,
            getKeywordById,
            onSort: onSortWithLog,
            selectionOperations,
            batchDeleteKeywords,
            filterMethods,
        },
    ];
}
