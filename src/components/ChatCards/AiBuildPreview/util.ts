import {TableProps} from '@baidu/one-ui';
import {useCallback, useEffect, useRef, useState} from 'react';
import {useOriginalDeepCopy} from 'huse';
import {omit} from 'lodash-es';
import {MaterialList} from '@/interface/tableList';
import {SortType} from '@/interface/report';
import {useLog} from '@/logger';
import {AdgroupContentType, CreativeImageRow, KeywordRow, CreativeTextRow, DrawerTriggerInfo} from './type';
import {KeywordTagEnum} from './config';

interface FilterableRow {
    _id: number | string;
    [key: string]: any;
}

export function filterAndSortRows<T extends FilterableRow>({
    rows,
    filters: _filters,
    pageNo,
    pageSize,
    sorter,
    filtersFormatter,
}: {
    rows: T[];
    filters: MaterialList.FilterItem[];
    pageNo: number;
    pageSize: number;
    sorter?: {
        sortField: string;
        sortType: SortType;
    };
    filtersFormatter?: (filters: MaterialList.FilterItem[]) => MaterialList.FilterItem[];
}) {
    let filteredRows = [...rows];

    const filters = filtersFormatter ? filtersFormatter(_filters) : _filters;
    // 处理筛选
    if (filters.length) {
        filteredRows = filteredRows.filter(row => {
            return filters.every(filter => {
                const {column, operator, values} = filter;

                const originRowValue = row[column as keyof T];
                const checkRowValue = column === 'keywordTags'
                    ? (originRowValue?.length ? originRowValue : [KeywordTagEnum.noTag])
                    : originRowValue;


                return matchFilter(checkRowValue, operator, values);
            });
        });
    }

    // 处理排序
    if (sorter?.sortField && sorter?.sortType) {
        filteredRows.sort((a, b) => {
            const compareResult = (a[sorter.sortField as keyof CreativeImageRow] || 0)
                > (b[sorter.sortField as keyof CreativeImageRow] || 0) ? 1 : -1;
            return sorter.sortType === 'ascend' ? compareResult : -compareResult;
        });
    }


    const totalRowCount = filteredRows.length;

    // 处理分页
    const pagedRows = filteredRows.slice((pageNo - 1) * pageSize, pageNo * pageSize);

    return {
        rows: pagedRows,
        filteredRows,
        totalRowCount,
    };
}

function matchNumberEnumFilter(rowValue: any, values: string[]) {
    return values.some(value => {
        const [_min, _max] = value.split('-');
        const min = _min ? Number(_min) : undefined;
        const max = _max ? Number(_max) : undefined;

        if (min && rowValue <= min) {
            return false;
        }
        if (max && rowValue > max) {
            return false;
        }
        return true;
    });
}

// eslint-disable-next-line complexity
function matchFilter(rowValue: any, operator: string, values: any[]) {
    switch (operator) {
        case 'like':
            return values.some(value => `${rowValue}`.includes(`${value}`));
        case 'nlike':
            return !values.some(value => `${rowValue}`.includes(`${value}`));
        case 'neq':
            return !values.some(value => rowValue === value);
        case 'gt':
            return rowValue && Number(rowValue) > Number(values[0]);
        case 'gte':
            return rowValue && Number(rowValue) >= Number(values[0]);
        case 'lt':
            return rowValue && Number(rowValue) < Number(values[0]);
        case 'lte':
            return rowValue && Number(rowValue) <= Number(values[0]);
        case 'numberEnum':
            return matchNumberEnumFilter(rowValue, values);
        case 'in':
        default:
            if (Array.isArray(rowValue)) {
                return rowValue.some(value => values.includes(value));
            }
            return values.some(value => rowValue === value);
    }
}

// 获取筛选项选项的通用方法
function getFilterOptions(rows: any[], field: string, triggerInfo: DrawerTriggerInfo) {
    const uniqueValues = new Set(
        [...rows.map(row => row[field]), triggerInfo.filters[field as keyof typeof triggerInfo.filters]].filter(Boolean)
    );
    return Array.from(uniqueValues).map(value => ({
        label: value,
        value: value,
    }));
}

// 获取通用的表格字段映射
export function getCommonTableFieldsMap(rows: any[], triggerInfo: DrawerTriggerInfo) {
    const campaignOptions = getFilterOptions(rows, 'campaignName', triggerInfo);
    const adgroupOptions = getFilterOptions(rows, 'adgroupName', triggerInfo);
    const businessWordOptions = getFilterOptions(rows, 'businessWord', triggerInfo);

    return {
        campaignOptions,
        adgroupOptions,
        businessWordOptions,
    };
}


export const commonTableProps: TableProps = {
    headerFixTop: 0,
    size: 'small',
    className: 'ai-build-preview-table-list',
    scroll: {y: 'calc(100vh - 430px)'},
    pagination: false,
};


function getTableTip(triggerInfo: DrawerTriggerInfo) {
    if (triggerInfo.filters.businessWord) {
        return '支持调整所有方案的物料，已为您筛选当前业务的数据，如果调整其他业务，请清空筛选项';
    }
    if (triggerInfo.filters.adgroupName) {
        return '支持调整所有方案的物料，已为您筛选当前单元的物料，如果调整其他单元，请清空筛选项';
    }
    if (triggerInfo.filters.campaignName) {
        return '支持调整所有方案的物料，已为您筛选当前方案的物料，如果调整其他方案，请清空筛选项';
    }
    return '';
}


export function useFirstFilter(
    filters: MaterialList.FilterItem[],
    triggerInfo: DrawerTriggerInfo,
    filterMethods: MaterialList.FilterMethods,
) {

    const [isShowTip, setIsShowTip] = useState(true);
    const hasShownTip = useRef(false);
    const tip = useRef(getTableTip(triggerInfo));

    const filters_ = useOriginalDeepCopy(filters);

    useEffect(() => {
        if (hasShownTip.current) {
            setIsShowTip(false);
        }
    }, [filters_]);

    useEffect(() => {
        for (const key in triggerInfo.filters) {
            if (triggerInfo.filters[key as keyof DrawerTriggerInfo['filters']]) {
                filterMethods.updateOrAddFilterByField(key, {
                    values: [triggerInfo.filters[key as keyof DrawerTriggerInfo['filters']]],
                    operator: 'in',
                });
            }
        }
        setTimeout(() => {
            hasShownTip.current = true;
        }, 0);
    }, []);

    return {
        isShowTip,
        tip: tip.current,
    };
}


function getMapKey(campaignName: string, adgroupName: string) {
    return JSON.stringify({campaignName, adgroupName});
}

export function formatRowToChangeData({
    keywords,
    creativeTexts,
    creativeImages,
}: {
    keywords: KeywordRow[];
    creativeTexts: CreativeTextRow[];
    creativeImages: CreativeImageRow[];
}): Array<{
    campaignName: string;
    adgroupName: string;
    keywords: AdgroupContentType['keywords'];
    creativeTexts: AdgroupContentType['creativeTexts'];
    creativeImages: AdgroupContentType['creativeImages'];
}> {
    // 以 campaignName 和 adgroupName 为 key 的物料数据 map
    const map = new Map<string, {
        keywords: AdgroupContentType['keywords'];
        creativeTexts: AdgroupContentType['creativeTexts'];
        creativeImages: AdgroupContentType['creativeImages'];
        adgroupName: string;
        campaignName: string;
    }>();
    for (const keyword of keywords) {
        const key = getMapKey(keyword.campaignName, keyword.adgroupName);
        if (!map.has(key)) {
            map.set(key, {
                keywords: [],
                creativeTexts: [],
                creativeImages: [],
                adgroupName: keyword.adgroupName,
                campaignName: keyword.campaignName,
            });
        }

        const item = omit(keyword, [
            '_id', 'businessWord', 'campaignName', 'adgroupName', 'matchTypeStr',
        ]) as AdgroupContentType['keywords'][number];
        map.get(key)?.keywords?.push(item);
    }

    for (const creativeText of creativeTexts) {
        const key = getMapKey(creativeText.campaignName, creativeText.adgroupName);
        if (!map.has(key)) {
            map.set(key, {
                campaignName: creativeText.campaignName,
                adgroupName: creativeText.adgroupName,
                keywords: [],
                creativeTexts: [],
                creativeImages: [],
            });
        }

        const item = omit(
            creativeText, ['_id', 'businessWord', 'campaignName', 'adgroupName']
        ) as AdgroupContentType['creativeTexts'][number];
        map.get(key)?.creativeTexts?.push(item);
    }
    for (const creativeImage of creativeImages) {
        const key = getMapKey(creativeImage.campaignName, creativeImage.adgroupName);
        if (!map.has(key)) {
            map.set(key, {
                keywords: [],
                creativeTexts: [],
                creativeImages: [],
                adgroupName: creativeImage.adgroupName,
                campaignName: creativeImage.campaignName,
            });
        }

        const item = omit(
            creativeImage, ['_id', 'businessWord', 'campaignName', 'adgroupName']
        ) as AdgroupContentType['creativeImages'][number];
        map.get(key)?.creativeImages?.push(item);
    }
    return Array.from(map.values());
}

/**
 * 计算删除后当前页码
 * @param total 删除前的总条数
 * @param pageSize 每页条数
 * @param currentPage 当前页码
 * @param deletedCount 删除的条数
 * @returns 删除后的当前页码
*/
export function getCurrentPageAfterDelete(
    total: number,
    pageSize: number,
    currentPage: number,
    deletedCount: number
): number {
    const newTotal = total - deletedCount;

    const newTotalPages = Math.max(Math.ceil(newTotal / pageSize), 1);

    return Math.min(newTotalPages, currentPage);
}

export function useFilterLog({
    tab,
}: {
    tab: string;
}) {

    const log = useLog();

    const onFilterConfirm: MaterialList.onFilterConfirm = useCallback(({field, operator, values}) => {
        log('click', {
            level: 'ai-build-filter-confirm',
            field,
            item: tab,
            'extra_params': JSON.stringify({field, operator, values}),
        });
    }, [tab, log]);

    const onFilterCancel: MaterialList.onFilterCancel = useCallback(({field}) => {
        log('click', {
            level: 'ai-build-filter-cancel',
            field,
            item: tab,
        });
    }, [tab, log]);
    return {
        onFilterConfirm,
        onFilterCancel,
    };
}
