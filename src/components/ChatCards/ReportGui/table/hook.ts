/* eslint-disable complexity */
import {useEffect, useMemo, useState} from 'react';
import {isFunction, isString, capitalize} from 'lodash-es';
import guid from '@baidu/guid';
import {aixAiCaiGouConversions} from '@/components/ChatCards/Chart/config';
import {fetchReportData} from '@/api/getReportData';
import {FetchParams, SdkFilterProps} from '@/interface/report';
import {useRowSelection} from '@/hooks/selection';
import {useMaterialListConfiguration} from '@/hooks/tableList/configuration';
import {useTablePagination} from '@/hooks/pagination';
import {GUI_REPORT_CONFIG} from '@/components/ChatCards/ReportGui/config';
import {useFilters} from '@/hooks/tableList/filters';
import {useTableSort} from '@/hooks/tableList/sorter';
import {useSortableColumns} from '@/hooks/tableList/columns';
import {getFilterTypeFromConfigs} from '@/utils/tableList/filters';
import {toMarsSortProps, toOneUISortProps} from '@/utils/report';
import {ReportType, PRODUCT_TYPE_TO_FIELD} from '@/config/report';
import {PRODUCT} from '@/dicts/campaign';
import {videoIdeaTypeValues} from '@/components/AiReportSDK/reportController/util';
import {useReportOuterContext} from '../../../AiReportSDK/context';
import {projectValueToKeyMap} from '../../../AiReportSDK/reportController/util';
import {useRequestRankListData, getColumnConfigurationWithSplit} from './util';

const initialData = {rows: [], summary: {}, rowCount: 0, totalRowCount: 0};
const initialSort = [{column: '', sortRule: ''}];

// 不需要初始化在filters中，可以通过交互来变更的column （智能体参数 -> 前端初始化在顶部可交互区域再下发 -> marspro）
const interactiveColumns = ['campaignId', 'projectId', 'productLine'];
// 不需要初始化在filters中，不可以通过交互变更，但是需要透传给marspro的column（智能体参数 -> 前端无感知 -> marspro）
const passColumns = ['product'];

export function useGuiTable(
    filterProps: SdkFilterProps,
    campaignReportParams_?: FetchParams,
    reportParamsMap?: Record<string, FetchParams>,
    defaultOption?: string,
    hasCampaignType?: boolean,
) {
    const {setReportType, reportType: contextReportType} = useReportOuterContext();

    const {
        dateRange, campaignIds = [], projectIds = [],
        timeUnit, productLineList = [], videoIdeaType, projectType, device,
    } = filterProps;
    const [selectedTab, setSelectedTab] = useState(defaultOption ? defaultOption : '');
    // 添加细分筛选状态
    const [splitColumn, setSplitColumn] = useState('');

    const campaignReportParams = useMemo(
        () => {
            if (!defaultOption && reportParamsMap && productLineList.length) {
                let configKey = PRODUCT_TYPE_TO_FIELD[
                    productLineList?.[0] as keyof typeof PRODUCT_TYPE_TO_FIELD || PRODUCT.FC];
                // 如果有标准版/极速版的选项，还需要根据这个选项来选择 reportParamsMap 配置，目前只有feed需要
                // 对应的 key 为 feedFast/feedStandard, 以前key是productLine，现在在它的基础上加了一层 projectType
                if (configKey === PRODUCT_TYPE_TO_FIELD[PRODUCT.FEED] && hasCampaignType && projectType) {
                    configKey = configKey + capitalize(projectValueToKeyMap[projectType]);
                }
                return reportParamsMap[configKey];
            }
            if (defaultOption && reportParamsMap) {
                return reportParamsMap[selectedTab];
            }
            return campaignReportParams_;
        },
        [selectedTab, productLineList, projectType, hasCampaignType]
    );
    const {reportType, sorts = initialSort} = campaignReportParams as FetchParams || {};
    const {
        maxRowCountLimit, showVideoIdeaTypeFilter, needVirtualId,
        hasSummary, customColumnRender, customFilterConfig, tableProps = {}, customerFields = [],
        customRequestFields = [], showProductFilter, supportProductList = [], productFilterConflict = false,
    } = GUI_REPORT_CONFIG[reportType];
    const {rowKey} = tableProps;
    const getIds = isFunction(rowKey) ? rowKey : (record: Record<string, any>) => record?.[rowKey];
    const [sorter, onSort] = useTableSort(toOneUISortProps(sorts?.[0] || initialSort[0]));
    const [columnConfiguration_, modConfiguration] = useMaterialListConfiguration(reportType, {withNewCategory: true});

    // 处理细分列配置
    const columnConfiguration = useMemo(
        () => getColumnConfigurationWithSplit(columnConfiguration_, splitColumn),
        [columnConfiguration_, splitColumn]
    );

    const {
        tableColumns, customColumns, defaultColumns, columnConfigs, splitColumnItems = [],
    } = columnConfiguration;
    const [filters, filterMethods] = useFilters({
        initialFilters: campaignReportParams?.filters?.filter(
            item => !interactiveColumns.includes(item.column) && !passColumns.includes(item.column)),
        getFilterTypeByField: column => getFilterTypeFromConfigs(columnConfigs[column]),
    });
    const [topCount, setTopCount] = useState(campaignReportParams?.topCount);
    const [pagination, {setPageNo, setPageSize, resetPageNo}] = useTablePagination();

    useEffect(
        () => {
            resetPageNo();
        },
        [campaignReportParams?.filters, campaignReportParams?.sorts, filterProps]
    );

    useEffect(() => {
        if (reportType !== campaignReportParams_?.reportType
            && reportType !== contextReportType
        ) {
            setReportType(reportType);
        }
    }, [reportType, campaignReportParams_?.reportType, contextReportType, setReportType]);

    const newFilters: FetchParams['filters'] = useMemo(
        () => {
            let newFilters = filters || [];
            if (campaignIds && campaignIds.length > 0) {
                newFilters = newFilters.filter(item => item.column !== 'campaignId').concat({
                    column: 'campaignId',
                    operator: 'IN',
                    values: campaignIds,
                });
            }
            if (projectIds && projectIds.length > 0) {
                newFilters = newFilters.filter(item => item.column !== 'projectId').concat({
                    column: 'projectId',
                    operator: 'IN',
                    values: projectIds,
                });
            }
            // 智能体filter参数透传给marspro，不需要在filters中实现筛选项
            passColumns.forEach(column => {
                const filter = campaignReportParams?.filters?.find(item => item.column === column);
                if (filter) {
                    newFilters = newFilters.concat(filter);
                }
            });
            if (device !== undefined && device !== 0) {
                newFilters = newFilters.filter(item => item.column !== 'device').concat({
                    column: 'device',
                    operator: 'IN',
                    values: [device],
                });
            }
            if (customFilterConfig) {
                const {getFilters} = customFilterConfig;
                const customFilters = getFilters();
                const {value} = customFilters;
                newFilters = newFilters.concat(value);
            }
            if (productLineList.length && showProductFilter && supportProductList.length
                && !productFilterConflict) {
                newFilters = newFilters.filter(item => item.column !== 'productLine').concat({
                    column: 'productLine',
                    operator: 'IN',
                    values: productLineList,
                });
            }
            if (showVideoIdeaTypeFilter && videoIdeaType) {
                if (+videoIdeaType === videoIdeaTypeValues.all) {
                    newFilters = newFilters.filter(item => item.column !== 'videoIdeaType');
                }
                else {
                    newFilters = newFilters.filter(item => item.column !== 'videoIdeaType').concat({
                        column: 'videoIdeaType',
                        operator: 'IN',
                        values: [+videoIdeaType],
                    });
                }
            }
            return newFilters;
        },
        [
            filters,
            campaignIds,
            projectIds,
            device,
            customFilterConfig,
            productLineList,
            videoIdeaType,
            showVideoIdeaTypeFilter,
            showProductFilter,
            productFilterConflict,
            campaignReportParams?.filters,
        ],
    );
    const formattedCampaignReportParams = useMemo(
        () => {
            const guiReportColumns = customColumns || defaultColumns;
            const params = {
                ...campaignReportParams,
                ...dateRange,
                columns: [...guiReportColumns, ...customRequestFields, ...(splitColumn ? [splitColumn] : [])],
                timeUnit,
                startRow: (pagination.pageNo - 1) * pagination.pageSize,
                // topCount情况下，取参数中的rowCount
                rowCount: topCount
                    ? (campaignReportParams.rowCount || pagination.pageSize)
                    : pagination.pageSize,
                filters: newFilters,
                sorts: toMarsSortProps(sorter),
            };
            if (maxRowCountLimit && ((params.startRow + params.rowCount) > maxRowCountLimit)) {
                params.rowCount = maxRowCountLimit - params.startRow;
            }
            return params;
        },
        // 日历、营销方案、搜索框、
        [
            campaignReportParams, customColumns, defaultColumns, dateRange, pagination,
            timeUnit, newFilters, topCount, maxRowCountLimit, sorter, customRequestFields, splitColumn,
        ] // todo 不该这么写啊
    );

    const {refreshTime, request, data: reportData = initialData, pending} = useRequestRankListData(
        fetchReportData,
        formattedCampaignReportParams
    );

    const {rows: originRows, totalRowCount, summary} = reportData;

    const rows = useMemo(() => {
        return needVirtualId && isString(rowKey)
            ? originRows.map((row: Record<string, any>) => ({...row, [rowKey]: guid()}))
            : originRows;
    }, [originRows, needVirtualId, rowKey]);

    const listIds = useMemo(
        () => {
            if (rowKey) {
                return rows.map(getIds);
            }
            return [];
        },
        [rows, rowKey]
    );
    const [selection, selectionOperations] = useRowSelection({ids: listIds, totalCount: totalRowCount});

    const {resetRowSelection} = selectionOperations;

    useEffect(
        () => {
            resetRowSelection();
        },
        [filterProps, pagination]
    );

    useEffect(() => {
        request();
    }, [request]);

    const dataSource = useMemo(
        () => {
            if (hasSummary) {
                return rows.length
                    ? [{...summary, totalCount: totalRowCount, date: `总计-${totalRowCount}`}, ...rows]
                    : [];
            }
            return rows;
        },
        [rows, summary, totalRowCount, hasSummary]
    );

    const formatedColumns = useMemo(
        () => {
            const customColumnRenderProps = {guiReport: true};
            const customColumnConfigs = isFunction(customColumnRender)
                ? customColumnRender(customColumnRenderProps)
                : customColumnRender;
            let filterColumns = tableColumns;

            const selectAcgJytProduct = reportType === ReportType.ACG_ACCOUNT || reportType === ReportType.ACG_GOODS;
            // 非单选爱采购加油推，不能展示爱采购的相关指标
            if (!selectAcgJytProduct) {
                filterColumns = tableColumns?.filter(
                    ({accessorKey}) => !aixAiCaiGouConversions.includes(accessorKey));
            }
            return filterColumns.concat(customerFields).map(item => ({
                ...item,
                ...(customColumnConfigs ? customColumnConfigs[item.accessorKey] : {}),
            }));
        },
        [customColumnRender, tableColumns, customerFields, productLineList, reportType]
    );

    const columns = useSortableColumns(formatedColumns, sorter);

    return [
        {
            reportType,
            pending,
            columns,
            dataSource,
            filters,
            pagination,
            columnConfiguration,
            reportData: {...reportData, rows}, // 用修改后的rows
            topCount,
            downloadParams: formattedCampaignReportParams,
            selectedTab,
            selection,
            rowKey,
            refreshTime,
            splitColumn,
            splitColumns: splitColumnItems,
        },
        {
            onSort,
            setPageNo,
            setPageSize,
            resetPageNo,
            modConfiguration,
            filterMethods,
            refresh: request,
            setTopCount,
            setSelectedTab,
            selectionOperations,
            getIds,
            setSplitColumn,
        },
    ] as const;
}
