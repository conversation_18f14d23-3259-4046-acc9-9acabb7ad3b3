/* eslint-disable max-statements */
/* eslint-disable complexity */
import {useRef, useMemo, useCallback} from 'react';
import {IconAppsSettings} from 'dls-icons-react';
import {Boundary, CacheProvider} from 'react-suspense-boundary';
import SplitCascader from 'commonLibs/materialList/Split';
import RefreshOperation from 'commonLibs/components/refreshOperation';
import {Tabs, TabsProps, TabItemProps} from '@baidu/light-ai-react';
import {Table, Button, TableColumnProps, SearchBox, Pagination, SearchBoxProps, TableProps} from '@baidu/one-ui';
import CustomFieldsSelector from '@/components/common/CustomFieldsSelector';
import {FetchParams, SdkFilterProps} from '@/interface/report';
import {toOneUIPaginationProps} from '@/hooks/pagination';
import {useAdRoute} from '@/modules/Ad/routes';
import {toOneUIRowSelectionProps} from '@/utils/selection';
import {AiChatProxyInstance} from '@/utils/aiChat';
import {ReportType} from '@/config/report';
import {PageType} from '@/dicts/pageType';
import {stringKeyMap} from '@/config/filters';
import {GUI_REPORT_CONFIG} from '@/components/ChatCards/ReportGui/config';
import Download from '../../Chart/download';
import {ReportCardParameters} from '../../Chart/interface';
import {useGuiTable} from './hook';
import {TableContextProvider} from './context';
import FilterList from './filterList';
import './index.global.less';

const initialRef = {updateColumnWidths: (columns: TableColumnProps[]) => {}};
interface Props {
    campaignReportParams: FetchParams;
    level: string;
    filterProps: SdkFilterProps;
    options?: TabItemProps[];
    defaultOption?: string;
    reportParamsMap?: Record<string, FetchParams>;
    aiChat: AiChatProxyInstance;
    cardParameters?: ReportCardParameters;
}

export default function ReportGuiTable({
    campaignReportParams,
    reportParamsMap,
    cardParameters, // 是否有方案类型
    defaultOption,
    options,
    level,
    filterProps,
    aiChat,
}: Props) {
    const {linkTo} = useAdRoute();
    const [{
        rowKey,
        reportType,
        topCount,
        columns,
        dataSource,
        pending,
        filters,
        pagination,
        columnConfiguration,
        reportData,
        downloadParams,
        selectedTab,
        selection,
        refreshTime,
        splitColumns,
    }, {
        onSort, setPageNo, setPageSize, modConfiguration, resetPageNo, filterMethods, setTopCount, setSelectedTab,
        selectionOperations, getIds, refresh, setSplitColumn,
    }] = useGuiTable(
        filterProps,
        campaignReportParams,
        reportParamsMap,
        defaultOption,
        cardParameters?.hasCampaignType
    );
    const {
        maxRowCountLimit,
        customFilterConfig,
        showDownload = true,
        customOperations: CustomOperations,
        tableProps = {},
    } = GUI_REPORT_CONFIG[reportType];
    const {
        operationBar: BatchOperationBar,
        showBatchOperationBar = () => true,
        getCheckBoxEnabled,
    } = tableProps;
    const {customColumns, defaultColumns, columnConfigs, columnCategories, allColumns} = columnConfiguration;
    const {dateRange} = filterProps;
    const {summary, totalRowCount, rows} = reportData;

    const tableContainerRef = useRef(initialRef);
    const refreshColumnsWidth = () => {
        tableContainerRef.current?.updateColumnWidths(columns);
    };
    const totalCount = maxRowCountLimit
        ? (totalRowCount < maxRowCountLimit ? totalRowCount : maxRowCountLimit)
        : totalRowCount;
    const paginationProps = toOneUIPaginationProps({...pagination, setPageNo, setPageSize}, totalCount, {
        size: 'small',
        className: 'gui-table-pagination',
    });

    const filterListProps = {
        filters,
        customFilterConfig,
        deleteFilterByIndex: filterMethods.deleteFilterByIndex,
        columnConfigs,
        topCount,
        setTopCount,
    };

    const {smartFilterable, smartFilterColumns = []} = columnConfiguration;
    const searchProps: SearchBoxProps = {
        placeholder: `请搜索${columnConfigs[smartFilterColumns[0]]?.columnText || ''}`,
        onSearch: (e: any) => {
            if (!e.target.value) {
                return;
            }
            filterMethods.changeFilterByField(smartFilterColumns[0], {
                operator: stringKeyMap.like, values: [e.target.value],
            });
        },
        className: 'table-search',
        size: 'medium',
        width: 150,
    };

    const customFieldsProps = {
        customColumns,
        summary,
        defaultColumns,
        columnConfigs,
        columnCategories,
        allColumns,
        modConfiguration,
    };

    const {onSelectChange, selectAll} = selectionOperations;

    const rowSelectionProps = useMemo(
        () => toOneUIRowSelectionProps(
            {selection, onSelectChange, selectAll},
            rows,
            {getId: getIds, getCheckBoxEnabled}
        ),
        [selection, onSelectChange, selectAll, rows, getIds, getCheckBoxEnabled]
    );

    const tableListProps: TableProps = {
        loading: pending,
        onSortClick: onSort,
        columns,
        dataSource,
        size: 'small',
        autoHideOperation: 'filter',
        updateWidthChange: true,
        pagination: false,
        headerFixTop: 0,
        bottomScroll: {
            bottom: 0,
        },
    };

    const tabsProps: TabsProps<string> = {
        variant: 'light',
        value: selectedTab,
        items: options,
        onChange: (value: any) => {
            setSelectedTab(value);
            resetPageNo();
            filterMethods.resetFilters();
            setTopCount(undefined);
        },
    };

    // 实时报告新增上次刷新时间与刷新按钮
    const refreshOperationProps = {
        refreshTime,
        loading: pending,
        refreshList: refresh,
    };

    // 实时报告细分级联
    const splitCascaderProps = {
        splitColumns: (splitColumns || []).map((item: any) => item.dataIndex),
        columnConfigs: columnConfigs || {},
        onSplitChange: setSplitColumn,
        cascaderProps: {
            size: 'small',
        },
    };


    const hasRowSelect = showBatchOperationBar({
        downloadParams,
    });

    if (rowKey && hasRowSelect) {
        tableListProps.rowKey = rowKey;
        tableListProps.rowSelection = rowSelectionProps;
    }

    const resetPageNoAnRefresh = useCallback(() => {
        resetPageNo();
        refresh();
    }, [resetPageNo, refresh]);
    const toManageMarketPointList = () => {
        linkTo(PageType.MarketPointList);
    };

    const batchOptProps = {
        selection,
        selectionOperations,
        totalCount: totalRowCount,
        dataSource: rows,
        rowKey,
        dateRange,
        refresh: resetPageNoAnRefresh,
        aiChat,
    };

    return (
        <CacheProvider>
            <Boundary>
                <div className="gui-table-container">
                    {options && options.length && (
                        <div className="gui-table-tab">
                            <Tabs {...tabsProps} />
                        </div>
                    )}
                    <div className="operation-container">
                        <div className="filters-content">
                            <FilterList {...filterListProps} />
                        </div>
                        <div className="operation-content">
                            {smartFilterable && <SearchBox {...searchProps} />}
                            <CustomFieldsSelector {...customFieldsProps} size="medium" />
                            <Button onClick={refreshColumnsWidth} size="medium">重置列宽</Button>
                            {/* <SplitCascader {...splitCascaderProps} /> */}
                            <RefreshOperation {...refreshOperationProps} />
                            {showDownload && <Download reportParams={downloadParams} level={level} size="medium" />}
                            {CustomOperations && <CustomOperations />}
                            {
                                reportType === ReportType.MARKET_POINT
                                    ? (
                                        <Button type="strong" icon={IconAppsSettings} onClick={toManageMarketPointList}>
                                            管理营销要点
                                        </Button>
                                    )
                                    : null
                            }
                        </div>
                    </div>
                    {BatchOperationBar && hasRowSelect
                        && <BatchOperationBar {...batchOptProps} />}
                    <TableContextProvider value={{refresh: resetPageNoAnRefresh}}>
                        <Table
                            {...tableListProps}
                            ref={ref => {
                                if (ref) {
                                    tableContainerRef.current = ref;
                                }
                            }}
                        />
                    </TableContextProvider>
                    {!topCount && <Pagination {...paginationProps} />}
                </div>
            </Boundary>
        </CacheProvider>
    );
}
