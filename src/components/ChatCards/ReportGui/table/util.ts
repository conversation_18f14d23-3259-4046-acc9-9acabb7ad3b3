import {useState, useCallback} from 'react';
import {useRequestCallback} from 'huse';

const initialData = {rows: [], summary: {}, rowCount: 0, totalRowCount: 0};

// 报告新增支持立即刷新与刷新时间
// 参照老平台对useRequestCallback做了个封装
export function useRequestRankListData(task: (params: any) => Promise<any>, params?: any) {
    const [refreshTime, setRefreshTime] = useState(new Date());
    const refreshTask = useCallback(payload => {
        setRefreshTime(new Date());
        return task(payload);
    }, [task]);
    const [refresh, {data = initialData, pending, error}] = useRequestCallback(refreshTask, params);

    return {
        refreshTime,
        request: refresh,
        data,
        error,
        pending,
    };
}

// 报告支持细分
export const getColumnConfigurationWithSplit = (columnConfiguration: any, splitColumn: string) => {
    const {tableColumns, columnConfigs, splitColumnItems = [], ...rest} = columnConfiguration;

    if (splitColumn && splitColumnItems.length > 0) {
        const splitColumnItem = splitColumnItems.find((item: any) => item.dataIndex === splitColumn);
        if (splitColumnItem) {
            return {
                ...rest,
                tableColumns: [...tableColumns, splitColumnItem],
                columnConfigs: {
                    ...columnConfigs,
                    [splitColumn]: columnConfigs[splitColumn] || {},
                },
                splitColumnItems,
            };
        }
    }

    return columnConfiguration;
};
