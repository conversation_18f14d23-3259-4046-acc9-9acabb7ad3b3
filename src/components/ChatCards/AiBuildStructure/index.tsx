import {useCallback, useState, ReactNode, useEffect} from 'react';
import classNames from 'classnames';
import {partial, noop, uniqBy, uniqueId} from 'lodash-es';
import {IconCheck} from 'dls-icons-react';
import {Radio, Checkbox, Input, Switch, SwitchProps, Toast} from '@baidu/one-ui';
import {Button, Typography, Alert} from '@baidu/light-ai-react';
import {StaticTip} from 'commonLibs/Tips';
import {useCardComplete, useRecordByComponent} from '@/hooks';
import {OPERATION_TYPE} from '@/dicts/fieldOperationType';
import {SliderWithPager, KeyedReactNode} from '@/components/common/Sliders/SliderWithPager';
import {LogEmphasize, useLog} from '@/logger';
import {AI_BUILD_RECORD_CONFIG} from '@/config/aiBuildRecordConfig';
import PrevButton from '../../PrevButton';
import {AiProps} from '../interface';
import {AdgroupInputList} from './AdgroupInputList';
import './index.global.less';

const Text = Typography.Text;

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

export const ADVANCE_CREATIVE_TIP_TEXT = '为营销方案绑定高级样式创意组，丰富创意展现从而吸引用户点击。根据不同行业特质，每个业务生成2个计算机端、2个移动端样式。';
export const AUTO_TARGETING_TIP_TEXT = '分理解营销意图和用户特征，破圈探索更多的转化人群，提升跑量能力。';
export const AUTO_CREATIVE_TIP_TEXT = '让优质营销特色和内容脱颖而出，实时契合多样化用户消费心智，提升转化效果。';

// 工具函数：格式化 AccountStructureType 为 StructureInfo，添加 selected 和 id 字段
export function formatStructureTemplate(structureTemplate: AccountStructureType[]): StructureInfo[] {
    return structureTemplate.map(item => ({
        ...item,
        campaigns: item.campaigns.map(campaign => ({
            ...campaign,
            selected: true,
            adgroups: campaign.adgroups.map(adgroup => ({
                ...adgroup,
                id: adgroup.id || uniqueId(),
            })),
        })),
    }));
}

// 工具函数：提交前清理数据，移除内部使用的 id 字段
export function cleanupStructureForSubmit(
    campaigns: Array<StructureInfo['campaigns'][0]>
): Array<Omit<CampaignStructureType, 'selected'>> {
    return campaigns.map(campaign => ({
        ...campaign,
        campaignName: campaign.campaignName,
        adgroups: uniqBy(campaign.adgroups, 'adgroupName')
            .filter(adgroup => !!adgroup.adgroupName)
            .map(({id, ...adgroup}) => adgroup), // 移除 id 字段
    }));
}

export interface CampaignStructureType {
    selected?: boolean;
    campaignName: string;
    adgroups: Array<{id?: string, adgroupName: string}>;
}

export interface clueCampaignsType {
    campaignName: string;
    productCategoryType: number;
    featureSource: number;
}

export interface ProjectStructureType {
    budget: number;
    ocpcBid: number;
    ocpcBidType: number;
    projectName: string;
    transTypes: number[];
}


// 计划划分方式，本次新增，1- 以投放设备+经营业务划分，2 - 以经营业务划分，3 - 只生成品牌计划
export enum CampaignPartitionType {
    DEVICE_AND_BUSINESS = 1, // 以投放设备+经营业务划分
    BUSINESS = 2, // 以经营业务划分
    BRAND = 3, // 只生成品牌计划
}
export interface AccountStructureType {
    campaignPartitionType: CampaignPartitionType;
    templateId: number; // 账户结构模板ID
    templateName: string; // 账户结构模板名称
    suggestReason?: string; // 推荐理由
    project?: ProjectStructureType;
    campaigns: CampaignStructureType[];
    cplCampaignType?: clueCampaignsType;
}

interface AiBuildStructureProps extends AiProps {
    field: string;
    cardParameters: {
        keyInformation: any;
        structureTemplate: AccountStructureType[];
        allowPreviousStep?: boolean;
        previousStepField?: string;
    };
}

export function AiBuildStructureComp({
    disabled,
    readonly,
    structureTemplate,
    selectedTemplateId,
    setSelectedTemplateId,
    structureInfos,
    setStructureInfos,
    needDanubeCreative,
    setNeedDanubeCreative,
    needAutoTargeting = true,
    setNeedAutoTargeting,
    needAutoCreative = true,
    setNeedAutoCreative,
    showAutoSwitches = false,
}: {
    // 整体不可以编辑 样式是disabled态
    disabled: boolean;
    // 可以选择计划，但是里面的内容只读，样式是只读态
    readonly?: boolean;
    structureTemplate: AccountStructureType[];
    selectedTemplateId: number;
    setSelectedTemplateId: (templateId: number) => void;
    structureInfos: StructureInfo[];
    setStructureInfos: React.Dispatch<React.SetStateAction<StructureInfo[]>>;
    needDanubeCreative: boolean;
    setNeedDanubeCreative: (value: boolean) => void;
    needAutoTargeting?: boolean;
    setNeedAutoTargeting?: (value: boolean) => void;
    needAutoCreative?: boolean;
    setNeedAutoCreative?: (value: boolean) => void;
    showAutoSwitches?: boolean;
}) {
    const selectedStructureInfo =
        structureInfos.find(item => item.templateId === selectedTemplateId) || {} as StructureInfo;
    const onCampaignTemplateChange = useCallback(
        (campaignIndex, info: Partial<CampaignStructureType>) => {
            setStructureInfos(data => {
                return data.map(template => {
                    if (template.templateId === selectedTemplateId) {
                        return {
                            ...template,
                            campaigns: template.campaigns.map((campaign, index) => {
                                if (campaignIndex === index) {
                                    // 确保新的 adgroups 有 id
                                    const updatedInfo = {...info};
                                    if (info.adgroups) {
                                        updatedInfo.adgroups = info.adgroups.map(adgroup => ({
                                            ...adgroup,
                                            id: adgroup.id || uniqueId(),
                                        }));
                                    }
                                    return {
                                        ...campaign,
                                        ...updatedInfo,
                                    };
                                }
                                return campaign;
                            }),
                        };
                    }
                    return template;
                }) as StructureInfo[];
            });
        },
        [selectedTemplateId, setStructureInfos]
    );

    const isBrandStructure = selectedStructureInfo.campaignPartitionType === CampaignPartitionType.BRAND;

    const log = useLog();

    const advancedStyleSettingSwitchProps: SwitchProps = {
        checked: needDanubeCreative,
        onChange: (value: boolean) => {
            setNeedDanubeCreative(value);
            log('click', {
                level: 'ai-build-structure-advanced-style-switch',
                'extra_params': JSON.stringify({checked: value}),
            });
        },
        disabled: disabled || isBrandStructure,
    };

    const autoTargetingSwitchProps: SwitchProps = {
        checked: needAutoTargeting,
        onChange: (value: boolean) => {
            setNeedAutoTargeting?.(value);
            log('click', {
                level: 'ai-build-structure-auto-targeting-switch',
                'extra_params': JSON.stringify({checked: value}),
            });
        },
        disabled: disabled,
    };

    const autoCreativeSwitchProps: SwitchProps = {
        checked: needAutoCreative,
        onChange: (value: boolean) => {
            setNeedAutoCreative?.(value);
            log('click', {
                level: 'ai-build-structure-auto-creative-switch',
                'extra_params': JSON.stringify({checked: value}),
            });
        },
        disabled: disabled,
    };

    return (
        <>
            <RadioGroup
                distribution="even"
                disabled={disabled}
                value={selectedTemplateId}
                onChange={e => {
                    const templateId = e.target.value as number;
                    setSelectedTemplateId(templateId);
                    const template = structureTemplate.find(item => item.templateId === templateId);
                    log('click', {
                        level: 'ai-build-structure-template-select',
                        'extra_params': JSON.stringify({
                            templateId,
                            templateName: template?.templateName || '',
                        }),
                    });
                }}
            >
                {
                    structureTemplate.map((item, index) => {
                        return (
                            <RadioButton value={item.templateId} key={item.templateId}>
                                结构{index + 1}：
                                {item.templateName}
                            </RadioButton>
                        );
                    })
                }
            </RadioGroup>
            <AiBuildStructureInfo
                {...selectedStructureInfo}
                disabled={disabled || isBrandStructure}
                readonly={readonly}
                onChange={onCampaignTemplateChange}
            />

            <div className="advanced-switch-setting-group">
                <div className="advanced-switch-setting">
                    <span className="advanced-style-setting-title">
                        生成高级样式<StaticTip content={ADVANCE_CREATIVE_TIP_TEXT} />：
                    </span>
                    <Switch {...advancedStyleSettingSwitchProps} />
                </div>

                {
                    showAutoSwitches && (
                        <>
                            <div className="advanced-switch-setting">
                                <span className="advanced-style-setting-title">
                                    智能定向扩量<StaticTip content={AUTO_TARGETING_TIP_TEXT} />：
                                </span>
                                <Switch {...autoTargetingSwitchProps} />
                            </div>

                            <div className="advanced-switch-setting">
                                <span className="advanced-style-setting-title">
                                    自动创意<StaticTip content={AUTO_CREATIVE_TIP_TEXT} />：
                                </span>
                                <Switch {...autoCreativeSwitchProps} />
                            </div>
                        </>
                    )
                }
            </div>
        </>
    );
}

export type StructureInfo = AccountStructureType & {
    campaigns: Array<CampaignStructureType & {selected: boolean} & {
        adgroups: Array<{id: string, adgroupName: string}>;
    }>;
};

function AiBuildStructureBase({
    cardParameters,
    field,
    aiChat,
    instructions,
}: AiBuildStructureProps) {
    const {
        structureTemplate, keyInformation,
        allowPreviousStep, previousStepField,
    } = cardParameters;
    const [selectedTemplateId, setSelectedTemplateId] = useState(structureTemplate[0].templateId);
    const [structureInfos, setStructureInfos] = useState<StructureInfo[]>(
        formatStructureTemplate(structureTemplate)
    );
    const selectedStructureInfo = (
        structureInfos.find(item => item.templateId === selectedTemplateId) || {}
    ) as StructureInfo;

    const isBrandStructure = selectedStructureInfo.campaignPartitionType === CampaignPartitionType.BRAND;

    const [{setCompleted}, {completed, expired}] = useCardComplete(instructions, field);
    const [needDanubeCreative, setNeedDanubeCreative] = useState(true);
    const [needAutoTargeting, setNeedAutoTargeting] = useState(true);
    const [needAutoCreative, setNeedAutoCreative] = useState(true);
    const log = useLog();

    useEffect(() => {
        if (isBrandStructure) {
            setNeedDanubeCreative(false);
        }
    }, [isBrandStructure]);

    const onOk = async () => {
        log('click', {
            level: 'ai-build-structure-confirm',
            'extra_params': JSON.stringify({
                templateId: selectedTemplateId,
                templateName: selectedStructureInfo.templateName,
                campaignCount: selectedStructureInfo.campaigns.filter(item => item.selected).length,
                needDanubeCreative,
                needAutoTargeting,
                needAutoCreative,
            }),
        });

        const selectedCampaigns = cleanupStructureForSubmit(
            selectedStructureInfo.campaigns.filter(item => item.selected) as Array<StructureInfo['campaigns'][0]>
        );

        // 如果有某一个方案没有单元，则不提交
        if (selectedCampaigns.some(campaign => campaign.adgroups.length === 0)) {
            Toast.error({
                content: '请至少添加一个单元',
            });
            return;
        }

        const finalStructureInfo = {
            ...selectedStructureInfo,
            campaigns: selectedCampaigns,
            needDanubeCreative,
            needAutoTargeting,
            needAutoCreative,
        };

        await aiChat.pushMessage(
            'user',
            `采纳结构：${finalStructureInfo.templateName}，`
            + `${finalStructureInfo.project?.projectName ? `项目：${finalStructureInfo.project.projectName}，` : ''}`
            + finalStructureInfo.campaigns.map((item, index) => `方案${index + 1}：${item.campaignName}`).join('，'),
            {
                custom: {
                    currentField: field,
                    currentFieldOperate: OPERATION_TYPE.MOD,
                    currentFieldValue: [{
                        keyInformation,
                        accountStructure: finalStructureInfo,
                    }],
                },
                trigger: 'card',
            }
        );
        setCompleted();
    };
    const disabled = completed || expired;

    return (
        <div className="ai-build-structure">
            <AiBuildStructureComp
                disabled={disabled}
                structureTemplate={structureTemplate}
                selectedTemplateId={selectedTemplateId}
                setSelectedTemplateId={setSelectedTemplateId}
                structureInfos={structureInfos}
                setStructureInfos={setStructureInfos}
                needDanubeCreative={needDanubeCreative}
                setNeedDanubeCreative={setNeedDanubeCreative}
                needAutoTargeting={needAutoTargeting}
                setNeedAutoTargeting={setNeedAutoTargeting}
                needAutoCreative={needAutoCreative}
                setNeedAutoCreative={setNeedAutoCreative}
                showAutoSwitches
            />
            <div className="footer">
                {
                    completed
                        ? <Text disabled><IconCheck /> 已确认使用</Text>
                        : (
                            <Button
                                variant="primary"
                                disabled={
                                    selectedStructureInfo.campaigns.filter(i => i.selected).length === 0 || expired
                                }
                                onClick={onOk}
                            >
                                {expired ? '已失效' : '确认使用'}
                            </Button>
                        )
                }
                <PrevButton
                    disabled={disabled}
                    field={field}
                    allowPreviousStep={allowPreviousStep}
                    previousStepField={previousStepField}
                    aiChat={aiChat}
                />
            </div>
        </div>
    );
}

export default function AiBuildStructure(props: AiBuildStructureProps) {
    useRecordByComponent({tag: AI_BUILD_RECORD_CONFIG.AI_BUILD_STRUCTURE});
    return (
        <LogEmphasize source="ai-build">
            <AiBuildStructureBase {...props} />
        </LogEmphasize>
    );
}

interface AiBuildStructureInfoProps extends Pick<AccountStructureType, 'project' | 'campaigns' | 'suggestReason'> {
    disabled: boolean;
    readonly?: boolean;
    onChange: (index: number, info: Partial<CampaignStructureType>) => void;
}

const CAMPAIGN_WIDTH = 196;
function AiBuildStructureInfo({
    project, campaigns, suggestReason, disabled, readonly, onChange: onChange_,
}: AiBuildStructureInfoProps) {
    const log = useLog();

    return (
        <div>
            {project?.projectName && <div className="project-name">项目名称：{project?.projectName}</div>}
            <SliderWithPager variant="normal">
                {
                    campaigns.map((item, index) => {
                        const isSelected = !!item.selected;
                        const onChange = disabled ? noop : onChange_;
                        const cls = classNames('campaign-structure-item', {'disabled': disabled});
                        return (
                            // eslint-disable-next-line react/no-array-index-key
                            <div className={cls} key={index}>
                                <CampaignStructureCard
                                    {...item}
                                    title={`方案${index + 1}`}
                                    disabled={disabled}
                                    readonly={readonly}
                                    isSelected={isSelected}
                                    onChange={partial(onChange, index)}
                                />
                                <Checkbox
                                    checked={isSelected}
                                    onChange={e => {
                                        const checked = e.target.checked;
                                        onChange(index, {selected: checked});
                                        log('click', {
                                            level: 'ai-build-structure-campaign-select',
                                            'extra_params': JSON.stringify({
                                                campaignIndex: index,
                                                campaignName: item.campaignName,
                                                checked,
                                            }),
                                        });
                                    }}
                                    className="campaign-structure-item-checkbox"
                                />
                            </div>
                        );
                    }) as Array<KeyedReactNode<number>>
                }
            </SliderWithPager>
            {
                suggestReason && (
                    <Alert status="info" className="suggest-reason">
                        <Typography.Text strong>推荐理由：</Typography.Text>
                        <Typography.Markdown>{suggestReason}</Typography.Markdown>
                    </Alert>
                )
            }
        </div>
    );
}

interface CampaignStructureCardProps extends Pick<CampaignStructureType, 'campaignName' | 'adgroups'> {
    title?: ReactNode | string;
    isSelected: boolean;
    disabled: boolean;
    readonly?: boolean;
    onChange: (info: Partial<CampaignStructureType>) => void;
}
function CampaignStructureCard({
    title,
    campaignName,
    adgroups,
    isSelected = false,
    disabled = false,
    readonly = false,
    onChange: onChange_,
}: CampaignStructureCardProps) {
    const cls = classNames('campaign-structure-card', {'selected': isSelected, 'disabled': disabled});
    const onChange = readonly ? noop : onChange_;
    const log = useLog();

    return (
        <div className={cls}>
            {title && (
                <div className="title">
                    {title}
                </div>
            )}
            <div className="content">
                <div className="campaign-name-input-wrapper">
                    <Input
                        className="campaign-name-input"
                        disabled
                        onChange={e => {
                            const value = e.value;
                            onChange({campaignName: value});
                            log('click', {
                                level: 'ai-build-structure-campaign-name-edit',
                                'extra_params': JSON.stringify({campaignName: value}),
                            });
                        }}
                        value={campaignName}
                        style={{width: CAMPAIGN_WIDTH}}
                    />
                </div>
                <AdgroupInputList
                    disabled={disabled || readonly}
                    readonly={readonly}
                    adgroups={adgroups as Array<{id: string, adgroupName: string}>}
                    onChange={onChange}
                    width={CAMPAIGN_WIDTH}
                />

            </div>
        </div>
    );
}
