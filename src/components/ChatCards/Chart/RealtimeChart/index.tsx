import React, {useMemo, useRef, memo, useEffect} from 'react';
import {useRequestCallback} from 'huse';
import {Line} from '@baidu/one-charts';
import {use} from 'echarts/core';
import {DataZoomComponent} from 'echarts/components';
import {Loading} from '@baidu/one-ui';
import {fetchRealTimeAccountAndOverview} from '@/api/getReportData';
import {useRealtimeGlobalStore} from '@/hooks/store/realtimeData';
import {SdkFilterProps} from '@/interface/report';
import {EmptyChart} from '../empty';
import {ChartPayload} from '../interface';
import {columnsConfig} from './config';
import {getToolTip, getAccountCardAndChartDataFromApiData} from './util';
import './index.global.less';

use([DataZoomComponent]);

interface SingleLineChartProps extends ChartPayload {
    filterProps: SdkFilterProps;
}

const RealtimeChartComp: React.FC<SingleLineChartProps> = ({filterProps}) => {
    const lineChartRef = useRef();
    const intervalRef = useRef(null);

    // 新增支持筛选设备控制趋势图表
    const {device} = filterProps || {};
    const requestParams = useMemo(() => {
        // 0:全部，1:计算机，2:移动
        return device ? {
            filters: [{
                column: 'device',
                operator: 'IN',
                values: [device],
            }],
        } : {};
    }, [device]);

    const [
        getOverviewData,
        {data, pending},
    ] = useRequestCallback(
        fetchRealTimeAccountAndOverview, requestParams
    );
    const {setRealtimeData} = useRealtimeGlobalStore();
    useEffect(
        () => {
            getOverviewData();
            const clearRefresh = () => {
                if (intervalRef.current) {
                    clearInterval(intervalRef.current);
                    intervalRef.current = null;
                }
            };
            intervalRef.current = setInterval(() => {
                getOverviewData();
            }, 60000);
            return clearRefresh;
        },
        [getOverviewData]
    );
    useEffect(
        () => {
            setRealtimeData(data);
        },
        [data]
    );
    const [
        chartData = [],
        chartDataObj = {},
    ] = useMemo(
        () => getAccountCardAndChartDataFromApiData(data),
        [data]
    );

    const tooltipFormatter = (params: any) => {
        return getToolTip(params, null, chartDataObj);
    };

    const xAxisData = chartData.map((item: any) => item.date);

    const seriesData = columnsConfig.map(({key, label, lineStyle}) => {
        return {
            name: label,
            type: 'line',
            data: chartData.map((item: any) => (item[key] === null ? '-' : Number(item[key]))),
            lineStyle,
            emphasis: {itemStyle: lineStyle},
        };
    });

    const options = {
        legend: {
            show: false,
        },
        xAxis: {
            data: xAxisData,
        },
        series: seriesData,
        tooltip: {
            trigger: 'axis',
            formatter: tooltipFormatter,
        },
    };

    return (
        <div className="realtime-chart-container">
            {
                pending
                    ? <Loading className="line-chart" />
                    : (
                        chartData.length
                            ? <Line ref={lineChartRef} options={options} />
                            : <EmptyChart style={{height: '100%', backgroundColor: 'transparent'}} />
                    )
            }
        </div>
    );
};

const RealtimeChart = memo(RealtimeChartComp);
export default RealtimeChart;
