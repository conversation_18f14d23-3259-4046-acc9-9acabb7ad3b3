import {IconChartStarSolid, IconFileAddSolid, IconFileStarSolid, IconShieldStarSolid} from 'dls-icons-react';
import {CONTENT_CONFIG, getHomepageCreateSugs, PROMPT_CONFIG} from '@/config/prompt';
import {PageType} from '@/dicts/pageType';
import {getUserId} from '@/utils';
import {SugAction} from '@/utils/aiChat';
import {sendMonitor} from '@/utils/logger';
import {isAiBuildUser, isFlashLinkUser} from '@/utils/getFlag';
import {DIAGNOSIS_TABS_ENUM} from '@/modules/Ad/OptCenter/AdvertiseDiagnosis/tabsContent';
export const getDefaultSugs = () => {
    if (isFlashLinkUser()) {
        if (!isAiBuildUser()) {
            return [];
        }
        return [
            {
                content: CONTENT_CONFIG.AI_BUILD_ENTRY,
                prompt: PROMPT_CONFIG.AI_BUILD,
                trigger: 'homepage',
            },
            {
                content: CONTENT_CONFIG.AI_BUILD_REFRESH,
                prompt: PROMPT_CONFIG.AI_BUILD_REFRESH,
                trigger: 'homepage',
            },
        ];
    }

    return [{
        content: PROMPT_CONFIG.CREATE,
        prompt: PROMPT_CONFIG.CREATE,
        trigger: 'homepage',
        children: getHomepageCreateSugs(),
    }, {
        content: PROMPT_CONFIG.VIEW_MY_CAMPAIGNS_1,
        prompt: PROMPT_CONFIG.VIEW_MY_CAMPAIGNS_1,
        trigger: 'homepage',
        type: SugAction.CALLBACK,
        tagProps: {variant: 'bubble-button'},
        callback: async () => {
            sendMonitor('click', {level: 'homepage', field: 'view_my_campaigns'});
            const r = await import('@/modules/Ad/routes');
            const url = r.default.getPathByName?.(PageType.CampaignList);
            url && window.open(url + '?userId=' + getUserId(), '_self');
        },
        once: false,
    }, {
        key: 'diagnosisIssue',
        content: PROMPT_CONFIG.DIAGNOSE_FLUCTUATIONS,
        prompt: PROMPT_CONFIG.DIAGNOSE_FLUCTUATIONS,
        api: 'diagnosisIssue',
        params: JSON.stringify({source: 'SUG'}),
        type: SugAction.CALLBACK,
        tagProps: {variant: 'bubble-button'},
        trigger: 'homepage',
        callback: async () => {
            sendMonitor('click', {level: 'homepage', field: 'diagnose_fluctuations'});
            const r = await import('@/modules/Ad/routes');
            const url = r.default.getPathByName?.(PageType.AdvertiseDiagnosis);
            url && window.open(`${url}?userId=${getUserId()}&tab=${DIAGNOSIS_TABS_ENUM.CONSUME_FLUCTUATION}`, '_self');
        },
    }, {
        content: PROMPT_CONFIG.VIEW_REPORT,
        prompt: PROMPT_CONFIG.VIEW_REPORT,
        trigger: 'homepage',
    }];
};

interface Card {
    icon: React.ReactNode;
    title: string;
    desc: string;
    prompt: string;
    visible?: (params?: any) => boolean | boolean;
}

export const defaultCards: Card[] = [
    {
        icon: <IconFileAddSolid />,
        title: '创建提示词广告',
        desc: '对话式交互，轻松高效的完成广告搭建',
        prompt: '新建提示词方案',
    },
    {
        icon: <IconShieldStarSolid />,
        title: '智能诊断',
        desc: '智能分析+归因+建议，一站式解决账户投放效果问题',
        prompt: '消费波动诊断',
    },
    {
        icon: <IconChartStarSolid />,
        title: '数据报告智能解读',
        desc: '智能解析多维数据，追踪投放效果，高效决策',
        prompt: '查看最近7日的投放效果分析',
    },
    {
        icon: <IconFileStarSolid />,
        title: '智能搭建',
        desc: '上传营销资料/选择项目，协助您智能搭建或翻新账户/项目',
        prompt: '智能翻新',
        visible: isAiBuildUser,
    },
];
