import {useMemo, useEffect, useState, use<PERSON><PERSON>back, Dispatch, SetStateAction} from 'react';
import {v4 as uuidv4} from 'uuid';
import {PageTips} from '@baidu/one-ui-pro';
import {Skeleton} from '@baidu/light-ai-react';
import stringifyJSON from 'fast-json-stable-stringify';
import {SceneType} from '@/modules/Ad/config';
import globalData from '@/utils/globalData';
import {useStreamRequest} from '@/hooks/request';
import {PRODUCT_TYPE_CONFIG, ReportType} from '@/config/report';
import {sendMonitor} from '@/utils/logger';
import {useSlot} from '@/hooks/slot';
import {Empty} from '@/modules/Ad/ManageCenter/common/Feedback';
import {GUI_REPORT_CONFIG} from '@/components/ChatCards/ReportGui/config';
import {ChatResult} from '../AiChat/interface';
import GuiController from './reportController';
import ChatCards, {CardType, Card} from './Cards';
import {getReportGUICardsByFilterProps} from './util';
import {ReportOuterContext, useReportGuiControllerParams} from './context';
import './index.global.less';

interface AiReportSDKProps {
    prompt: string;
    reportConfigs?: {
        [reportType: number]: {
            hasSummary?: boolean;
        };
    };
    aiChat: {
        pushMessage: (role: 'user', prompt: string, info: Record<string, any>) => Promise<void>;
    };
    slots?: {
        [slotName: string]: React.ComponentType<any>;
    };
    hairuoConfig?: {
        userId?: number;
        token?: number;
    };
}

export function AiReportSDK({
    prompt,
    reportConfigs: localReportConfigs = {},
    aiChat,
    slots,
    hairuoConfig,
}: AiReportSDKProps) {
    useEffect(
        () => {
            if (hairuoConfig) {
                globalData.set({hairuoConfig});
            }
        },
        [hairuoConfig]
    );
    useEffect(
        () => {
            sendMonitor('report', {event: 'report_gui_sum'});
        },
        []
    );
    const [guiPlatform, setGuiPlatform] = useState<number | undefined>(PRODUCT_TYPE_CONFIG.ACCOUNT);
    const [contextReportType, setContextReportType] = useState<number>();
    const extraPayload = globalData.get('extraPayload') || {};
    const {interactStorage = {}} = extraPayload;
    const [uuid, refreshUuid] = useState(uuidv4());
    const refreshGui = useCallback(
        () => refreshUuid(uuidv4()),
        [refreshUuid]
    );
    const sessionId = `AIX-${uuid}`;
    const {data, pending, done, error} = useStreamRequest<ChatResult>({
        path: 'torch/GET/DirectorSseService/aiChat',
        params: {
            source: 100,
            sessionId,
            question: {
                text: prompt,
                payload: {
                    scene: SceneType.REPORT,
                    interactStorage,
                    parameters: {
                        guiReport: true,
                        guiPlatform,
                    },
                },
                trigger: 'gui_center',
            },
        },
    });

    const {cards: reportGUICards} = useMemo(
        () => {
            if (!done) {
                return {cards: []};
            }
            return {cards: data?.answer.cards || []};
        },
        [data, done]
    );

    const slotProps = useMemo(() => ({slots}), [slots]);
    const Slot = useSlot(slotProps);

    if (data?.scene === SceneType.NOT_SUPPORT) {
        return (
            <div className="report-GUI">
                <Empty />
            </div>
        );
    }

    if (pending || !reportGUICards.length) {
        return (
            <div className="report-GUI">
                <Skeleton active />
            </div>
        );
    }

    if (error) {
        return (
            <div className="report-GUI">
                <PageTips.Error />
            </div>
        );
    }

    const reportGUIController = reportGUICards.find(item => item.type === CardType.ReportGuiController);
    const guiReportParams = reportGUIController?.payload?.guiReportParams || {};
    const reportGUICards_ = reportGUICards.filter(item => item.type !== CardType.ReportGuiController);
    const reportType = contextReportType || guiReportParams.reportType;
    const {name} = GUI_REPORT_CONFIG[(reportType as ReportType)];

    return (
        <div className="report-GUI">
            <div className="ai-report-sdk-container">
                <ReportOuterContext.Provider
                    value={{
                        reportType: contextReportType,
                        setReportType: setContextReportType,
                    }}
                >
                    <Slot name="reportGUIMenu" title={name} />
                    <div className="sdk-content">
                        <GuiController
                            setGuiPlatform={setGuiPlatform}
                            refreshGui={refreshGui}
                            guiReportParams={guiReportParams}
                            reportType={reportType}
                        >
                            <RenderCards
                                reportGUICards={reportGUICards_ as Card[]}
                                aiChat={aiChat}
                                guiPlatform={guiPlatform}
                                setGuiPlatform={setGuiPlatform}
                                reportType={reportType}
                            />
                        </GuiController>
                    </div>
                </ReportOuterContext.Provider>
            </div>
        </div>
    );
}

function RenderCards({reportGUICards, aiChat, guiPlatform, setGuiPlatform, reportType}: {
    reportGUICards: Card[];
    aiChat: AiReportSDKProps['aiChat'];
    guiPlatform?: PRODUCT_TYPE_CONFIG;
    setGuiPlatform: Dispatch<SetStateAction<PRODUCT_TYPE_CONFIG | undefined>>;
    reportType: ReportType;
}) {
    const {
        dateRange, campaignIds, projectIds, timeUnit,
        productLineList, videoIdeaType, projectType, device,
    } = useReportGuiControllerParams();
    const filterProps = useMemo(
        () => ({dateRange, campaignIds, projectIds, timeUnit, productLineList, videoIdeaType, projectType, device}),
        [dateRange, campaignIds, projectIds, timeUnit, productLineList, videoIdeaType, projectType, device]
    );
    const normalizedReportGUICards = useMemo(
        () => getReportGUICardsByFilterProps({reportGUICards, filterProps, reportType}),
        [reportGUICards, filterProps, reportType]
    );
    return (
        <div className="sdk-card-content">
            {
                normalizedReportGUICards.map(card => {
                    const key = stringifyJSON(card);
                    const {type, payload: props} = card;
                    const AIXCard = ChatCards[type];
                    const aixCardProps = {
                        ...props,
                        aiChat,
                        filterProps,
                        ...(type === CardType.AssembleReport ? {guiPlatform, setGuiPlatform} : {}),
                    };
                    return (<AIXCard key={key} {...aixCardProps} />);
                })
            }
        </div>
    );
}

export {default as ChatCards} from './Cards';
