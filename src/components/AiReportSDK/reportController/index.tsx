/* eslint-disable max-statements */
import {Dispatch, SetStateAction, useState, useMemo, ReactNode, useEffect} from 'react';
import {
    Dropdown,
    Button,
    ProviderConfig,
    DropdownButtonProps,
    ButtonProps,
} from '@baidu/one-ui';
import {Filters} from '@baidu/one-ui-pro';
import {IconSync} from 'dls-icons-react';
import {useRequest, useRequestCallback} from 'huse';
import dayjs from 'dayjs';
import {timeUnitOptions as DEFAULT_TIME_OPTIONS, ReportType, PRODUCT_TYPE_CONFIG} from '@/config/report';
import {GUI_REPORT_CONFIG} from '@/components/ChatCards/ReportGui/config';
import {dateFormat} from '@/config/date';
import {fetchAllCampaigns} from '@/api/getCampaign';
import {fetchAllProjects} from '@/api/getProject';
import {PRODUCT, PRODUCT_NAME} from '@/dicts/campaign';
import {getUserId} from '@/utils';
import DateSwitch, {DateSwitchProps} from '../../ChatCards/Chart/dateSwitch';
import {ReportGuiControllerContext} from '../context';
import {
    getProductFilterOptions,
    projectTypeOptions,
    projectTypeValues,
    projectTypeValueToNameMap,
    videoIdeaTypeOptions,
    videoIdeaTypeValues,
    videoIdeaTypeValueToNameMap,
    deviceOptions,
    deviceValues,
    deviceValueToNameMap,
} from './util';
import './index.global.less';

interface Props {
    setGuiPlatform: Dispatch<SetStateAction<number | undefined>>;
    refreshGui: () => void;
    reportType: ReportType;
    guiReportParams: {
        reportType: ReportType;
        startDate?: string;
        endDate?: string;
        timeUnit?: string;
        campaignId?: number;
        projectId?: number;
        videoIdeaType?: number;
        device?: number;
    };
    children: ReactNode;
}

/* eslint complexity: ["error", 30] */
function ReportGuiController({reportType, refreshGui, guiReportParams, children, setGuiPlatform}: Props) {
    const REPORT_CONFIG = GUI_REPORT_CONFIG[reportType];
    const {
        // name,
        timeUnitOptions,
        showCampaignFilter = true,
        showProjectFilter = false,
        showProductFilter = false,
        showDatePicker = true,
        showVideoIdeaTypeFilter = false,
        showProjectTypeFilter = false,
        showDeviceFilter = false,
        supportProductList = [],
    } = REPORT_CONFIG;
    const [campaignIds, setCampaignIds] = useState(guiReportParams.campaignId ? [guiReportParams.campaignId] : []);
    const [projectIds, setProjectIds] = useState(guiReportParams.projectId ? [guiReportParams.projectId] : []);
    const [timeUnit, setTimeUnit] = useState(guiReportParams.timeUnit || (timeUnitOptions && timeUnitOptions[0].value));
    const [videoIdeaType, setVideoIdeaType] = useState(
        guiReportParams.videoIdeaType || videoIdeaTypeValues.all
    );
    const [projectType, setProjectType] = useState(projectTypeValues.fast);
    const [device, setDevice] = useState(guiReportParams.device || deviceValues.all);
    const [dateRange, setDateRange] = useState({
        startDate: guiReportParams.startDate || dayjs().format(dateFormat),
        endDate: guiReportParams.endDate || dayjs().format(dateFormat),
    });
    const [selectProductLine, setSelectProductLine] = useState<PRODUCT[]>(() => {
        if (!showProductFilter) {
            return [];
        }
        if (guiReportParams.aixProduct) {
            return guiReportParams.aixProduct;
        }
        if (guiReportParams.guiPlatform) {
            return [guiReportParams.guiPlatform];
        }
        const productLineList = getProductFilterOptions(reportType);
        const LocalProductSelected = localStorage.getItem(`ProductSelected_${getUserId()}`);
        // 创意视频报告，默认选对应的产品线
        if (reportType === ReportType.VIDEO_FC) {
            return [PRODUCT_TYPE_CONFIG.FC];
        }
        if (reportType === ReportType.VIDEO_FEED) {
            return [PRODUCT_TYPE_CONFIG.FEED];
        }
        if (LocalProductSelected && productLineList.find(item => item.value === +LocalProductSelected)) {
            return [+LocalProductSelected];
        }
        const defaultProductLine = productLineList[0]?.value;
        if (defaultProductLine !== PRODUCT_TYPE_CONFIG.ACCOUNT) {
            return [defaultProductLine];
        }
        return [];
    });
    const filterProps = useMemo(
        () => ({
            dateRange,
            campaignIds,
            projectIds,
            timeUnit,
            videoIdeaType,
            projectType,
            device,
            productLineList: selectProductLine,
        }),
        [dateRange, campaignIds, projectIds, timeUnit, selectProductLine, videoIdeaType, projectType, device]
    );
    // one-ui-pro Filter组件value不能不受控，设置一个临时变量selectCampaignList
    const [selectFilterItem, setSelectFilterItem] = useState(showProjectFilter ? projectIds : campaignIds);
    const {
        data: {
            qinggeCampaignList = [],
            fcCampaignList = [],
            feedCampaignList = [],
            fcCplCampaignList = [],
            feedCplCampaignList = [],
        } = {},
    } = useRequest(fetchAllCampaigns, {});
    const [fetchProjectList, {
        data: {
            fcProjectList = [],
            feedProjectList = [],
        } = {},
    }] = useRequestCallback(fetchAllProjects, {});
    const allCampaignList = useMemo(
        () => ([
            ...qinggeCampaignList,
            ...fcCampaignList,
            ...feedCampaignList,
            ...fcCplCampaignList,
            ...feedCplCampaignList,
        ]),
        [
            qinggeCampaignList,
            fcCampaignList,
            feedCampaignList,
            fcCplCampaignList,
            feedCplCampaignList,
        ]
    );
    const allProjectList = [...fcProjectList, ...feedProjectList];
    useEffect(() => {
        if ([ReportType.FC_PROJECT, ReportType.FEED_PROJECT].includes(reportType)) {
            fetchProjectList();
        }
    }, [fetchProjectList, reportType]);
    const {startDate, endDate} = dateRange;

    const productOptions = getProductFilterOptions(reportType);
    const productDropDownProps: DropdownButtonProps = {
        options: productOptions,
        title: `投放产品线：${selectProductLine.length ? selectProductLine.map(item => PRODUCT_NAME[item]) : '全部'}`,
        className: 'qingge-product-drop-down',
        size: 'small',
        handleMenuClick: (e: any) => {
            if (+e.key === PRODUCT_TYPE_CONFIG.ACCOUNT) {
                setSelectProductLine([]);
            } else {
                setSelectProductLine([+e.key]);
            }
            setCampaignIds([]);
            setProjectIds([]);
            setSelectFilterItem([]);
            localStorage.setItem(`ProductSelected_${getUserId()}`, e.key);
        },
    };

    const splitTimeOptions = timeUnitOptions || DEFAULT_TIME_OPTIONS;
    const splitDropDownProps: DropdownButtonProps = {
        value: timeUnit,
        options: splitTimeOptions,
        title: `时间：${splitTimeOptions.find(item => item.value === timeUnit)?.label}`,
        className: 'split-report',
        size: 'small',
        handleMenuClick: (e: any) => setTimeUnit(e.key),
        width: 120,
    };

    const campaignList = useMemo(
        () => {
            if (selectProductLine.length) {
                return allCampaignList.filter(item => item.aixProduct?.[0] === selectProductLine[0]);
            }
            return allCampaignList;
        },
        [selectProductLine, allCampaignList]
    );
    const campaignFilterProps = {
        value: selectFilterItem,
        filterType: 'enum',
        options: campaignList.map(item => ({label: item.campaignName, value: item.campaignId})),
        className: 'gui-campaign-filter-list',
        onChange: (e: any) => setSelectFilterItem(e.value),
        onConfirm: (e: any) => setCampaignIds(e.value),
    };

    const videoIdeaTypeDropdownProps: DropdownButtonProps = {
        value: videoIdeaType,
        // @ts-ignore
        options: videoIdeaTypeOptions,
        title: `素材类型：${videoIdeaTypeValueToNameMap[videoIdeaType]}`,
        className: 'qingge-product-drop-down',
        size: 'small',
        handleMenuClick: (e: any) => {
            setVideoIdeaType(e.key);
            setCampaignIds([]);
            setProjectIds([]);
            setSelectFilterItem([]);
        },
    };

    const projectTypeDropDownProps: DropdownButtonProps = {
        value: projectType,
        options: projectTypeOptions,
        title: `方案类型：${projectTypeValueToNameMap[projectType]}`,
        className: 'qingge-product-drop-down',
        size: 'small',
        handleMenuClick: (e: any) => {
            setProjectType(e.key);
            setCampaignIds([]);
            setProjectIds([]);
            setSelectFilterItem([]);
        },
    };

    const projectList = useMemo(
        () => {
            if (selectProductLine.length) {
                return allProjectList.filter(item => item.aixProduct?.[0] === selectProductLine[0]);
            }
            return allProjectList;
        },
        [selectProductLine, allProjectList]
    );
    const projectFilterProps = {
        value: selectFilterItem,
        onChange: (e: any) => setSelectFilterItem(e.value),
        filterType: 'enum',
        options: projectList.map(item => ({label: item.projectName, value: item.projectId})),
        className: 'gui-campaign-filter-list',
        onConfirm: (e: any) => setProjectIds(e.value),
    };

    const campaignTitleRender = {
        campaignIds,
        campaignList,
        selectProductLine,
    };
    const campaignListProps: DropdownButtonProps = {
        title: <CampaignTitleRender {...campaignTitleRender} />,
        overlay: <Filters {...campaignFilterProps} />,
        size: 'small',
    };
    const projectListProps: DropdownButtonProps = {
        title: <ProjectTitleRender projectIds={projectIds} projectList={projectList} />,
        overlay: <Filters {...projectFilterProps} />,
        size: 'small',
    };

    const dateSwitchProps: DateSwitchProps = {
        value: {startDate, endDate},
        onChange: (value: any) => {
            setDateRange(value);
        },
    };

    const deviceDropdownProps: DropdownButtonProps = {
        options: deviceOptions.map(option => ({
            label: option.label,
            value: String(option.value),
        })),
        title: `推广设备：${deviceValueToNameMap[device] || '全部'}`,
        size: 'small',
        handleMenuClick: (e: any) => {
            setDevice(+e.key);
        },
    };

    const refreshButtonProps: ButtonProps = {
        size: 'small',
        icon: <IconSync />,
        onClick: refreshGui,
    };

    return (
        <ProviderConfig theme="light-ai">
            <div className="report-controller-filter-area">
                {/* <div className="filter-area-title">
                    <span className="title">{REPORT_CONFIG.name}</span>
                    <Dropdown.Button {...filterReportProps} size="medium" />
                </div> */}
                <div className="filter-area-content">
                    {timeUnitOptions && <Dropdown.Button {...splitDropDownProps} size="medium" />}
                    {showProductFilter
                        && !supportProductList.includes(PRODUCT_TYPE_CONFIG.ACCOUNT)
                        && <Dropdown.Button {...productDropDownProps} size="medium" />}
                    {showVideoIdeaTypeFilter
                        && <Dropdown.Button {...videoIdeaTypeDropdownProps} size="medium" />}
                    {showProjectTypeFilter
                        && <Dropdown.Button {...projectTypeDropDownProps} size="medium" />}
                    {showDeviceFilter
                        && <Dropdown.Button {...deviceDropdownProps} size="medium" />}
                    {showCampaignFilter && <Dropdown.Button {...campaignListProps} size="medium" />}
                    {showProjectFilter && <Dropdown.Button {...projectListProps} size="medium" />}
                    {showDatePicker && <DateSwitch {...dateSwitchProps} size="medium" />}
                    <Button {...refreshButtonProps} size="medium" />
                </div>
            </div>
            <ReportGuiControllerContext.Provider value={filterProps}>
                {children}
            </ReportGuiControllerContext.Provider>
        </ProviderConfig>
    );
}

function CampaignTitleRender({campaignIds, campaignList, selectProductLine}: {
    campaignIds: number[];
    campaignList: any[];
    selectProductLine?: PRODUCT[];
}) {
    const isSelectedAll = campaignIds.length === campaignList.length || campaignIds.length === 0;
    const firstCampaignName = campaignList.find(
        c => c.campaignId === campaignIds[0])?.campaignName || '';
    if (isSelectedAll) {
        return (
            <div>
                全部营销方案
            </div>
        );
    }
    return (
        <div>
            范围：<span className="campaign-filter-title">{firstCampaignName}</span>等{campaignIds.length}个营销方案
        </div>
    );
}

function ProjectTitleRender({projectIds, projectList}: {projectIds: number[], projectList: any[]}) {
    const isSelectedAll = projectIds.length === projectList.length || projectIds.length === 0;
    const firstProjectName = projectList.find(
        c => c.projectId === projectIds[0])?.projectName || '';
    if (isSelectedAll) {
        return <div>全部项目</div>;
    }
    return (
        <div>
            范围：<span className="campaign-filter-title">{firstProjectName}</span>等{projectIds.length}个项目
        </div>
    );
}

export default ReportGuiController;
