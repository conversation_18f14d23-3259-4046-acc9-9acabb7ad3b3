import {invert, mapKeys} from 'lodash-es';
import {PRODUCT_TYPE_CONFIG} from '@/config/report';
import {PLATFORM_ENUM} from '@/dicts/index';
import {ReportType} from '@/config/report';
import {
    GUI_REPORT_CONFIG,
} from '@/components/ChatCards/ReportGui/config';
import {isFcCplUser, isFeedCplUser, isRfqUser} from '@/utils/getFlag';
import {UC_TO_PLATFORM, UC_PRODUCT} from '@/dicts/index';
import globalData from '@/utils/globalData';
import {videoIdeaTypeNameMap, videoIdeaTypeValues} from './pureConfig';

export function getProductFilterOptions(reportType: ReportType) {
    const {hasAuthAppIds} = globalData.get('aixAccountInitInfo');
    const supportAixProduct = hasAuthAppIds.filter((
        p: UC_PRODUCT) => UC_PRODUCT[p]).map((p: UC_PRODUCT) => UC_TO_PLATFORM[p]);
    const {supportProductList = [], productFilterConflict} = GUI_REPORT_CONFIG[reportType];
    const productList: Array<{label: string, value: PRODUCT_TYPE_CONFIG, visible: boolean}> = [
        {
            label: '搜索推广',
            value: PRODUCT_TYPE_CONFIG.FC,
            visible: supportProductList.includes(PRODUCT_TYPE_CONFIG.FC),
        },
        {
            label: '信息流推广',
            value: PRODUCT_TYPE_CONFIG.FEED,
            visible: supportProductList.includes(PRODUCT_TYPE_CONFIG.FEED)
                && supportAixProduct.includes(PLATFORM_ENUM.FEED),
        },
        {
            label: '爱采购加油推',
            value: PRODUCT_TYPE_CONFIG.B2B_PROMOTION,
            visible: supportProductList.includes(PRODUCT_TYPE_CONFIG.B2B_PROMOTION),
        },
        {
            label: '搜索服务直达',
            value: PRODUCT_TYPE_CONFIG.FC_CPL,
            visible: supportProductList.includes(PRODUCT_TYPE_CONFIG.FC_CPL)
                && isFcCplUser(),
        },
        {
            label: '信息流服务直达',
            value: PRODUCT_TYPE_CONFIG.FEED_CPL,
            visible: supportProductList.includes(PRODUCT_TYPE_CONFIG.FEED_CPL)
                && isFeedCplUser(),
        },
        {
            label: '线索加油包',
            value: PRODUCT_TYPE_CONFIG.RFQ,
            visible: supportProductList.includes(PRODUCT_TYPE_CONFIG.RFQ)
                && isRfqUser(),
        },
    ];

    const options = productList.filter(product => product.visible).map(product => ({
        label: product.label,
        value: product.value,
    }));
    if ((options.length > 1 && !productFilterConflict)
        || supportProductList.includes(PRODUCT_TYPE_CONFIG.ACCOUNT)) {
        options.unshift({
            label: '全部',
            value: PRODUCT_TYPE_CONFIG.ACCOUNT,
        });
    }
    return options;
}

export * from './pureConfig';

export const videoIdeaTypeValueToNameMap = mapKeys(videoIdeaTypeNameMap, (value, key) => {
    return videoIdeaTypeValues[key as keyof typeof videoIdeaTypeValues];
});

export const videoIdeaTypeOptions = [
    {label: videoIdeaTypeNameMap.all, value: videoIdeaTypeValues.all},
    {label: videoIdeaTypeNameMap.custom, value: videoIdeaTypeValues.custom},
    {label: videoIdeaTypeNameMap.auto, value: videoIdeaTypeValues.auto},
];

export const projectTypeValues = {
    standard: '1',
    fast: '2',
};

export const projectTypeValueToNameMap = {
    [projectTypeValues.fast]: '极速版',
    [projectTypeValues.standard]: '标准版',
};

export const projectTypeOptions = [
    {label: projectTypeValueToNameMap[projectTypeValues.fast], value: projectTypeValues.fast},
    {label: projectTypeValueToNameMap[projectTypeValues.standard], value: projectTypeValues.standard},
];

export const projectValueToKeyMap = invert(projectTypeValues);

export const deviceValues = {
    all: 0,
    pc: 1,
    mobile: 2,
};

export const deviceOptions = [
    {label: '全部', value: deviceValues.all},
    {label: '计算机', value: deviceValues.pc},
    {label: '移动', value: deviceValues.mobile},
];

export const deviceValueToNameMap = {
    [deviceValues.all]: '全部',
    [deviceValues.pc]: '计算机',
    [deviceValues.mobile]: '移动',
};
