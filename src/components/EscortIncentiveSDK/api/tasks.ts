import {pick} from 'lodash-es';
import {request} from '@/utils/ajax';
import {TaskIdEnum, TaskStatusEnum} from '../config';
import {getInvertTime} from '../utils';
import {TaskBasicInfo, SubTaskDetailInfo, TaskDetailData} from '../interface/task';

export async function fetchTaskInfo(params: {isHistory: boolean}) {
    return request<TaskBasicInfo[]>(
        'optcenter/GET/InspireTaskService/queryOutline',
        params,
    );
}

export async function onAddTask({taskId}: {taskId: TaskIdEnum}) {
    return request<TaskBasicInfo[]>(
        'optcenter/GET/InspireTaskService/addTask',
        {taskId},
    );
}

interface TaskDetailInfo {
    taskId: TaskIdEnum;
    status: TaskStatusEnum;
    startTime: string;
    /**
     * 任务领取时间
     */
    userStartTime: string;
    endTime: string;
    /**
     * 任务周期; 比如：任务倒计时，通过此字段和userStartTime一起得出
     */
    taskDuration: number;
    /**
     * 当前任务的奖励上限；
     */
    rewardLimit: number;
    /**
     * 当前任务获得的奖励值
     */
    rewards: number;
    /**
     * 当前任务已完成值
     */
    indicatorCurrent: number;
    /**
     * 当前任务的目标值
     */
    taskIndicator: number;
    /**
     * 当前任务的预估可达成值
     */
    estimateIndicator: number;
    title: string;
    desc: string;
    subTasks: SubTaskDetailInfo[];
    /**
     * 同行采纳比例
     */
    competition: number;
    reportData?: Record<string, number>;
    extConfig?: Record<string, any>;
    userTaskUpdateTime?: string;
    rewardsData: Record<string, any>;
}

export async function queryTaskDetail({taskId}: {taskId: TaskIdEnum}) {
    const [result] = await request<TaskDetailInfo[]>(
        'optcenter/GET/InspireTaskService/queryDetail',
        {taskId}
    );

    // todo: 接口字段后续统一调整
    const data = pick(
        result,
        [
            'taskId', 'reportData', 'subTasks', 'extConfig', 'rewardLimit', 'userTaskUpdateTime', 'userStartTime',
            'taskDuration', 'rewardsData', 'rewardType', 'endTime', 'estimateData',
        ]
    ) as TaskDetailData;

    if (result.title || result.desc) {
        data.reason = {title: result.title, desc: result.desc};
    }
    data.currentRewardsCount = result.rewardsData?.rewards ?? result.rewards;
    data.daysRemaining = getInvertTime({
        userStartTime: result.userStartTime,
        taskDuration: result.taskDuration,
        endTime: result.endTime,
    });
    data.taskCurrentValue = result.indicatorCurrent;
    data.taskTargetValue = result.taskIndicator;
    data.taskEstimateValue = result.estimateIndicator;
    return data;
}

interface DownloadAdviceInfo {
    adviceId: number;
    adviceKey: string;
    acceptInfo: {
        downloadUrl: string;
    };
}

export async function onDownloadOptAdvice({adviceKey}: {
    adviceKey: string;
}) {
    return request<DownloadAdviceInfo>(
        'optcenter/GET/AdviceService/downloadAdvice',
        {
            operationType: 6,
            adviceKey
        },
    );
}
