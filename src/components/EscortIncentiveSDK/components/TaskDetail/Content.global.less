@import 'src/styles/mixin/mobile.global.less';

.task-detail-container {
    .one-ai-drawer-wrapper-body-container {
        background: linear-gradient(195.31deg, #FDF6D3 2.8%, #FFFEFA 31.49%);
    }

    .one-ai-drawer-header {
        background: none;
    }

    .unaccept-task-detail-button {
        padding: 14px 100px;
    }

    .task-detail {
        display: flex;
        flex-direction: column;
        gap: calc(16 * var(--base-unit));

        .task-detail-container-title {
            display: flex;
            justify-content: space-between;
            border: 1px solid #FFF;
            padding: calc(16 * var(--base-unit)) calc(16 * var(--base-unit)) calc(16 * var(--base-unit)) 0;
            border-radius: calc(6 * var(--base-unit));
            background: linear-gradient(182.08deg, rgba(255, 255, 255, 0.42) 1.33%, rgba(255, 255, 255, 0.7) 97.82%);
            backdrop-filter: blur(12px);

            .task-detail-name {
                display: flex;
                font-weight: 600;
                font-size: calc(18 * var(--base-unit));
                color: #0E0F11;
            }

            .task-detail-status {
                display: flex;
                align-items: center;
                gap: calc(8 * var(--base-unit));
            }

            .coins {
                display: flex;
                align-items: center;
                color: #191B1E;
                gap: calc(2 * var(--base-unit));
                margin-left: 12px;

                &-value {
                    font-size: 14px;
                    color: #FF6638;
                    font-weight: 400;
                }
            }

            .days {
                display: flex;
                align-items: center;
                padding: calc(2 * var(--base-unit)) calc(2 * var(--base-unit)) calc(2 * var(--base-unit)) calc(8 * var(--base-unit));
                gap: calc(2 * var(--base-unit));
                border-radius: calc(4 * var(--base-unit));
                background: linear-gradient(90deg, #FF875C 0%, #FF6537 100%);
                color: #fff;
                font-size: calc(14 * var(--base-unit));

                .days-remaining {
                    padding: calc(2 * var(--base-unit)) calc(4 * var(--base-unit));
                    gap: calc(2 * var(--base-unit));
                    border-radius: calc(4 * var(--base-unit));
                    background-color: #fff;
                    font-family: Baidu Number;
                    font-size: calc(20 * var(--base-unit));
                    font-weight: 500;
                    line-height: calc(20 * var(--base-unit));
                    color: #FF6638;
                }
            }
        }

        .task-detail-content {
            display: flex;
            flex-direction: column;
            gap: calc(16 * var(--base-unit));

            .task-content-target-title {
                display: flex;
                align-items: center;
                font-weight: 400;
                font-size: calc(14 * var(--base-unit));
                line-height: calc(20 * var(--base-unit));
                color: #0E0F11;

                .title {
                    font-size: calc(16 * var(--base-unit));
                    font-weight: 600;
                    line-height: calc(26 * var(--base-unit));
                }

                .sub-title {
                    margin-left: calc(8 * var(--base-unit));
                }

                .task-update-time {
                    font-size: calc(12 * var(--base-unit));
                    color: #848B99;
                    margin-left: calc(4 * var(--base-unit));
                }
            }

            .task-content-target-info {
                margin-top: calc(16 * var(--base-unit));
                padding: calc(16 * var(--base-unit));
                border-radius: calc(6 * var(--base-unit));
                border: 1px solid #FFF;
                backdrop-filter: blur(12px);
                background: linear-gradient(182.08deg, rgba(255, 255, 255, 0.42) 1.33%, rgba(255, 255, 255, 0.7) 97.82%);
                box-shadow: 0 calc(4 * var(--base-unit)) calc(12 * var(--base-unit)) 0 #CFA16433;
                display: flex;
                flex-direction: column;
                gap: calc(16 * var(--base-unit));
            }

            .task-content-progress {
                display: flex;
                flex-direction: column;
                gap: calc(12 * var(--base-unit));
                font-size: calc(14 * var(--base-unit));

                .aix-progress {
                    margin-left: calc(16 * var(--base-unit));
                    margin-top: calc(24 * var(--base-unit));
                }

                .extra-rewards {
                    position: absolute;
                    right: calc(-14 * var(--base-unit));
                    top: 0;
                    display: flex;
                    align-items: center;
                    color: #FF6638;
                    font-size: calc(14 * var(--base-unit));
                    font-weight: 500;
                    line-height: calc(28 * var(--base-unit));
                }

                .extra-rewards-value {
                    padding-left: calc(4 * var(--base-unit));
                    font-size: calc(14 * var(--base-unit));
                    font-weight: 500;
                    width: calc(100% + calc(10 * var(--base-unit)));

                    .one-ai-tooltip-inner {
                        background-color: #FF6638;
                        line-height: calc(20 * var(--base-unit));
                    }

                    .one-ai-tooltip-content .one-ai-tooltip-arrow {
                        color: #FF6638;
                    }
                }

                .task-progress-summary-pc,
                .task-progress-summary {
                    display: flex;
                    color: #545B66;

                    .light-ai-mark {
                        font-size: calc(14 * var(--base-unit));
                    }
                }

                .task-progress-summary-pc {
                    justify-content: space-between;
                }
            }

            .sub-task-list-container {
                .sub-task-list-header {
                    display: flex;
                    align-items: center;
                    gap: calc(8 * var(--base-unit));
                    margin-bottom: calc(8 * var(--base-unit));

                    .sub-task-list-title {
                        font-size: calc(16 * var(--base-unit));
                        font-weight: 600;
                        line-height: calc(26 * var(--base-unit));
                    }

                    .sub-task-list-sub-title {
                        display: flex;
                        align-items: center;
                        gap: calc(2 * var(--base-unit));
                        color: #0E0F11;
                        font-size: calc(14 * var(--base-unit));
                    }

                    .sub-task-list-sub-title-strong {
                        color: #FF6638;
                    }
                }

                .sub-task-container {
                    border: calc(1 * var(--base-unit)) solid var(--Translucent-3, #6692DE26);
                    border-radius: calc(10 * var(--base-unit));
                    padding: calc(16 * var(--base-unit));

                    .sub-task-list-header-line {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: calc(12 * var(--base-unit));

                        .header-left {
                            display: flex;
                            gap: calc(8 * var(--base-unit));
                            align-items: center;

                            .title {
                                font-weight: 500;
                                font-size: calc(14 * var(--base-unit));
                                line-height: calc(20 * var(--base-unit));
                                color: #000;
                            }

                            .sub-title {
                                font-weight: 400;
                                font-size: calc(14 * var(--base-unit));
                                line-height: calc(20 * var(--base-unit));
                                color: #545B66;
                            }
                        }
                    }

                    .sub-task-list {
                        display: flex;
                        gap: calc(12 * var(--base-unit));
                        overflow-x: scroll;
                    }
                }

                .peer-reference-container {
                    border: calc(1 * var(--base-unit)) solid var(--Translucent-3, #6692DE26);
                    border-radius: calc(10 * var(--base-unit));
                    padding: calc(16 * var(--base-unit));
                    margin: calc(10 * var(--base-unit)) 0;

                    .header-line {
                        display: flex;
                        align-items: center;
                        margin-bottom: calc(12 * var(--base-unit));
                        gap: calc(8 * var(--base-unit));

                        .title {
                            font-weight: 500;
                            font-size: calc(14 * var(--base-unit));
                            line-height: calc(20 * var(--base-unit));
                            color: #000;
                        }

                        .sub-title {
                            font-weight: 400;
                            font-size: calc(14 * var(--base-unit));
                            line-height: calc(20 * var(--base-unit));
                            color: #545B66;
                        }
                    }

                    .peer-reference-list {
                        display: flex;
                        gap: calc(12 * var(--base-unit));

                        .peer-reference-item {
                            width: calc(332 * var(--base-unit));
                            height: calc(172 * var(--base-unit));
                            border-radius: calc(10 * var(--base-unit));
                            padding: calc(12 * var(--base-unit));
                            background: var(--Translucent-1, #6D9FF712);

                            .title {
                                font-weight: 500;
                                font-size: calc(14 * var(--base-unit));
                                line-height: calc(20 * var(--base-unit));
                                color: #0E0F11;
                                height: calc(40 * var(--base-unit));
                            }

                            .content {
                                border-radius: calc(6 * var(--base-unit));
                                padding: calc(12 * var(--base-unit));
                                background: #fff;
                                display: flex;
                                flex-direction: column;
                                margin: calc(10 * var(--base-unit)) 0;

                                .content-tip {
                                    display: flex;
                                    justify-content: space-between;

                                    .content-left-tip {
                                        font-weight: 400;
                                        font-size: calc(12 * var(--base-unit));
                                        line-height: calc(16 * var(--base-unit));
                                        color: #FF6638;

                                        .content-tip-value {
                                            font-family: Baidu Number;
                                            font-weight: 500;
                                            font-size: calc(16 * var(--base-unit));
                                            line-height: calc(16 * var(--base-unit));
                                        }
                                    }

                                    .content-right-tip {
                                        font-weight: 400;
                                        font-size: calc(12 * var(--base-unit));
                                        line-height: calc(16 * var(--base-unit));
                                        color: #545B66;
                                        display: flex;
                                        align-items: center;
                                    }
                                }

                                .content-progress {
                                    margin-top: calc(6 * var(--base-unit));
                                    height: calc(18 * var(--base-unit));
                                    border-radius: calc(6 * var(--base-unit));
                                    background: #DCEAFF;
                                    position: relative;

                                    .current-progress {
                                        position: absolute;
                                        left: 0;
                                        top: 0;
                                        height: 100%;
                                        background: #FF9877;
                                        border-radius: calc(6 * var(--base-unit));
                                    }

                                    .process-value {
                                        position: relative;
                                        font-weight: 400;
                                        font-size: calc(12 * var(--base-unit));
                                        line-height: calc(16 * var(--base-unit));
                                        color: #848B99;
                                        text-align: right;
                                        padding-right: calc(6 * var(--base-unit));
                                    }
                                }
                            }

                            .footer-action {
                                display: flex;
                                justify-content: space-between;
                                margin-top: calc(10 * var(--base-unit));
                                align-items: center;

                                .footer-action-link {
                                    font-weight: 400;
                                    font-size: calc(12 * var(--base-unit));
                                    line-height: calc(14 * var(--base-unit));
                                    color: #848B99;
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    .question-mark-icon {
        color: #5A77A84D;
        margin-left: calc(4 * var(--base-unit));
        font-size: calc(16 * var(--base-unit));
    }

    &-header {
        display: flex;
        gap: calc(4 * var(--base-unit));
        font-weight: 700;
        font-size: calc(20 * var(--base-unit));
        align-items: center;

        .coin-bg {
            position: absolute;
            right: calc(95 * var(--base-unit));
            top: calc(12 * var(--base-unit));
        }
    }
}

.task-recommend-reason {
    padding: calc(12 * var(--base-unit));
    border-radius: calc(6 * var(--base-unit));
    background: #FFF8EBCC;

    .light-ai-paragraph {
        font-size: calc(12 * var(--base-unit));
        line-height: calc(22 * var(--base-unit));
    }
}

.task-reward-pool {
    border-radius: 6px;
    padding: 4px 12px;
    background: var(--Yellow-1, #FFF8EB);
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;

    .reward-progress {
        margin-right: 12px;

        .one-ai-progress-circle-trail {
            stroke: #EBEDF5 !important;
        }

        .one-ai-progress-circle-path {
            stroke: #FFA20D !important;
        }

        .one-ai-progress-inner {
            width: 28px !important;
            height: 28px !important;
        }

        .one-ai-progress-check-icon {
            font-size: 21px !important;
        }

        .one-ai-progress-text {
            font-family: Baidu Number !important;
            font-weight: 500 !important;
            font-size: 10px !important;
            line-height: 12px !important;
            text-align: center !important;
            vertical-align: middle !important;
            color: #848B99 !important;
        }
    }
}

.mobile-task-detail {
    display: flex;
    flex-direction: column;
    gap: 1.6rem;
    padding: 1.6rem;
    background: linear-gradient(195.31deg, #FDF6D3 2.8%, #FFFEFA 31.49%);

    .aix-progress {
        margin-left: 0 !important;
    }

    .task-detail-container-title {
        display: flex;
        flex-direction: column;
        gap: 1.2rem;

        .task-detail-status {
            justify-content: space-between;
        }
    }
}
