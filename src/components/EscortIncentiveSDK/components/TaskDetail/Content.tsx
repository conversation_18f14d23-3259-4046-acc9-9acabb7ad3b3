import {Typography} from '@baidu/light-ai-react';
import {Tooltip, Progress, Button} from '@baidu/one-ui';
import dayjs from 'dayjs';
import {IconQuestionCircleSolid, IconDownload} from 'dls-icons-react';
import {useMemo} from 'react';
import {toPercent} from '@/utils/format';
import {useSlot} from '@/hooks/slot';
import {PageType} from '@/dicts/pageType';
import {appendQuery} from '@/utils/route';
import {getFcUrl} from '@/utils/format/url';
import {AixProgress} from '@/components/common/progress';
import {SubTaskDetailInfo, TaskDetailData} from '../../interface/task';
import {LocalTaskConfig} from '../../config/LocalTaskConfiguration';
import {DefaultTaskTargetExplanation} from '../../components/TaskExplanation';
import actionCards from '../TaskActions/actionCards';
import {useEscortIncentiveConfig} from '../../context';
import {EscortButton} from '../Button';
import {
    PC_START_PERCENT,
    PC_PERCENT_LENGTH,
    MOBILE_PERCENT,
    MOBILE_PERCENT_LENGTH,
    MAX_PERCENT,
    AdviceSource,
    LogActionType,
    SubTaskIdEnum,
    AppIdEnum
} from '../../config';
import {Coins} from '../Coins';
import {TaskTip} from '../Tip';
import './Content.global.less';
import {getCurrentProgressPercent} from './util';

export function TaskDetailContent(props: TaskDetailData & {isMobile?: boolean}) {
    const {
        reason,
        taskCurrentValue,
        taskTargetValue,
        taskEstimateValue,
        subTasks,
        taskId,
        reportData,
        extConfig,
        currentRewardsCount,
        userTaskUpdateTime,
        isMobile = false,
    } = props;

    const {rewardCurrentStock, rewardStock} = extConfig;
    const {slots, getTaskTargetDescription} = LocalTaskConfig[taskId];
    const taskTargetDescription = getTaskTargetDescription?.(props) ?? '';

    if (isMobile) {
        slots.TaskProgress = DefaultTaskProgressMobile;
    }
    const Slot = useSlot({slots, ...props});

    const rewardPoolRemainPercentage = ((rewardCurrentStock / rewardStock) * 100).toFixed(0);
    return (
        <div className="task-detail-content">
            <div className="task-content-target">
                <div className="task-content-target-title">
                    <div className="title">任务目标</div>
                    <Slot name="TaskDetailTargetExplanation">
                        <Tooltip placement="top" title={<DefaultTaskTargetExplanation />} type="dark">
                            <IconQuestionCircleSolid className="question-mark-icon" />
                        </Tooltip>
                    </Slot>
                    <div className="sub-title">{taskTargetDescription}</div>
                    {
                        userTaskUpdateTime && (
                            <div className="task-update-time">
                                {dayjs(userTaskUpdateTime).format('HH:mm')}更新
                            </div>
                        )
                    }
                </div>
                <div className="task-content-target-info">
                    <Slot name="TaskProgress">
                        <DefaultTaskProgress
                            taskCurrentValue={taskCurrentValue}
                            taskTargetValue={taskTargetValue}
                            taskEstimateValue={taskEstimateValue}
                            taskId={taskId}
                            reportData={reportData}
                            extConfig={extConfig}
                            currentRewardsCount={currentRewardsCount}
                        />
                    </Slot>
                    {
                        rewardCurrentStock > 0 ? (
                            <div className="task-reward-pool">
                                <Progress
                                    type="circle"
                                    percent={rewardPoolRemainPercentage}
                                    className="reward-progress"
                                />
                                请尽快达成剩余目标，奖池全部发放后，仅能获得已收集的金币。
                            </div>
                        ) : (
                            <div className="task-reward-pool">
                                奖池已全部发放，感谢参与！您可继续根据建议提升投放效果。
                            </div>
                        )
                    }
                    {
                        reason?.desc && (
                            <div className="task-recommend-reason">
                                <Typography.Markdown>{'说明：' + reason.desc}</Typography.Markdown>
                            </div>
                        )
                    }
                </div>
            </div>
            <SubTaskList subTasks={subTasks} isMobile={isMobile} />
        </div>
    );
}

/* eslint-disable max-len */
type TaskProgressProps = Pick<
    TaskDetailData,
    'taskCurrentValue' | 'taskTargetValue' | 'taskEstimateValue' | 'taskId' | 'reportData' | 'currentRewardsCount'
    | 'extConfig'
>;

function DefaultTaskProgress({
    taskCurrentValue,
    taskTargetValue,
    taskEstimateValue,
    taskId,
    reportData,
    extConfig,
    currentRewardsCount,
}: TaskProgressProps) {
    const {stages = [], rewardCurrentStock} = extConfig || {};
    const currentProgressPercent = useMemo(() => getCurrentProgressPercent({
        START_PERCENT: PC_START_PERCENT,
        PERCENT_LENGTH: PC_PERCENT_LENGTH,
        MAX_PERCENT,
        stages: extConfig?.stages,
        taskCurrentValue,
        taskTargetValue,
    }), [extConfig, taskCurrentValue, taskTargetValue]);

    const {taskIndicatorUnit, slots} = LocalTaskConfig[taskId];
    const Slot = useSlot({
        slots,
        taskCurrentValue,
        taskEstimateValue,
        reportData,
        extConfig,
        currentRewardsCount,
        currentProgressPercent,
        taskTargetValue,
        taskIndicatorUnit,
    });
    const dotType = rewardCurrentStock ? 'dot' : 'gray-dot';
    return (
        <div className="task-content-progress">
            <Slot name="TaskDetailProgressSummary" />
            <AixProgress
                width={750}
                // @ts-ignore
                style={{'--aix-progress-bgColor': 'linear-gradient(90deg, #FEF8DE 90%, rgb(255, 255, 255) 100%)'}}
                value={[
                    {
                        percent: currentProgressPercent,
                        style: {background: 'linear-gradient(90deg, rgb(255, 255, 255) 0%, rgb(255, 221, 103) 20%, rgb(255, 135, 15) 100%)'},
                    },
                ]}
                marks={[
                    {percent: 0, text: '任务领取'},
                    ...stages.map(({indicator, rewards, extraRewards}: any) => {
                        const percent = PC_START_PERCENT
                            + Math.floor(((indicator - stages[0].indicator) / (taskTargetValue - stages[0].indicator)) * PC_PERCENT_LENGTH);
                        return {
                            percent,
                            type: percent > currentProgressPercent ? dotType : 'checked-dot',
                            text: `${indicator}${taskIndicatorUnit}`,
                            textPlacement: 'bottom',
                            tip: (
                                <div>
                                    <Coins value={rewards} />
                                    {
                                        extraRewards && (
                                            <Tooltip
                                                visible
                                                placement="right"
                                                title={`额外${extraRewards}`}
                                                type="dark"
                                                overlayClassName="extra-rewards-value"
                                                getPopupContainer={n => n.parentNode as HTMLElement}
                                            >
                                                <div className="extra-rewards">+</div>
                                            </Tooltip>
                                        )
                                    }
                                </div>
                            ),
                        };
                    }),
                    {
                        percent: currentProgressPercent,
                        type: 'button',
                        variant: 'primary',
                        text: `${taskCurrentValue}${taskIndicatorUnit}`,
                        textPlacement: 'inline',
                    },
                ]}
            />
        </div>
    );
}

function DefaultTaskProgressMobile({
    taskCurrentValue,
    taskTargetValue,
    taskEstimateValue,
    taskId,
    reportData,
    extConfig,
    currentRewardsCount,
}: TaskProgressProps) {
    const {stages = []} = extConfig || {};
    const currentProgressPercent = useMemo(() => getCurrentProgressPercent({
        START_PERCENT: MOBILE_PERCENT,
        PERCENT_LENGTH: MOBILE_PERCENT_LENGTH,
        MAX_PERCENT,
        stages: extConfig?.stages,
        taskCurrentValue,
        taskTargetValue,
    }), [extConfig, taskCurrentValue, taskTargetValue]);

    const {taskIndicatorUnit, slots} = LocalTaskConfig[taskId];
    const Slot = useSlot({
        slots,
        taskCurrentValue,
        taskEstimateValue,
        reportData,
        extConfig,
        currentRewardsCount,
    });
    return (
        <div className="task-content-progress">
            <Slot name="MobileTaskDetailProgressSummary" />
            <AixProgress
                width="100%"
                isMobile
                // @ts-ignore
                style={{'--aix-progress-bgColor': 'linear-gradient(90deg, #FEF8DE 90%, rgb(255, 255, 255) 100%)'}}
                value={[
                    {
                        percent: currentProgressPercent,
                        // eslint-disable-next-line max-len
                        style: {background: 'linear-gradient(90deg, rgb(255, 255, 255) 0%, rgb(255, 221, 103) 20%, rgb(255, 135, 15) 100%)'},
                    },
                ]}
                marks={[
                    ...stages.map(({indicator, rewards, extraRewards}: any) => {
                        const percent = MOBILE_PERCENT
                            // eslint-disable-next-line max-len
                            + Math.floor(((indicator - stages[0].indicator) / (taskTargetValue - stages[0].indicator)) * MOBILE_PERCENT_LENGTH);
                        return {
                            percent,
                            type: percent > currentProgressPercent ? 'dot' : 'checked-dot',
                            text: `${indicator}${taskIndicatorUnit}`,
                            textPlacement: 'bottom',
                            tip: (
                                <div>
                                    {
                                        extraRewards ? (
                                            <Tooltip
                                                visible
                                                placement="top"
                                                title={`额外${extraRewards}`}
                                                type="dark"
                                                overlayClassName="extra-rewards-value"
                                                getPopupContainer={n => n.parentNode as HTMLElement}
                                            >
                                                <Coins value={rewards} />
                                            </Tooltip>
                                        ) : <Coins value={rewards} />
                                    }
                                </div>
                            ),
                        };
                    }),
                    {
                        percent: currentProgressPercent,
                        type: 'button',
                        variant: 'primary',
                        text: `${taskCurrentValue}${taskIndicatorUnit}`,
                        textPlacement: 'inline',
                    },
                ]}
            />
        </div>
    );
}


function PeerAddKeyword({
    indicatorCurrent,
    taskIndicator
}: {
    indicatorCurrent: number;
    taskIndicator: number;
}) {
    const {appId, userId, log} = useEscortIncentiveConfig();
    const progressWidth = toPercent(indicatorCurrent / taskIndicator, {decimals: 0});
    return (
        <div className="peer-reference-item">
            <div className="title">为推广设备为不限/计算机单元添加关键词</div>
            <div className="content">
                <div className="content-tip">
                    <div className="content-left-tip">
                        已添加 <span className="content-tip-value">{indicatorCurrent}</span>
                    </div>
                    <div className="content-right-tip">
                        目标词数
                        <TaskTip title="目标词数是基于当前账户与同行同体量账户词数差距计算得出，建议添加至目标数量以覆盖更多计算机端流量" />
                    </div>
                </div>
                <div className="content-progress">
                    <div
                        className="current-progress"
                        style={{width: progressWidth}}
                    />
                    <div className="process-value">
                        {indicatorCurrent}/{taskIndicator}
                    </div>
                </div>
            </div>
            <div className="footer-action">
                {
                    taskIndicator ? (
                        <EscortButton
                            type="primary"
                            onClick={() => {
                                log(LogActionType.GO_TO_OPTIMIZE_SUBTASK, {subTaskId: SubTaskIdEnum.addKeyword});

                                let url = getFcUrl('/fc/manage/tools/user/${userId}/optimizeAdviceDetail', {userId});

                                url = appendQuery(
                                    url,
                                    {
                                        adviceKey: 'addKeyword',
                                        source: AdviceSource,
                                        from: appId,
                                        in: 'iframe'
                                    }
                                );
                                window.open(url, '_blank');
                            }}
                            size="small"
                        >
                            查看推荐关键词
                        </EscortButton>
                    ) : <div />
                }
                <div className="footer-action-link">
                    前往关键词列表
                    <Button
                        type="text-strong"
                        size="small"
                        onClick={
                            () => {
                                log(LogActionType.GO_TO_MATERIAL_LIST, {pageType: PageType.KeywordList});
                                let url = `https://qingge.baidu.com/ad/manageCenter/keywordList?userId=${userId}&globalProduct=1`;
                                if (appId === AppIdEnum.fengchao) {
                                    url = getFcUrl('/fc/managecenter/dashboard/keywords/user/${userId}', {userId});
                                }
                                window.open(url, '_blank');
                            }
                        }
                    >
                        添加
                    </Button>
                </div>
            </div>
        </div>
    );
}

function PeerAddPcPicture({
    indicatorCurrent,
    taskIndicator
}: {
    indicatorCurrent: number;
    taskIndicator: number;
}) {
    const {appId, userId, log} = useEscortIncentiveConfig();
    const progressWidth = toPercent(indicatorCurrent / taskIndicator, {decimals: 0});
    return (
        <div className="peer-reference-item">
            <div className="title">为推广设备为不限/计算机的单元添加计算机尺寸图片</div>
            <div className="content">
                <div className="content-tip">
                    <div className="content-left-tip">
                        已添加 <span className="content-tip-value">{indicatorCurrent}</span>
                    </div>
                    <div className="content-right-tip">
                        目标图片数
                        <TaskTip title="目标图片数是基于当前账户与同行同体量账户的图片数差距计算得出，建议添加至目标数量以提高竞争力" />
                    </div>
                </div>
                <div className="content-progress">
                    <div
                        className="current-progress"
                        style={{width: progressWidth}}
                    />
                    <div className="process-value">
                        {indicatorCurrent}/{taskIndicator}
                    </div>
                </div>
            </div>
            <div className="footer-action">
                {
                    taskIndicator ? (
                        <EscortButton
                            type="primary"
                            onClick={() => {
                                log(LogActionType.GO_TO_OPTIMIZE_SUBTASK, {subTaskId: SubTaskIdEnum.addCreativeImageOptimizeCtr});

                                let url = getFcUrl('/fc/managecenter/optimizeAdviceDetail/user/${userId}', {userId});

                                url = appendQuery(
                                    url,
                                    {
                                        adviceKey: 'addCreativeImageOptimizeCtr',
                                        source: AdviceSource,
                                        from: appId,
                                        in: 'iframe'
                                    }
                                );
                                window.open(url, '_blank');
                            }}
                            size="small"
                        >
                            查看推荐创意
                        </EscortButton>
                    ) : <div />
                }
                <div className="footer-action-link">
                    前往创意图片列表
                    <Button
                        type="text-strong"
                        size="small"
                        onClick={
                            () => {
                                log(LogActionType.GO_TO_MATERIAL_LIST, {pageType: PageType.PictureList});
                                let url = `https://qingge.baidu.com/ad/manageCenter/creativeList/pictureList?userId=${userId}&globalProduct=1`;
                                if (appId === AppIdEnum.fengchao) {
                                    url = getFcUrl('/fc/managecenter/dashboard/creativesimageUser/user/${userId}', {userId});
                                }
                                window.open(url, '_blank');
                            }
                        }
                    >
                        添加
                    </Button>
                </div>
            </div>
        </div>
    );
}

function SubTaskList({subTasks, isMobile = false}: {subTasks: SubTaskDetailInfo[], isMobile?: boolean}) {
    const addPcImagesTask = subTasks.find(t => t.subTaskId === SubTaskIdEnum.addPcImages);
    const addPcKewordsTask = subTasks.find(t => t.subTaskId === SubTaskIdEnum.addPcKewords);
    const subTasksOfOptimize = subTasks.filter(t => ![SubTaskIdEnum.addPcImages, SubTaskIdEnum.addPcKewords].includes(t.subTaskId));
    return (
        <div className="sub-task-list-container">
            <div className="sub-task-list-header">
                <Typography.Title className="sub-task-list-title">
                    建议动作
                </Typography.Title>
                <div className="sub-task-list-sub-title">
                    助您达成任务目标
                    {/* <Typography.Text>已完成</Typography.Text>
                    <div className="sub-task-list-sub-title-strong">
                        {`(${subTasks.filter(t => t.status === TaskStatusEnum.completed || t.status === TaskStatusEnum.completedAndGetCoin).length}/${subTasks.length})`}
                    </div> */}
                </div>
            </div>
            {
                subTasksOfOptimize.length ? (
                    <div className="sub-task-container">
                        <div className="sub-task-list-header-line">
                            <div className="header-left">
                                <div className="title">账户分析</div>
                                <div className="sub-title">
                                    系统基于账户情况，推荐可帮助达成任务目标的优化动作
                                </div>
                                {/* <Button type="text-strong" size="small">
                                    一键采纳
                                </Button> */}
                            </div>
                            {/* <Button
                                type="text-strong"
                                icon={IconDownload}
                                size="small"
                            >
                                导出优化详情
                            </Button> */}
                        </div>
                        <div className="sub-task-list">
                            {
                                subTasks.map(subtask => {
                                    const ActionCard = actionCards[subtask.subTaskId];
                                    if (!ActionCard) {
                                        return null;
                                    }
                                    return (
                                        <ActionCard key={subtask.subTaskId} {...subtask} isMobile={isMobile} />
                                    );
                                })
                            }
                        </div>
                    </div>
                ) : null
            }
            <div className="peer-reference-container">
                <div className="header-line">
                    <div className="title">同行参考</div>
                    <div className="sub-title">
                        以下为系统分析得出，与同行同体量账户的计算机端基建差距
                    </div>
                </div>
                <div className="peer-reference-list">
                    <PeerAddKeyword {...addPcKewordsTask} />
                    <PeerAddPcPicture {...addPcImagesTask} />
                </div>
            </div>
        </div>
    );
}


