.banfei-guide-dialog {
    &-content-bg {
        background-image: var(--guide-dialog-bg-image, url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_guide_dialog.png));
        background-repeat: no-repeat;
        height: 450px;
        width: 542px;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-size: cover;
    }

    // 智能翻新
    &.rebuild-variant &-content-bg {
        --guide-dialog-bg-image: url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_rebuild_guide_dialog.png);
    }

    &-content-text {
        position: absolute;
        bottom: -71px;
        width: 384px;
        font-weight: 400;
        font-size: 16px;
        line-height: 22px;
        margin-left: 12px;

        .text1 {
            .text-number {
                font-weight: 500;
                color: #FF6638;
            }

            .text-union {
                font-weight: 500;
                margin: 0 2px;
            }
        }

        .text2 {
            margin-top: 72px;
        }
    }

    &-content-btn {
        cursor: pointer;
        background-image: url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_guide_accept.svg);
        z-index: 1;
        position: absolute;
        background-repeat: no-repeat;
        height: 48px;
        width: 478px;
        left: 50%;
        bottom: -49px;
        transform: translate(-50%, 120px);

        &:hover {
            background-image: url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_guide_accept_active.svg);
        }
    }

    &-content-image-preload {
        height: 0;
        width: 0;
        visibility: none;
        background-image: url(https://fc-feed.bj.bcebos.com/aix%2Fbanfei_guide_accept_active.svg);
    }

    .one-dialog-header {
        position: absolute;
        z-index: 1;
        right: 16px;
        top: -50px;
    }

    .one-dialog-close {
        margin-right: 0;
        position: absolute;
        color: #fff;
        right: -123px;
        top: -142px;
        height: 36px !important;
        width: 36px;
        background: #ffffff4d;
        border-radius: 30px;
    }
}
