import {useCallback} from 'react';
import {appendQuery} from '@/utils/route';
import {getFcUrl} from '@/utils/format/url';
import {onDownloadOptAdvice} from '../../../api/tasks';
import {ActionCard} from '../Card';
import {SubTaskDetailInfo} from '../../../interface/task';
import {useEscortIncentiveConfig} from '../../../context';
import {AdviceSource, SubTaskIdEnum, LogActionType} from '../../../config';

export default function FcAddKeyword(props: SubTaskDetailInfo) {
    const {appId, userId, log} = useEscortIncentiveConfig();

    const onAccept = useCallback(
        () => {
            log(LogActionType.GO_TO_OPTIMIZE_SUBTASK, {subTaskId: SubTaskIdEnum.addKeyword});

            let url = getFcUrl('/fc/manage/tools/user/${userId}/optimizeAdviceDetail', {userId});

            url = appendQuery(
                url,
                {
                    adviceKey: 'addKeyword',
                    source: AdviceSource,
                    from: appId,
                    in: 'iframe'
                }
            );
            window.open(url, '_blank');
        },
        [appId]
    );

    const onDownload = useCallback(
        async () => {
            const data = await onDownloadOptAdvice({adviceKey: 'addKeyword'});
            log(LogActionType.OPTIMIZE_SUBTASK_DOWNLOAD, {subTaskId: SubTaskIdEnum.addKeyword});
            window.open(data.acceptInfo.downloadUrl, '_blank');
        },
        []
    );

    return <ActionCard {...props} onAccept={onAccept} onDownload={onDownload} />;
}
