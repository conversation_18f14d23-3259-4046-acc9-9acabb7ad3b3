import {useCallback} from 'react';
import {appendQuery} from '@/utils/route';
import {getFcUrl} from '@/utils/format/url';
import {ActionCard} from '../Card';
import {onDownloadOptAdvice} from '../../../api/tasks';
import {SubTaskDetailInfo} from '../../../interface/task';
import {useEscortIncentiveConfig} from '../../../context';
import {AdviceSource, SubTaskIdEnum, LogActionType} from '../../../config';

export default function FcPcOptimize(props: SubTaskDetailInfo) {
    const {appId, userId, log} = useEscortIncentiveConfig();

    const onAccept = useCallback(
        () => {
            log(LogActionType.GO_TO_OPTIMIZE_SUBTASK, {subTaskId: SubTaskIdEnum.fcPcOptimize});

            let url = getFcUrl('/fc/managecenter/optimizeAdviceDetail/user/${userId}', {userId});

            url = appendQuery(
                url,
                {
                    adviceKey: 'fcPcOptimize',
                    source: AdviceSource,
                    from: appId,
                    in: 'iframe'
                }
            );
            window.open(url, '_blank');
        },
        [appId]
    );

    const onDownload = useCallback(
        async () => {
            const data = await onDownloadOptAdvice({adviceKey: 'fcPcOptimize'});
            log(LogActionType.OPTIMIZE_SUBTASK_DOWNLOAD, {subTaskId: SubTaskIdEnum.fcPcOptimize});
            window.open(data.acceptInfo.downloadUrl, '_blank');
        },
        []
    );

    return <ActionCard {...props} onAccept={onAccept} onDownload={onDownload} />;
}
