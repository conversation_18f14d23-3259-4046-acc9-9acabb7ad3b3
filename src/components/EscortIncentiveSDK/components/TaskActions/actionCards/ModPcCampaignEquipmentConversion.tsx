import {useCallback} from 'react';
import {appendQuery} from '@/utils/route';
import {getFcUrl} from '@/utils/format/url';
import {EquipmentType} from '@/dicts/equipmentType';
import {ActionCard} from '../Card';
import {onDownloadOptAdvice} from '../../../api/tasks';
import {SubTaskDetailInfo} from '../../../interface/task';
import {useEscortIncentiveConfig} from '../../../context';
import {AdviceSource, SubTaskIdEnum, LogActionType} from '../../../config';

export default function ModPcCampaignEquipmentConversion(props: SubTaskDetailInfo) {
    const {appId, userId, log} = useEscortIncentiveConfig();

    const onAccept = useCallback(
        () => {
            log(LogActionType.GO_TO_OPTIMIZE_SUBTASK, {subTaskId: SubTaskIdEnum.modPcCampaignEquipmentConversion});

            let url = getFcUrl('/fc/managecenter/optimizeAdviceDetail/user/${userId}', {userId});

            url = appendQuery(
                url,
                {
                    adviceKey: 'modCampaignEquipmentConversion',
                    source: AdviceSource,
                    from: appId,
                    in: 'iframe',
                    filters: JSON.stringify([
                        {field: 'currentEquipmentType', op: 'in', value: [`${EquipmentType.WISE}`]},
                    ]),
                }
            );
            window.open(url, '_blank');
        },
        [appId, userId, log]
    );

    const onDownload = useCallback(
        async () => {
            const data = await onDownloadOptAdvice({adviceKey: 'modCampaignEquipmentConversion'});
            log(LogActionType.OPTIMIZE_SUBTASK_DOWNLOAD, {subTaskId: SubTaskIdEnum.modPcCampaignEquipmentConversion});
            window.open(data.acceptInfo.downloadUrl, '_blank');
        },
        []
    );

    return <ActionCard {...props} onAccept={onAccept} onDownload={onDownload} />;
}
