import {useCallback} from 'react';
import {EquipmentType} from '@/dicts/equipmentType';
import {getFcUrl} from '@/utils/format/url';
import {appendQuery} from '@/utils/route';
import {ActionCard} from '../Card';
import {onDownloadOptAdvice} from '../../../api/tasks';
import {SubTaskDetailInfo} from '../../../interface/task';
import {useEscortIncentiveConfig} from '../../../context';
import {AdviceSource, SubTaskIdEnum, LogActionType} from '../../../config';

const equipmentType = [
    EquipmentType.PC,
    EquipmentType.ALL
];

export default function DynamicImageOptimizeOfPC(props: SubTaskDetailInfo) {
    const {appId, userId, log} = useEscortIncentiveConfig();

    const onAccept = useCallback(
        () => {
            log(LogActionType.GO_TO_OPTIMIZE_SUBTASK, {subTaskId: SubTaskIdEnum.dynamicImageOptimizeOfPC});
            let url = getFcUrl('/fc/managecenter/optimizeAdviceDetail/user/${userId}', {userId});

            url = appendQuery(
                url,
                {
                    adviceKey: 'dynamicImageOptimize',
                    source: AdviceSource,
                    from: appId,
                    filters: JSON.stringify([{field: 'equipmentType', op: 'in', value: equipmentType}]),
                    in: 'iframe'
                }
            );
            window.open(url, '_blank');
        },
        [appId]
    );

    const onDownload = useCallback(
        async () => {
            const data = await onDownloadOptAdvice({adviceKey: 'dynamicImageOptimize'});
            log(LogActionType.OPTIMIZE_SUBTASK_DOWNLOAD, {subTaskId: SubTaskIdEnum.dynamicImageOptimizeOfPC});
            window.open(data.acceptInfo.downloadUrl, '_blank');
        },
        []
    );

    return <ActionCard {...props} onAccept={onAccept} onDownload={onDownload} />;
}
