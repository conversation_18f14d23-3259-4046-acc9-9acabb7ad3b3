import {TaskIdEnum, TaskStatusEnum, SubTaskIdEnum, RewardType} from '../config';

interface InviteUser {
  inviteCompany: string; // 被邀请客户公司名
  inviteOfficial: string; // 被邀请公司负责人
  inviteOfficialPhone: number; // 被邀请公司负责人手机号
  city: string; // 城市
  inviteStatus: 0 | 1; // 0 - 未邀请成功；1 - 邀请成功
  custType: 0 | 1;
  inviteFailReason: string; // 邀请失败原因
  hasPay: 0 | 1; // 0 - 未产生消费；1 - 产生消费
}

export interface TaskBasicInfo {
    taskId: TaskIdEnum;
    status: TaskStatusEnum;
    startTime: string;
    /**
     * 任务领取时间
     */
    userStartTime: string;
    endTime: string;
    /**
     * 任务周期; 比如：任务倒计时，通过此字段和userStartTime一起得出
     */
    taskDuration: number;
    /**
     * 当前任务的奖励上限；
     */
    rewardLimit: number;
    /**
     * 当前任务获得的奖励值
     */
    rewards: number;
    /**
     * 当前任务已完成值
     */
    indicatorCurrent: number;
    /**
     * 当前任务的目标值
     */
    taskIndicator: number;
    estimateData?: Record<string, number>;
    reportData: Record<string, number> & {inviteUsers: InviteUser[]};
    subTasks: Array<{
        subTaskid: number;
        status: number;
    }>;
    /**
     * 同行采纳比例
     */
    competition: number;
    desc: string;
    extConfig: Record<string, any>;
    rewardsData?: Record<string, number>;
    rewardType: RewardType;
    title: string;
}

export interface SubTaskDetailInfo {
    subTaskId: SubTaskIdEnum;
    status: TaskStatusEnum;
    rewards: number;
    rewardLimit: number;
    title: string;
    desc: string;
    isMobile?: boolean;
}

export interface TaskDetailData {
    taskId: TaskIdEnum;
    reason?: {
        title: string;
        desc?: string;
    };
    currentRewardsCount: number;
    rewardLimit: number;
    daysRemaining: number;
    taskCurrentValue: number;
    taskTargetValue: number;
    taskEstimateValue: number;
    estimateData: {estimateCoupon: number};
    subTasks: SubTaskDetailInfo[];
    reportData: Record<string, number> & {inviteUsers: InviteUser[]};
    extConfig: Record<string, any>;
    userTaskUpdateTime?: string;
    taskDuration: number;
    userStartTime: string;
    rewardsData: Record<string, any>;
    rewardType: RewardType;
    endTime: string;
}
