import {Typography} from '@baidu/light-ai-react';
import {isNumber} from 'lodash-es';
import {Button, Tooltip} from '@baidu/one-ui';
import {toPercent} from '@/utils/format';
import CoinImg from '@/components/EscortIncentiveSDK/resource/coin.svg';
import {TaskDetailData, TaskBasicInfo} from '../interface/task';
import {Coins, CoinsProgress} from '../components/Coins';
import {EscortButton} from '../components/Button';
import {formatTaskEndTime, getInvertTime} from '../utils';
import {EscortCoupon} from '../components/Coupons';
import {TaskIdEnum, TaskStatusEnum, LogActionType} from './index';
import './Task_PC.global.less';

/* eslint-disable */
function TaskEstimateTipOnCard({estimateData, viewUnacceptTaskDetail, log, pendingCount}: {
    estimateData?: Record<string, any>;
    viewUnacceptTaskDetail: (taskId: number) => void;
    log: (type: string, ...args: any[]) => void;
    pendingCount: boolean;
}) {
    if (!estimateData || (!estimateData?.conversions && !estimateData?.click)) {
        return (
            <Button
                size="small"
                type="text-strong"
                style={{color: '#FF6638'}}
                onClick={() => {
                    viewUnacceptTaskDetail(TaskIdEnum.ExpansionPC3);
                    log(LogActionType.OPEN_UNACCEPT_TASK_DETAIL);
                }}
                disabled={pendingCount}
            >
                查看详情
            </Button>
        );
    }

    return (
        <div style={{display: 'flex', alignItems: 'baseline'}}>
            <Typography.Markdown className="estimate-tip">
                {
                    !estimateData.conversions && estimateData.click
                        ? `完成任务预计获得:mark[${estimateData.click}]{status="warning" size="small"}个点击`
                        : `完成任务预计获得:mark[${estimateData.conversions}]{status="warning" size="small"}个转化`
                }
            </Typography.Markdown>
            <div style={{display: 'flex', alignItems: 'baseline'}}>
                ，
                <Button
                    size="small"
                    type="text-strong"
                    style={{color: '#FF6638'}}
                    onClick={() => {
                        viewUnacceptTaskDetail(TaskIdEnum.ExpansionPC3);
                        log(LogActionType.OPEN_UNACCEPT_TASK_DETAIL);
                    }}
                    disabled={pendingCount}
                >
                    查看详情
                </Button> 
            </div>
        </div>
    );
}

function TaskRewardPool({rewardCurrentStock, rewardStock, isAccept}: {
    rewardCurrentStock: number;
    rewardStock: number;
    isAccept: boolean;
}) {
    if (!rewardCurrentStock) {
        return null;
    }
    const rewardPoolRemainPercentage = toPercent(rewardCurrentStock / rewardStock, {decimals: 0});
    const text = `奖池剩余${rewardPoolRemainPercentage}`
    const tipText = isAccept
        ? '请尽快达成剩余目标，奖池全部发放后，将无法继续收集金币。'
        : '即刻领取任务瓜分奖池，奖励先到先得！'
    return (
        <Tooltip title={tipText}>
            <div
                className="task-reward-pool-remain"
            >
                <div
                    className="task-reward-pool-remain-progress"
                    style={{width: rewardPoolRemainPercentage}}
                />
                <div className="task-reward-pool-remain-text">
                    {text}
                </div>
            </div>
        </Tooltip>
    );
}

function TaskFeaturesOnCard({
    taskId,
    competition,
    onAcceptTask,
    pendingCount,
    extConfig,
}: {
    taskId: number;
    competition?: number;
    onAcceptTask?: (taskId: number) => void;
    pendingCount?: number;
    extConfig: {
        rewardCurrentStock: number;
        rewardStock: number;
    }
}) {
    return (
        <div className="task-card-operation">
            <TaskRewardPool {...extConfig} isAccept={false}  />
            <div className="task-operation-content">
                {
                    competition != null ? (
                        <div className="task-feature">
                            {toPercent(competition, {decimals: 0})}同行领取
                        </div>
                    ) : null
                }
            </div>
            <EscortButton size="small" onClick={() => onAcceptTask?.(taskId)} loading={!!pendingCount}>
                领取任务
            </EscortButton>
        </div>
    );
}

function Right() {
    return (
        <div className="task-pc-coupon-tip">
            <div className="task-pc-coupon-tip-top">限时折扣券</div>
            <div className="task-pc-coupon-tip-bottom">消费越多券额越大</div>
        </div>
    );
}

function Left({rewardLimit}: {rewardLimit: number}) {
    return (
        <div className="task-pc-coupon-amount">
            <div className="task-pc-coupon-amount-value">
                <span className="unit">￥</span>
                <span className="number">{Math.round(rewardLimit / 10)}</span>
            </div>
            <div className="task-pc-coupon-amount-label">
                最高券额
            </div>
        </div>
    );
}

function UnacceptedTaskCardOperation({rewardLimit}: {rewardLimit: number}) {
    return (
        <div className="task-pc-coupon">
            <EscortCoupon
                contentWidth="40%"
                renderLeft={<Left rewardLimit={rewardLimit} />}
                renderRight={<Right />}
            />
        </div>
    );
}

function UnacceptedTaskCardInfo(props: TaskBasicInfo) {
    return (
        <>
            <TaskEstimateTipOnCard {...props} />
            <UnacceptedTaskCardOperation {...props} />
            <TaskFeaturesOnCard {...props} />
        </>
    );
}

function ReportDataDesc({reportData}: {reportData: Record<string, any>}) {
    if (!reportData) {
        return null;
    }

    return (
        <Typography.Markdown>
            {
                (!reportData.conversions && reportData.click)
                    ? `已累计为您带来:mark[${reportData.click}]{status="warning" size="small"}个点击`
                    : `已累计为您带来:mark[${reportData.conversions}]{status="warning" size="small"}个转化`
            }
        </Typography.Markdown>
    );
}

function SubTaskProgressOnCard({
    subTasks, userStartTime, taskDuration, endTime, taskId, extConfig
}: {
    subTasks: Array<{
        subTaskid: number;
        status: number;
    }>;
    userStartTime: string;
    taskDuration: number;
    endTime: string;
    taskId: number;
    extConfig: {
        rewardCurrentStock: number;
        rewardStock: number;
    }
}) {
    return (
        <div style={{display: 'flex', justifyContent: 'space-between'}}>
            <TaskRewardPool {...extConfig} isAccept  />
            <div className="sub-task-progress">
                已完成
                <span className="highlight">
                    {subTasks.filter(item => item.status === TaskStatusEnum.completed).length}
                    /
                    {subTasks.length}
                </span>，任务倒计时
                <span className="highlight">{getInvertTime({userStartTime, taskDuration, endTime})}</span>天
            </div>
        </div>
    )
}

function TaskDetailProgressSummary({
    taskCurrentValue,
    reportData,
    taskEstimateValue,
    extConfig,
    currentRewardsCount,
}: TaskDetailData) {
    const {conversions, click, cost} = reportData || {};
    const {stages = []} = extConfig || {};

    return (
        <div className="task-progress-summary-pc">
            <div style={{display: 'flex', 'alignItems': 'center'}}>
                <Typography.Markdown>{`计算机累计消费:mark[${Math.round(cost)}]{status="warning"}元`}</Typography.Markdown>，
                {
                    taskCurrentValue < stages[0]?.indicator
                        ? (
                            <Typography.Markdown>
                                {`带来${(!conversions && click)
                                    ? `:mark[${click}]{status="warning"}个点击`
                                    : `:mark[${conversions}]{status="warning"}个转化`}，`
                                        + `还差${Math.round(stages[0]?.indicator - taskCurrentValue)}元可收集金币`
                                }
                            </Typography.Markdown>
                        )
                        : (
                            <>
                                已收集金币 <Coins value={currentRewardsCount} />，
                                <Typography.Markdown>
                                    {`同时带来${(!conversions && click)
                                        ? `:mark[${click}]{status="warning"}个点击`
                                        : `:mark[${conversions}]{status="warning"}个转化`}`
                                    }
                                </Typography.Markdown>
                            </>
                        )
                }
            </div>
            <Typography.Markdown>
                {`任务结束时预估计算机累计消费可达:mark[${Math.round(taskEstimateValue)}]{status="warning"}元`}
            </Typography.Markdown>
        </div>
    );
}

function MobileTaskDetailProgressSummary({
    taskCurrentValue,
    reportData,
    taskEstimateValue,
    extConfig,
    currentRewardsCount,
}: TaskDetailData) {
    const {conversions, click, cost} = reportData || {};
    const {stages = []} = extConfig || {};

    return (
        <div className="task-progress-summary">
            <div style={{display: 'flex', 'alignItems': 'center'}}>
                <Typography.Markdown className="summary-text">
                    {
                        `当前计算机消费:mark[${Math.round(cost)}]{status="warning"}元`
                        + `${taskCurrentValue < stages[0]?.indicator ? `
                            已带来${(!conversions && click)
            ? `:mark[${click}]{status="warning"}个点击`
            : `:mark[${conversions}]{status="warning"}个转化`}，`
                                        + `还差${Math.round(stages[0]?.indicator - taskCurrentValue)}元即可收集金币
                            ` : `
                            已收集个:mark[${currentRewardsCount}]{status="warning"}金币，
                                    同时带来${(!conversions && click)
            ? `:mark[${click}]{status="warning"}个点击`
            : `:mark[${conversions}]{status="warning"}个转化`}
                            `}，`
                        + `基于当前消费情况，预估任务结束时可达:mark[${Math.round(taskEstimateValue)}]{status="warning"}元`
                    }
                </Typography.Markdown>
            </div>
        </div>
    );
}

function TaskDetailTitle({
    title,
    currentRewardsCount,
    rewardLimit,
    daysRemaining
}: {
    title: string;
    currentRewardsCount: number;
    rewardLimit: number;
    daysRemaining: number;
}) {
    return (
        <div className="task-detail-container-title">
            <div className="task-detail-name">
                {title}
                {rewardLimit && (
                    <div className="coins">
                        <CoinsProgress
                            size="large"
                            value={currentRewardsCount}
                            limit={rewardLimit}
                        />
                        <span className="coins-value">(价值{currentRewardsCount / 10}元优惠券)</span>
                    </div>
                )}
            </div>
            {isNumber(daysRemaining) && (
                <div className="task-detail-status">
                    <div className="days">
                        {formatTaskEndTime(daysRemaining, 'MM月DD日0点')}截止，剩余
                        <div className="days-remaining">{daysRemaining}天</div>
                    </div>
                </div>
            )}
        </div>
    );
}

function TaskDetailProgressIntroduce({rewards, extConfig}: {
    rewards: number;
    extConfig: {
        rewardCurrentStock: number;
    }
}) {
    if (extConfig?.rewardCurrentStock) {
        return null;
    }
    return (
        <div className="task-detail-progress-introduce">
            <div className="introduce-title">
                奖池已瓜分完毕，感谢参与！
            </div>
            <div className="introduce-content">
                已获得<span className="coin-number">{rewards}</span>
                个金币，任务结束后发放。任务还在进行中，可继续根据建议优化提升转化
            </div>
            <img src={CoinImg} alt="金币图标" className="coin-bg" />
        </div>
    );
}

export const slots = {
    UnacceptedTaskCardInfo,
    ReportDataDesc,
    TaskDetailProgressSummary,
    TaskDetailTitle,
    MobileTaskDetailProgressSummary,
    SubTaskProgressOnCard,
    TaskDetailProgressIntroduce
};
