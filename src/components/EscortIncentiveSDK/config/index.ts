import {getConfigFromDataSource} from '@/utils/type/config';

export enum AppIdEnum {
    tuiguang = 'tuiguang',
    qingge = 'qingge',
    fengchao = 'fengchao',
}

export enum PLATFORM_ENUM {
    FC = 1,
    FEED = 2,
    ONWEB = 3,
}

export const AdviceSource = 300;

export enum TaskIdEnum {
    /**
     * PC专项任务
     */
    ExpansionPC = 1,
    /**
     * 首续激励任务
     */
    Renewal = 2,
    /**
     * 首续激励任务第二期(内容与第一版完全一样)
    */
    Renewal2 = 3,
    /**
     * 唤醒激励任务
     */
    Wakeup = 4,
    /**
     * 消费冲刺优惠券任务
     */
    UpperConsumptionCoupons = 5,
    /**
     * 消费冲刺积分任务
     */
    UpperConsumptionPoints = 6,
    /**
     * PC专项任务二期
     */
    ExpansionPC2 = 7,
    /**
     * 联中智能翻新激励-202505
     */
    UpperAiRebuildCoupons = 8,
     /**
     * PC专项任务三期
     */
    ExpansionPC3 = 9,
    /**
     * Q3 联中首续
     */
    UpperQ3Renewal = 10,
    /**
     * Q3 唤醒激励
     */
    UpperQ3Wakeup = 11,
    /**
     * Q3 联中智能翻新激励-任务中心
     */
    UpperQ3AiRebuildCoupons = 12,
    /**
     * 老带新任务
     */
    OldBringNew = 13,
}

export const expansionPcTaskIds = [TaskIdEnum.ExpansionPC, TaskIdEnum.ExpansionPC2];

export enum SubTaskIdEnum {
    /**
     * 操作动作 - 优化中心建议 - 调整设备为PC
     */
    modPcCampaignEquipmentConversion = 1,
    /**
     * 操作动作 - 新建推广设备为不限/计算机的方案
     */
    createPcCampaign = 2,
    /**
     * 操作动作 - 为推广设备为不限/计算机的单元开启自动图片优化
     */
    dynamicImageOptimizeOfPC = 3,
    /**
     * 操作动作 - 为推广设备为不限/计算机的单元开启自动定向
     */
    fcAdgroupAutoTargetingOfPC = 4,
    /**
     * 操作动作 - 为推广设备为不限/计算机的单元添加关键词
     */
    addPcKewords = 5,
    /**
     * 操作动作 - 为推广设备为不限/计算机的单元添加计算机尺寸图片
     */
    addPcImages = 6,
    /**
     * 操作动作 - 优化计算机端投放获取更多转化
     */
    fcPcOptimize = 7,
    /**
     * 操作动作 - 添加关键词
     */
    addKeyword = 8,
    /**
     * 操作动作 - 添加创意图片获得更多流量
     */
    addCreativeImageOptimizeCtr = 1001,
}

export enum TaskStatusEnum {
    /**
     * 未领取
     */
    unAccepted = -1,
    /**
     * 已领取，但任务进度未开始
     */
    accepted = 0,
    /**
     * 任务进度已开始，但未达到任务门槛
     */
    notMeetTaskThreshold = 1,
    /**
     * 已达到门槛但未达到任务上限
     */
    notMeetTaskLimit = 2,
    /**
     * 已完成（包含已达到任务上限）（未发放金币）
     */
    completed = 3,
    /**
     * 已完成（已发放金币）
     */
    completedAndGetCoin = 4,
    /**
     * 未领取，但优惠券暂时未发放
    */
    unIssued = 5,
    /**
     * 已完成，但是没有获得奖励
     */
    completedButNoRewards = 6,
}

export enum CoinUsageEnum {
    task = 1,
    exchange = 2,
    expire = 3,
}
export const {
    nameMapByValue: CoinUsageTextMapByValue,
} = getConfigFromDataSource([
    [CoinUsageEnum.task, 'task', '任务完成'],
    [CoinUsageEnum.exchange, 'exchange', '兑换金币'],
    [CoinUsageEnum.expire, 'expire', '金币过期'],
]);

export enum RewardType {
    coupons = 1,
    points = 2,
}
export const RewardTypeText = {
    [RewardType.coupons]: '优惠券',
    [RewardType.points]: '积分'
};

export enum LogActionType {
    /**
     * 埋点 - 领取任务成功
     */
    ACCEPT_TASK_SUCCESS = 'ACCEPT_TASK_SUCCESS',
    /**
     * 埋点 - 领取任务失败
     */
    ACCEPT_TASK_FAIL = 'ACCEPT_TASK_FAIL',
    /**
     * 埋点 - 未领取的任务在任务卡片面板上曝光
     */
    VIEW_UNACCEPTED_TASK = 'VIEW_UNACCEPTED_TASK',
    /**
     * 埋点 - 已领取的任务在任务卡片面板上曝光
     */
    VIEW_ACCEPTED_TASK = 'VIEW_ACCEPTED_TASK',
    /**
     * 埋点 - 打开任务详情
     */
    OPEN_TASK_DETAIL = 'OPEN_TASK_DETAIL',
    /**
     * 埋点 - 打开任务详情
     */
    OPEN_UNACCEPT_TASK_DETAIL = 'OPEN_UNACCEPT_TASK_DETAIL',
    /**
     * 埋点 - 子任务操作动作 - 去优化
     */
    GO_TO_OPTIMIZE_SUBTASK = 'GO_TO_OPTIMIZE_SUBTASK',
    /**
     * 埋点 - 子任务操作动作 - 去列表
     */
    GO_TO_MATERIAL_LIST = 'GO_TO_MATERIAL_LIST',
    /**
     * 埋点 - 打开金币历史记录
     */
    OPEN_COIN_HISTORY = 'OPEN_COIN_HISTORY',
    /**
     * 埋点 - 金币历史记录-查看优惠券
     */
    COIN_HISTORY_VIEW_COUPONS = 'COIN_HISTORY_VIEW_COUPONS',
    /**
     * 埋点 - 打开任务历史记录
     */
    OPEN_HISTORY_TASKS = 'OPEN_HISTORY_TASKS',
    /**
     * 埋点 - 任务历史记录-查看优惠券
     */
    TASKS_HISTORY_VIEW_COUPONS = 'TASKS_HISTORY_VIEW_COUPONS',
    /**
     * 埋点 - 打开任务规则
     */
    OPEN_TASK_RULES = 'OPEN_TASK_RULES',
    /**
     * 埋点 - 伴飞任务入口卡片面板的曝光
     */
    VIEW_TASK_ENTRY = 'VIEW_TASK_ENTRY',
    /**
     * 埋点 - 打开金币兑换弹窗
     */
    OPEN_EXCHANGE_COIN_DIALOG = 'OPEN_EXCHANGE_COIN_DIALOG',
    /**
     * 埋点 - 兑换金币成功
     */
    EXCHANGE_COIN_SUCCESS = 'EXCHANGE_COIN_SUCCESS',
    /**
     * 埋点 - 兑换金币失败
     */
    EXCHANGE_COIN_FAIL = 'EXCHANGE_COIN_FAIL',
    /**
     * 埋点 - 子任务操作动作 - 曝光
     */
    VIEW_SUBTASK_IN_DETAIL = 'VIEW_SUBTASK_IN_DETAIL',
    /**
     * 埋点 - 激励任务-去支付按钮点击
    */
    CLICK_PAY_BUTTON = 'CLICK_PAY_BUTTON',
    /**
     * 埋点 - 任务引导弹窗曝光
    */
    VIEW_GUIDE_DIALOG = 'VIEW_GUIDE_DIALOG',
    /**
     * 埋点 - 任务引导弹窗-领取任务
    */
    GUIDE_DIALOG_CONFIRM = 'GUIDE_DIALOG_CONFIRM',
    /**
     * 埋点 - 翻新激励-使用智能翻新
     */
    USE_SMART_RENOVATION = 'USE_SMART_RENOVATION',
    /**
     * 埋点 - 下载
     */
    OPTIMIZE_SUBTASK_DOWNLOAD = 'OPTIMIZE_SUBTASK_DOWNLOAD',
}

export const PC_START_PERCENT = 20;
export const PC_PERCENT_LENGTH = 60;
export const MOBILE_PERCENT = 10;
export const MOBILE_PERCENT_LENGTH = 80;
export const MAX_PERCENT = 90;
