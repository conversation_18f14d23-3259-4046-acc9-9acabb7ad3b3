/* eslint-disable max-len */
/*
 * @file 老带新任务
 * <AUTHOR>
 * @date 2025-07-29 15:28:28
 */

import dayjs from 'dayjs';
import {Typography} from '@baidu/light-ai-react';
import {Tooltip, Button, Toast, Table, Link} from '@baidu/one-ui';
import {IconQuestionCircle, IconLink} from 'dls-icons-react';
import classNames from 'classnames';
import {copyText} from '@/utils/text/copy';
import {LogActionType, TaskIdEnum, TaskStatusEnum} from '../config';
import {useEscortIncentiveConfig} from '../context';
import {EscortButton} from '../components/Button';
import {TaskDetailData, TaskBasicInfo} from '../interface/task';
import {EscortCoupon, SimpleCoupons} from '../components/Coupons';
import './Task_OldBringNew.global.less';

function TaskEstimateTipOnCard() {
    return (
        <Typography.Markdown className="estimate-tip">
            邀请越多奖励越多
        </Typography.Markdown>
    );
}

function CouponLeft({value}: {value: number}) {
    return (
        <div className="old-bring-new-coupon-left">
            <div className="value">¥{value}</div>
            <div className="desc">最高券额</div>
        </div>
    );
}
function CouponRight() {
    return (
        <div className="old-bring-new-coupon-right">
            <div className="value">专属优惠券</div>
            <div className="desc">限时领取先到先得</div>
        </div>
    );
}
const couponSlots = {
    left: CouponLeft,
    right: CouponRight,
};
type TaskFeaturesOnCardProps = Pick<TaskBasicInfo, 'taskId' | 'rewardLimit' | 'endTime' | 'estimateData'> & {
    onAcceptTask: (taskId: number) => void;
    pendingCount: number;
    viewTaskDetail: (taskId: number) => void;
};
function TaskFeaturesOnCard({
    endTime,
    taskId,
    onAcceptTask,
    pendingCount,
    estimateData,
    viewTaskDetail,
}: TaskFeaturesOnCardProps) {
    const estimateCoupon = estimateData?.estimateCoupon || 0;
    return (
        <div>
            <EscortCoupon
                className="old-bring-new-coupons-limit"
                value={estimateCoupon}
                slots={couponSlots}
                contentWidth="42%"
            />
            <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                <div className="task-features">
                    限时优惠
                    <Tooltip title={`活动截止${dayjs(endTime).format('YYYY-MM-DD')}`}>
                        <IconQuestionCircle />
                    </Tooltip>
                </div>
                <EscortButton
                    size="small"
                    onClick={() => {
                        onAcceptTask(taskId);
                        viewTaskDetail(taskId);
                    }}
                    loading={!!pendingCount}
                >
                    参与活动
                </EscortButton>
            </div>
        </div>
    );
}

type AcceptedTaskCardContentProps = Pick<
    TaskBasicInfo,
    'rewardLimit' | 'reportData' | 'extConfig' | 'indicatorCurrent' | 'taskIndicator' | 'endTime' | 'rewards' | 'status' | 'rewardsData' | 'estimateData'
>;
function AcceptedTaskCardContent({
    endTime,
    status,
    reportData,
    estimateData,
    rewardsData,
}: AcceptedTaskCardContentProps) {
    const {inviteUsers = []} = reportData || {};
    const totalInvite = inviteUsers.length;
    const totalInviteSuccess = inviteUsers.filter(item => item.inviteStatus === 1).length;
    const totalInvitePayCount = inviteUsers.filter(item => item.hasPay === 1).length;

    const estimateCoupon = estimateData?.estimateCoupon || 0;
    const rewards = rewardsData?.rewards || 0;
    const formattedEndTime = dayjs(endTime).format('YYYY-MM-DD');

    switch (status) {
        case TaskStatusEnum.accepted:
            return (
                <AcceptedTaskContent
                    formattedEndTime={formattedEndTime}
                    estimateCoupon={estimateCoupon}
                />
            );

        case TaskStatusEnum.notMeetTaskLimit:
            return (
                <TaskStatusInfo
                    leftContent={
                        <>
                            <div className="invite-status">
                                已邀请{totalInvite}位客户，
                                <span className="invite-pay-count">{totalInvitePayCount}位</span>已产生消费
                            </div>
                            <div className="task-features error medium">
                                截止日期{formattedEndTime}
                            </div>
                        </>
                    }
                    rightContent={
                        <>
                            <div className="top-tips">预计可获得</div>
                            <div className="money">¥{rewards}</div>
                            <div className="result">成功邀请:{totalInviteSuccess}/{totalInvite}</div>
                        </>
                    }
                />
            );

        case TaskStatusEnum.completed:
            return (
                <TaskStatusInfo
                    leftContent={
                        <>
                            <div className="invite-status">预计10月31日前完成发放</div>
                            <div className="task-finished">活动结束</div>
                        </>
                    }
                    rightContent={
                        <>
                            <div className="top-tips">预计可获得</div>
                            <div className="money">¥{rewardsData?.rewards || 0}</div>
                            <div className="result">成功邀请:{totalInviteSuccess}/{totalInvite}</div>
                        </>
                    }
                />
            );


        default:
            return null;
    }
}

// ✅ 子组件 - 接受任务状态
function AcceptedTaskContent({formattedEndTime, estimateCoupon}: {estimateCoupon: number, formattedEndTime: string }) {
    return (
        <div>
            <TaskEstimateTipOnCard />
            <EscortCoupon
                className="old-bring-new-coupons-limit"
                value={estimateCoupon}
                slots={couponSlots}
                contentWidth="42%"
            />
            <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                <div className="task-features">
                    限时优惠
                    <Tooltip title={`活动截止${formattedEndTime}`}>
                        <IconQuestionCircle />
                    </Tooltip>
                </div>
                <EscortButton size="small">立即邀请</EscortButton>
            </div>
        </div>
    );
}

// ✅ 子组件 - 公共布局
function TaskStatusInfo({leftContent, rightContent}: { leftContent: React.ReactNode, rightContent: React.ReactNode }) {
    return (
        <div className="invite-status-container">
            <div className="left">{leftContent}</div>
            <div className="right">{rightContent}</div>
        </div>
    );
}

function SubTaskProgressOnCard() {
    return null;
}

type TaskDetailContentProps = Pick<
    TaskDetailData,
    'extConfig' | 'taskCurrentValue' | 'taskTargetValue' | 'reportData' | 'rewardLimit' | 'currentRewardsCount'
    | 'userTaskUpdateTime' | 'estimateData' | 'rewardsData'
>;

const columns = [
    {
        title: '公司名称',
        dataIndex: 'username',
        key: 'username',
    },
    {
        title: '客户类型',
        dataIndex: 'type',
        key: 'type',
    },
    {
        title: '是否邀请成功',
        dataIndex: 'ifSuccess',
        key: 'ifSuccess',
    },
    {
        title: '备注',
        dataIndex: 'other',
        key: 'other',
    },
];

const Mark = Typography.Mark;

const isMobile = /Mobi|Android|iPhone/i.test(navigator.userAgent) || document.body.clientWidth <= 500;

function TaskDetailContent({
    userTaskUpdateTime,
    reportData,
    estimateData,
    rewardsData,
}: TaskDetailContentProps) {
    const inviteUsers = reportData?.inviteUsers || [];
    const data = inviteUsers.map((item, index) => ({
        key: index,
        username: item.inviteCompany,
        type: item.custType === 1 ? '个人' : '企业',
        ifSuccess: item.inviteStatus === 1 ? '是' : '否',
        other: item.inviteFailReason || '',
    }));
    const totalInvite = inviteUsers.length; // 可能用得到
    const totalInvitePayCount = inviteUsers.filter(item => item.hasPay === 1).length;
    const estimateCoupon = estimateData?.estimateCoupon || 0;
    const rewards = rewardsData?.rewards || 0;
    return (
        <div className="task-detail-content">
            <div className="task-content-target">
                <div className="task-content-target-title">
                    <div className="title">邀请客户列表</div>
                    {
                        userTaskUpdateTime && (
                            <div className="task-update-time">数据更新时间：{userTaskUpdateTime}</div>
                        )
                    }
                </div>
                <div className="task-content-target-info">
                    <div className="info">
                        <div className="left">
                            已邀请<Mark status="warning" className="bold-num">{totalInvite}</Mark>个客户，其中<Mark status="warning" className="bold-num">{totalInvitePayCount}</Mark>个客户已产生消费，预计可获得<span style={{color: '#FF5500'}}>¥{rewards}</span>优惠券
                        </div>
                        <div className="right">限时领取先到先得</div>

                    </div>
                    <div className="table-detail">
                        <Table
                            columns={columns}
                            dataSource={data}
                            showHeader
                            pagination={false}
                        />
                    </div>
                </div>
                <Typography.Markdown className="old-bring-new-task-statement">
                    {`
1. 活动周期：2025年7月1日至9月30日
2. 可参与任务的客户：营销联络中心近90天有消费客户
3. 优惠券不可与框架优惠叠加，优惠券可以转移给同一客户的其他账户
4. 邀请1名新客户可获得1张1000元5折优惠券，最多获得5张1000元优惠券（成功邀请5个新客户，成功定义：新客在联中开户并上线，新客需要符合新开口径，近90天日均=0）
5. 优惠券类型：优惠券为5折折扣券，适用于搜索推广、信息流推广、知识营销产品线
6. 奖池有限，先到先得，奖励发完活动提前结束
                    `}
                </Typography.Markdown>
            </div>
        </div>
    );
}

function HistoryTaskTip({
    status,
    taskId,
    rewardsData,
}: {
    status: TaskStatusEnum;
    taskId: TaskIdEnum;
    rewardsData: {
        coupon: number;
        isSameCid: boolean;
        rewards?: number;
    };
}) {
    const {log} = useEscortIncentiveConfig();
    const onLinkClick = () => {
        log(LogActionType.TASKS_HISTORY_VIEW_COUPONS, {taskId});
    };
    return (
        <div>
            {
                status === TaskStatusEnum.completedAndGetCoin && (
                    <div className="effect">
                        恭喜获得<SimpleCoupons value={rewardsData.rewards || 0} size="small" />
                        <span className="effect-text">5折优惠券一张</span>，
                        <span onClick={onLinkClick}>
                            <Link
                                type="strong"
                                target="_blank"
                                size="small"
                                toUrl="https://eyouhui.baidu.com/polaris-web/front-advertisers/#/home/<USER>"
                            >
                                去查看
                            </Link>
                        </span>
                    </div>
                )
            }
            {
                status === TaskStatusEnum.completed && rewardsData?.isSameCid && (
                    <div className="effect">同主体下其他账户已获得优惠券</div>
                )
            }
        </div>
    );
}

function AcceptedTaskCardTitle({title}: TaskBasicInfo) {
    return <div className="title old-bring-new-title">{title}</div>;
}

function generateLink(userId: string | number) {
    const encoded = btoa(String(userId));
    return `https://qingge.baidu.com/mobile/taskInvite?userId=${encoded}`;
}

function TaskDetailTitle({title, daysRemaining, endTime, reportData}: {title: string, daysRemaining: number, reportData: TaskDetailData['reportData'], endTime: string}) {
    const {inviteUsers = []} = reportData || {};
    const totalInviteSuccess = inviteUsers.filter(item => item.inviteStatus === 1).length;
    const {userId} = useEscortIncentiveConfig();
    function onCopyLink() {
        // 链接失效（邀请成功的客户已达到上限5个；或者任务到期） ；endTime
        const overTime = new Date().getTime() > new Date(endTime).getTime();
        if (totalInviteSuccess >= 5 || overTime) {
            Toast.error({content: '链接失效'});
            return;
        }
        const link = generateLink(userId);
        copyText(link, {
            successMessage: '邀请链接复制成功，1秒后跳转邀请页面',
        });
        // 5s后跳转到邀请页面
        setTimeout(() => {
            window.location.href = link;
        }, 1000);
    }
    return (
        <div className={classNames('task-detail-container-title', {
            'task-detail-container-title-mobile': isMobile,
        })}
        >
            <div className="task-detail-name">{title}</div>
            <div className="task-detail-status">
                {isMobile ? (
                    <>
                        <div className="text">9月30日24点截止，剩余<div className="days-remaining">{daysRemaining}天</div></div>
                        <div className="days copy-link" onClick={onCopyLink}>
                            复制专属链接邀请客户
                        </div>
                    </>
                ) : (
                    <>
                        <Button type="text-strong" icon={<IconLink style={{color: '#3A5BFD'}} />} onClick={onCopyLink}>复制专属链接邀请客户</Button>
                        <div className="days">
                            9月30日24点截止，剩余<div className="days-remaining">{daysRemaining}天</div>
                        </div>
                    </>
                )}
            </div>
        </div>
    );
}

export const TaskOldBringNewSlots = {
    SubTaskProgressOnCard,
    AcceptedTaskCardContent,
    TaskEstimateTipOnCard,
    TaskFeaturesOnCard,
    TaskDetailContent,
    HistoryTaskTip,
    TaskDetailTitle,
    AcceptedTaskCardTitle,
};
