.task-pc-coupon {
    margin: 8px 0;

    &-amount {
        width: 90px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        &-value {
            color: #822D02;
            font-family: 'Baidu Number Plus';
            font-weight: 700;

            .unit {
                font-size: 14px;
            }

            .number {
                font-size: 20px;
            }
        }

        &-label {
            font-size: 12px;
            color: #8B6553;
        }
    }

    &-tip {
        &-top {
            font-size: 14px;
            color: #822D02;
            font-weight: 700;
            height: 28px;
            line-height: 28px;
        }

        &-bottom {
            font-size: 12px;
            color: #8B6553;
        }
    }
}

.task-reward-pool-remain {
    min-width: 96px;
    height: 18px;
    border-radius: 6px;
    background: linear-gradient(90deg, rgba(255, 135, 92, 0.4) 0%, rgba(255, 101, 55, 0.4) 100%);
    padding: 1px 4px;
    position: relative;

    .task-reward-pool-remain-progress {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        background: linear-gradient(90deg, #FF875C 0%, #FF6537 100%);
        border-radius: 6px;
    }

    .task-reward-pool-remain-text {
        position: relative;
        color: #FFF;
        font-weight: 500;
        font-size: 12px;
        text-align: center;
    }
}

.task-detail-progress-introduce {
    border: 1px solid #FFD6C74D;
    background: linear-gradient(90deg, #FCF5E0 0%, #FCF3D7 100%);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 8px;
    position: relative;

    .introduce-title {
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        color: #822D02;
    }

    .introduce-content {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: #8B6553;

        .coin-number {
            font-family: Baidu Number;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            color: #FF6638;
            padding: 0 4px;
        }
    }

    .coin-bg {
        position: absolute;
        bottom: -44px;
        right: -28px;
    }
}
