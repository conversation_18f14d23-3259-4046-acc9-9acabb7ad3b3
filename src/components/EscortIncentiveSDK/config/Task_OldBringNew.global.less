.old-bring-new-task-statement {
    color: #545B66;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    margin-top: 16px;
}

.old-bring-new-coupons-limit {
    margin: 8px 0;
}

.old-bring-new-title {
    display: inline-block;
}

.old-bring-new-task-rewards-limit-tip {
    color: #FF6537;
}

.bold-num {
    font-weight: 500;
    color: #0E0F11;
}

.task-content-target-info {
    .info {
        display: flex;
        justify-content: space-between;
    }

    .right {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 14px;
        color: #545B66;
    }

    .left {
        font-size: 14px;
        color: #545B66;
        font-weight: 400;
    }
}

.invite-status-container {
    display: flex;
    justify-content: space-between;

    .invite-status {
        font-size: 12px;
        color: #545B66;
        margin-top: 8px;

        .invite-pay-count {
            color: #FF6638;
        }
    }

    .task-finished {
        margin-top: 8px;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 10px;
        width: 52px;
        height: 16px;
        background-color: #EBEDF5;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        color: #848B99;
    }

    .left {
        width: 178px;
        padding-right: 7px;
    }

    .right {
        width: 74px;
        transform: translateY(-10px);
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .top-tips {
            font-size: 12px;
            transform: scale(0.8333);
            color: #848B99;
            text-align: center;
            font-weight: 400;
        }

        .money {
            text-align: center;
            color: #FF6638;
            font-weight: 500;
            font-size: 22px;
        }

        .result {
            width: max-content;
            color: #FF6638;
            font-weight: 400;
            font-size: 12px;
            text-align: center;
        }
    }
}

.old-bring-new-coupon-left {
    display: flex;
    flex-direction: column;
    align-items: center;

    .value {
        font-family: Baidu Number Plus;
        font-weight: 700;
        font-size: 20px;
        line-height: 22px;
        color: #822D02;
    }

    .desc {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: #8B6553;
    }
}

.old-bring-new-coupon-right {
    display: flex;
    flex-direction: column;
    align-items: center;

    .value {
        font-weight: 600;
        font-size: 14px;
        line-height: 20px;
        color: #822D02;
    }

    .desc {
        font-weight: 400;
        font-size: 12px;
        line-height: 16px;
        color: #8B6553;
    }
}

.task-detail-container-title-mobile {
    .task-detail-status {
        display: block !important;
    }

    .text {
        display: flex;
        font-size: 14px;
        color: #545B66;
    }

    .copy-link {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 40px;
        border-radius: 10px !important;
        margin-top: 10px;
    }
}
