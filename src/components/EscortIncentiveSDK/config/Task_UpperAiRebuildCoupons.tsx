/* eslint-disable max-len */
import {useMemo} from 'react';
import dayjs from 'dayjs';
import {Typography, Progress, Tag} from '@baidu/light-ai-react';
import {Tooltip, Link} from '@baidu/one-ui';
import {IconQuestionCircle} from 'dls-icons-react';
import {appendQuery} from '@/utils/route';
import {AixProgress} from '@/components/common/progress';
import {LogActionType, TaskStatusEnum} from '../config';
import {useEscortIncentiveConfig} from '../context';
import {EscortButton} from '../components/Button';
import {getCurrentProgressPercent} from '../components/TaskDetail/util';
import {TaskDetailData, TaskBasicInfo} from '../interface/task';
import {SimpleCoupons, EscortCoupon} from '../components/Coupons';
import './Task_UpperAiRebuildCoupons.global.less';

function TaskEstimateTipOnCard() {
    return (
        <Typography.Markdown className="estimate-tip">
            AI重构投放，科学调优助力效果提升
        </Typography.Markdown>
    );
}

function CouponLeft({value}: {value: number}) {
    return (
        <div className="upper-aiRebuild-coupon-left">
            <div className="value">¥{value}</div>
            <div className="desc">最高券额</div>
        </div>
    );
}
function CouponRight() {
    return (
        <div className="upper-aiRebuild-coupon-right">
            <div className="value">专属优惠券</div>
            <div className="desc">消费越多，返券越多</div>
        </div>
    );
}
const couponSlots = {
    left: CouponLeft,
    right: CouponRight,
};
type TaskFeaturesOnCardProps = Pick<TaskBasicInfo, 'taskId' | 'rewardLimit' | 'endTime'> & {
    onAcceptTask: (taskId: number) => void;
    pendingCount: number;
};
function UpperAiRebuildCouponsTaskFeaturesOnCard({
    endTime,
    taskId,
    rewardLimit,
    onAcceptTask,
    pendingCount,
}: TaskFeaturesOnCardProps) {
    return (
        <div>
            <EscortCoupon
                className="upper-aiRebuild-coupons-limit"
                value={rewardLimit}
                slots={couponSlots}
                contentWidth="42%"
            />
            <div style={{display: 'flex', justifyContent: 'space-between', alignItems: 'center'}}>
                <div className="task-features">
                    限时优惠
                    <Tooltip title={`活动截止${dayjs(endTime).format('YYYY-MM-DD')}`}>
                        <IconQuestionCircle />
                    </Tooltip>
                </div>
                <EscortButton size="small" onClick={() => onAcceptTask(taskId)} loading={!!pendingCount}>
                    参与活动
                </EscortButton>
            </div>
        </div>
    );
}

type AcceptedTaskCardContentProps = Pick<
    TaskBasicInfo,
    'rewardLimit' | 'reportData' | 'extConfig' | 'indicatorCurrent' | 'taskIndicator' | 'endTime' | 'rewards'
>;
function AcceptedTaskCardContent({
    reportData,
    extConfig,
    rewardLimit,
    indicatorCurrent,
    taskIndicator,
    endTime,
    rewards,
}: AcceptedTaskCardContentProps) {
    const currentProgressPercent = Math.min(Math.floor((indicatorCurrent / taskIndicator) * 100), 100);
    return (
        <div>
            <div className="content-desc">
                {
                    reportData.intelligentRenovationProjess === 1
                        ? <Typography.Markdown>活动期内未使用【智能翻新】功能，无法获得专属奖励</Typography.Markdown>
                        : (
                            reportData.intelligentRenovationProjess === 2
                                ? <Typography.Markdown>活动期内翻新项目/计划消费＜7天，无法获得专属奖励</Typography.Markdown>
                                : (
                                    <Typography.Markdown>
                                        {
                                            `已累计消费现金:mark[${reportData.cashAfterAddTask || 0}]{status="warning" size="small"}元，`
                                            + (
                                                reportData.cashAfterAddTask < extConfig.threshold
                                                    ? `未达到门槛，再消费现金:mark[${extConfig.threshold - reportData.cashAfterAddTask}]{status="warning" size="small"}元即可获得奖励`
                                                    : `预计可得:mark[${rewards}]{status="warning"}元优惠券`
                                            )
                                        }
                                    </Typography.Markdown>
                                )
                        )
                }
            </div>
            <AixProgress
                className="task-card-progress"
                width="auto"
                style={{padding: '12px 0 12px'}}
                value={[
                    {
                        percent: currentProgressPercent,
                        style: {background: 'linear-gradient(90deg, #FFDD67 0%, #FF870F 100%)'},
                    },
                ]}
                marks={[
                    {
                        percent: currentProgressPercent,
                        type: 'dot',
                        overlay: (
                            <ProgressExplanation
                                taskCurrentValue={indicatorCurrent}
                                extConfig={extConfig}
                                taskTargetValue={taskIndicator}
                                currentRewardsCount={rewards}
                                rewardLimit={rewardLimit}
                            />
                        ),
                    },
                    {
                        percent: Math.floor((extConfig.threshold / taskIndicator) * 100),
                        type: reportData.cashAfterAddTask < extConfig.threshold ? 'dot' : 'checked-dot',
                    },
                ]}
            />
            <div className="task-features error medium">
                截止日期{dayjs(endTime).format('YYYY-MM-DD')}
            </div>
        </div>
    );
}

function SubTaskProgressOnCard() {
    return null;
}

type TaskDetailContentProps = Pick<
    TaskDetailData,
    'extConfig' | 'taskCurrentValue' | 'taskTargetValue' | 'reportData' | 'rewardLimit' | 'currentRewardsCount'
    | 'userTaskUpdateTime'
>;
function TaskDetailContent({
    extConfig,
    taskCurrentValue,
    taskTargetValue,
    reportData,
    rewardLimit,
    currentRewardsCount,
    userTaskUpdateTime,
}: TaskDetailContentProps) {
    const {userId, log} = useEscortIncentiveConfig();

    const START_PERCENT = 10;
    const PERCENT_LENGTH = 80;
    const MAX_PERCENT = 99;
    const currentProgressPercent = useMemo(
        () => getCurrentProgressPercent({
            START_PERCENT,
            PERCENT_LENGTH,
            MAX_PERCENT,
            stages: [{indicator: extConfig.threshold * 0.2}],
            taskCurrentValue,
            taskTargetValue,
        }),
        [taskCurrentValue, taskTargetValue, extConfig.threshold]
    );
    const thresholdProgressPercent = useMemo(
        () => getCurrentProgressPercent({
            START_PERCENT,
            PERCENT_LENGTH,
            MAX_PERCENT,
            stages: [{indicator: extConfig.threshold * 0.2}],
            taskCurrentValue: extConfig.threshold,
            taskTargetValue,
        }),
        [extConfig, taskTargetValue]
    );
    return (
        <div className="task-detail-content">
            <div className="task-content-target">
                <div className="task-content-target-title">
                    <div className="title">任务目标</div>
                    {
                        userTaskUpdateTime && (
                            <div className="task-update-time">数据更新时间：{userTaskUpdateTime}</div>
                        )
                    }
                </div>
                <div className="task-content-target-info">
                    <div className="task-content-progress">
                        <div className="task-progress-summary">
                            {
                                reportData.intelligentRenovationProjess === 1
                                    ? <Typography.Markdown>活动期内未使用【智能翻新】功能，无法获得专属奖励</Typography.Markdown>
                                    : (
                                        reportData.intelligentRenovationProjess === 2
                                            ? <Typography.Markdown>活动期内翻新项目/计划消费＜7天，无法获得专属奖励</Typography.Markdown>
                                            : (
                                                <Typography.Markdown>
                                                    {
                                                        `已累计消费现金:mark[${reportData.cashAfterAddTask || 0}]{status="warning" size="small"}元，`
                                                        + (
                                                            reportData.cashAfterAddTask < extConfig.threshold
                                                                ? `未达到门槛，再消费现金:mark[${extConfig.threshold - reportData.cashAfterAddTask}]{status="warning" size="small"}元即可获得奖励`
                                                                : `预计可得:mark[${currentRewardsCount}]{status="warning"}元优惠券`
                                                        )
                                                    }
                                                </Typography.Markdown>
                                            )
                                    )
                            }
                        </div>
                        <AixProgress
                            width="auto"
                            // @ts-ignore
                            style={{'--aix-progress-bgColor': 'linear-gradient(90deg, #FEF8DE 90%, rgb(255, 255, 255) 100%)'}}
                            value={[
                                {
                                    percent: currentProgressPercent,
                                    style: {background: 'linear-gradient(90deg, rgb(255, 255, 255) 0%, rgb(255, 221, 103) 20%, rgb(255, 135, 15) 100%)'},
                                },
                            ]}
                            marks={[
                                {percent: 0, text: '任务领取'},
                                {
                                    percent: thresholdProgressPercent,
                                    type: reportData.cashAfterAddTask < extConfig.threshold ? 'dot' : 'checked-dot',
                                    text: `任务门槛${extConfig.threshold}元`,
                                    textPlacement: 'bottom',
                                },
                                {
                                    percent: START_PERCENT + PERCENT_LENGTH,
                                    type: currentProgressPercent < (START_PERCENT + PERCENT_LENGTH) ? 'dot' : 'checked-dot',
                                    text: `消费达${taskTargetValue}元`,
                                    textPlacement: 'bottom',
                                    tip: (
                                        <div className="upper-aiRebuild-task-rewards-limit-tip">
                                            最高可得 <SimpleCoupons value={rewardLimit} />
                                        </div>
                                    ),
                                },
                                {
                                    percent: currentProgressPercent,
                                    type: 'button',
                                    variant: 'primary',
                                    text: `${taskCurrentValue}元`,
                                    textPlacement: 'inline',
                                    overlay: (
                                        <ProgressExplanation
                                            taskCurrentValue={taskCurrentValue}
                                            extConfig={extConfig}
                                            taskTargetValue={taskTargetValue}
                                            currentRewardsCount={currentRewardsCount}
                                            rewardLimit={rewardLimit}
                                        />
                                    ),
                                },
                            ]}
                        />
                    </div>
                </div>
                <Typography.Markdown
                    className="upper-aiRebuild-task-statement"
                    directives={{
                        linkto: () => {
                            const onTagClick = () => {
                                log(LogActionType.USE_SMART_RENOVATION);
                                window.open(
                                    appendQuery('//qingge.baidu.com/ad', {prompt: '帮我翻新账户', userId}),
                                    '_blank'
                                );
                            };
                            return (
                                <Tooltip title="基于历史物料，Al智能规划重构投放，同时增加优质物料衍生与推荐，新老物料合力提升老户基建水平，提升整账户投放效果">
                                    <Tag variant="text-bubble" onClick={onTagClick}>
                                        去使用智能翻新
                                    </Tag>
                                </Tooltip>
                            );
                        },
                    }}
                >
                    {`
1. 活动周期：2025年7月28日~2025年8月31日
2. 参与资格：7月1日后使用智能翻新功能且活动周期内翻新项目/计划消费天数≥7天 <linkto />
3. 现金消费满任务门槛值，超额部分即可获得优惠券，最高获得不超过${rewardLimit}元
4. 活动结束后，优惠券自动下发您领取任务活动的账户内，优惠券有效期90天，过期失效
5. 优惠券类型：优惠券为 5 折折扣券，适用于搜索推广、信息流推广、知识营销产品线
6. 本次活动考核的搜索推广、信息流推广、知识营销产品线剔除优惠券消费后的累计消费现金
7. 奖池有限，先到先得，奖励发完活动提前结束
                    `}
                </Typography.Markdown>
            </div>
        </div>
    );
}

function ProgressExplanation({
    taskCurrentValue,
    extConfig,
    taskTargetValue,
    currentRewardsCount,
    rewardLimit,
}: Pick<
    TaskDetailData,
    'taskCurrentValue' | 'taskTargetValue' | 'extConfig' | 'currentRewardsCount' | 'rewardLimit'
>) {
    const taskThreshold = extConfig?.threshold;
    return (
        <div className="progress-explanations">
            {
                taskCurrentValue < taskThreshold
                    ? (
                        <div className="progress-explanation-item">
                            <Progress value={Math.floor((taskCurrentValue / taskThreshold) * 100)}>
                                <Typography.Text number status="aux">
                                    {Math.floor((taskCurrentValue / taskThreshold) * 100)}
                                </Typography.Text>
                            </Progress>
                            <Typography.Text>
                                任务门槛 <Typography.Text strong>{taskCurrentValue}</Typography.Text>/{taskThreshold}元暂未达到
                            </Typography.Text>
                        </div>
                    )
                    : (
                        <div className="progress-explanation-item">
                            <Progress status="success" />
                            <Typography.Text>
                                任务门槛
                                <Typography.Text strong>{taskCurrentValue}</Typography.Text>/{taskThreshold}元已达到，积累奖励中
                            </Typography.Text>
                        </div>
                    )
            }
            {
                taskCurrentValue < taskTargetValue
                    ? (
                        <div>
                            <div className="progress-explanation-item">
                                <Progress value={Math.floor((taskCurrentValue / taskTargetValue) * 100)}>
                                    <Typography.Text number status="aux">
                                        {Math.floor((taskCurrentValue / taskTargetValue) * 100)}
                                    </Typography.Text>
                                </Progress>
                                <Typography.Text>累计消费现金达{taskCurrentValue}元，预计可得</Typography.Text>
                                <SimpleCoupons value={currentRewardsCount} />
                            </div>
                        </div>
                    )
                    : (
                        <div className="progress-explanation-item">
                            <Progress status="success" />
                            <Typography.Text>任务累计消费现金达{taskCurrentValue}元，已达到上限</Typography.Text>
                        </div>
                    )
            }
            {
                taskCurrentValue <= taskTargetValue && (
                    <div className="progress-explanation-item">
                        <Progress value={Math.floor((taskCurrentValue / taskTargetValue) * 100)}>
                            <Typography.Text number status="aux">
                                {Math.floor((taskCurrentValue / taskTargetValue) * 100)}
                            </Typography.Text>
                        </Progress>
                        <Typography.Text>
                            任务目标<Typography.Text strong>{taskCurrentValue}</Typography.Text>/{taskTargetValue}元最高可得
                        </Typography.Text>
                        <SimpleCoupons value={rewardLimit} />
                    </div>
                )
            }
        </div>
    );
}

function HistoryUpperAiRebuildCouponsTaskTip(
    {status, taskId, rewardsData}: Pick<TaskBasicInfo, 'status' | 'taskId' | 'rewardsData'>
) {
    const {log} = useEscortIncentiveConfig();
    const onLinkClick = () => {
        log(LogActionType.TASKS_HISTORY_VIEW_COUPONS, {taskId});
    };
    return (
        <div>
            {
                status === TaskStatusEnum.completedAndGetCoin && (
                    <div className="effect">
                        恭喜{rewardsData?.rewardUserName}账户获得
                        <SimpleCoupons value={rewardsData?.rewards || 0} size="small" />
                        <span className="effect-text">5折优惠券一张</span>，
                        <span onClick={onLinkClick}>
                            <Link
                                type="strong"
                                target="_blank"
                                size="small"
                                toUrl="https://eyouhui.baidu.com/polaris-web/front-advertisers/#/home/<USER>"
                            >
                                去查看
                            </Link>
                        </span>
                    </div>
                )
            }
        </div>
    );
}

function TaskDetailTitle({title, daysRemaining}: {title: string, daysRemaining: number}) {
    return (
        <div className="task-detail-container-title">
            <div className="task-detail-name">{title}</div>
            <div className="task-detail-status">
                <div className="days">
                    任务剩余<div className="days-remaining">{daysRemaining}天</div>
                </div>
            </div>
        </div>
    );
}

export const TaskUpperAiRebuildCouponsSlots = {
    SubTaskProgressOnCard,
    AcceptedTaskCardContent,
    TaskEstimateTipOnCard,
    TaskFeaturesOnCard: UpperAiRebuildCouponsTaskFeaturesOnCard,
    TaskDetailContent,
    HistoryTaskTip: HistoryUpperAiRebuildCouponsTaskTip,
    TaskDetailTitle,
};
