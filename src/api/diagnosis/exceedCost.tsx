import dayjs from 'dayjs';
import {request} from '@/utils/ajax';
import {UnionToIntersection} from '@/interface/common';
import {ScheduleItem} from '@/utils/schedule';
import {SchedulePriceFactor} from '@/interface/fcThreeAdForm/campaign';
import {FcBidTypeEnum, FcOcpcBidTypeEnum} from '@/dicts/ocpc';
import {FcLocationOptionsEnum} from '@/dicts/getLocations';
import {FcRegionTargetTypeEnum} from '@/interface/campaign';
import {FC_MARKET_TARGET} from '@/dicts/marketingTarget';
import {isDiagnosisOverCostTodayUser} from '@/utils/getFlag';
import {isVirOnline} from '@/utils';
import {DIAGNOSIS_DATE_FORMAT} from './consumeFluctuation';
import {
    DiagnosisInfo, DiagnosisInfoRange,
    DiagnosisScene, getDiagnosisInfo, getDiagnosisTriggerParams, ImproveBidInfo,
} from './common';

export const EXCEED_DATE_RANGE = 7;
const MAX_DATE_LENGTH = 91;

export const DEFAULT_EXCEED_COST_DATE: {
    diagnosisTime: [string, string];
    baseTime: [string, string];
} = {
    diagnosisTime: [
        dayjs().subtract(EXCEED_DATE_RANGE, 'day').format(DIAGNOSIS_DATE_FORMAT),
        dayjs().subtract(1, 'day').format(DIAGNOSIS_DATE_FORMAT),
    ],
    baseTime: [
        dayjs().subtract(EXCEED_DATE_RANGE + 7, 'day').format(DIAGNOSIS_DATE_FORMAT),
        dayjs().subtract(EXCEED_DATE_RANGE + 1, 'day').format(DIAGNOSIS_DATE_FORMAT),
    ],
};

export const getExceedCostReadyTimeRange = () => {
    return [
        dayjs().subtract(MAX_DATE_LENGTH - 1, 'day').format(DIAGNOSIS_DATE_FORMAT),
        dayjs().subtract(
            isDiagnosisOverCostTodayUser() ? 0 : 1,
            'day'
        ).format(DIAGNOSIS_DATE_FORMAT),
    ] as [string, string];
};


/**
 * 超成本诊断建议类型枚举
 */
export enum ExceedCostAdviceName {
    /**
     * 波动要素-(出价)提升出价
     */
    OVER_CHARGE_INCREASE_BID = 'OVER_CHARGE_INCREASE_BID',
    /**
     * 波动要素-(定向)拓宽[人群]、[投放时段]、[推广设备]、[推广地域]
     */
    WIDEN_CROWD = 'WIDEN_CROWD',
    OVER_CHARGE_RELAX_TIME = 'OVER_CHARGE_RELAX_TIME',
    OVER_CHARGE_EXPAND_REGION = 'OVER_CHARGE_EXPAND_REGION',
    OVER_CHARGE_MOD_PLAN_DEVICE = 'OVER_CHARGE_MOD_PLAN_DEVICE',
    /**
     * 波动要素-(定向) 高转化词榜单
     */
    OVER_CHARGE_HIGH_CV_WORD = 'OVER_CHARGE_HIGH_CV_WORD',
    /**
     * 波动要素-(定向) 高转化、高消费拓词
     */
    OVER_CHARGE_HIGH_CV_HIGH_PAY_WORD = 'OVER_CHARGE_HIGH_CV_HIGH_PAY_WORD',
    /**
     * 波动要素-(创意)优化创意
     */
    OPTIMIZE_CREATIVE = 'OPTIMIZE_CREATIVE',
    /**
     * 波动要素-(落地页)优化落地页
     * 建议根据转化率趋势优化落地页
     */
    OPTIMIZE_LANDING_PAGE = 'OPTIMIZE_LANDING_PAGE',
    /**
     * 波动要素-(转化)新增同行业常用目标转化类型
     * 建议新增同行业常用目标转化类型
     */
    ADD_TRANS_TYPE = 'ADD_TRANS_TYPE',
    // (投放更精准的定向人群 )  "本期不做"
}


/**
 * FE config 后端不返回但是前端会使用
 */

export enum CreativeOptimizeFeConfig {
    CREATIVE_ADD_TEXT = 'FE_CREATIVE_ADD_TEXT',
    CREATIVE_ADD_IMAGE = 'FE_CREATIVE_ADD_IMAGE',
    CREATIVE_ADD_VIDEO = 'FE_CREATIVE_ADD_VIDEO',
    CREATIVE_HIGH_CTR_COMP = 'FE_CREATIVE_HIGH_CTR_COMP',
}

export enum HighConversionKeywordFeConfig {
    KEYWORD_DOWNLOAD = 'FE_KEYWORD_DOWNLOAD',
}

export interface AdviceLevelItem {
    adviceName: ExceedCostAdviceName;
    level: number;
    tab: string;
}

export interface ExceedCostResponse {
    text: string;
    processId: string;
    adviceLevelList: AdviceLevelItem[];
    diagnosisInfo: DiagnosisInfo;
    isProjectOvercharge: boolean;
    transPrice: number;
    deepTransPrice: number;
    /**
     * 浅层出价
     */
    setCpa: number;
    /**
     * 深层出价
     */
    deepSetCpa: number;
    overchargeRatio: number;
    /**
     * 是否开启轮询深度思考结果
     */
    isDeepThink: boolean;
}


export async function getExceedCostDiagnosisResult(params: DiagnosisInfoRange) {
    const result = await request<ExceedCostResponse>(
        'ad-diagnosis-agent/GET/AdDiagnosisService/getProjectOverchargeResult',
        {
            projectId: params.projectId,
            ...getDiagnosisInfo({
                diagnosisTime: params.diagnosisTime,
                baseTime: params.baseTime,
                diagnosisScene: params.diagnosisScene || DiagnosisScene.projectOverCharge,
            }),
            ...getDiagnosisTriggerParams(params.triggerSource),
        },
    );
    return {...params, ...result};
}

enum SexType {
    /**
     * 不限
     */
    UNLIMITED = 0,
    /**
     * 男
     */
    MALE = 1,
    /**
     * 女
     */
    FEMALE = 2,
}
interface CrowdAIXType {
    /**
     * 人群id
     */
    crowdId: number;
    /**
     * 人群名称
     */
    crowdName: string;
    /**
     * 年龄
     */
    age: number[];
    /**
     * 性别
     */
    sex: SexType;
    /**
     * ID包
     */
    idPack: number[];
    /**
     * 人群定向方式
     */
    crowdDirectType: number;
    /**
     * 人群生效类型
     */
    effectType: number;
    /**
     * 年龄类型
     */
    ageType?: number;
    /**
     * 轻舸标记字段，轻舸计划绑定人群会打这个标记
     */
    crowdAIXStatus: number;
    /**
     * 转化时间窗口
     */
    recentDays: number;
    /**
     * 转化层级
     */
    conversionLevel?: number;
    /**
     * 兴趣
     */
    inPeople: number[];
    /**
     * 自定义年龄
     */
    customAge?: number[];
    /**
     * 判断当前的请求是否来自idmp
     */
    idmpStatus?: boolean;
}

interface RateTrendInfo {
    date: string[];
    rate: number[];
}

export interface DiagnosisNarrowingPlan {
    /**
     * 方案 id
     */
    campaignId: number;
    /**
     * 方案名称
     */
    campaignName: string;
    /**
     * 近一周消费
     */
    cost: number;
    /**
     * 近一周展现
     */
    impression: number;
    /**
     * 近一周点击量
     */
    click: number;
    /**
     * 投放地域
     */
    regionTarget: number[];
    /** 营销目标id */
    marketingTargetId: FC_MARKET_TARGET;
    /** 地域类型 */
    regionTargetType: FcRegionTargetTypeEnum;
    /** 地域系数 */
    regionPriceFactor: Array<{
        regionId: number;
        priceFactor: number;
    }>;
    /** 地理位置状态 */
    geoLocationStatus: FcLocationOptionsEnum;
    /**
     * 未投放时段
     */
    schedule: ScheduleItem[];

    campaignBidType: FcBidTypeEnum;
    campaignOcpcBidType: FcOcpcBidTypeEnum;

    /**
     * 时段价格系数
     */
    schedulePriceFactors: SchedulePriceFactor[];
    /**
     * 人群定向
     */
    crowdAixExcludeAiTypes?: CrowdAIXType[];
    /**
     * 排除人群-已转化用户
     */
    crowdAixExcludConvertedTypes?: CrowdAIXType[];
    /**
     * 定向人群-基本属性（包含性别、年龄等）
     */
    crowdAixTargetBaseTypes?: CrowdAIXType[];

}

export enum QualityEnum {
    low = 1,
    average = 2,
    good = 3,
    noData = 4,
}

export interface SuggestLandingPage {
    campaignId: number;
    campaignName: string;
    adgroupId: number;
    adgroupName: string;
    topImpressionIncMax: number;
    topImpressionCurrent: number;
    businessPointId: number;
    adType: number;
    marketingTargetId: number;
    keywordCount: number;
    keyword: string;
    keywordMobileDestinationUrl: string;
    keywordMobileDestinationUrlCount: number;
    creativeMobileDestinationUrl: string;
    creativeMobileDestinationUrlCount: number;
    quality: QualityEnum;
    addTime: number;
    status: number;
}


interface ExceedCostMetricMap {
    [ExceedCostAdviceName.OVER_CHARGE_INCREASE_BID]: {
        improveBidMetric: {
            improveBidInfo: ImproveBidInfo[];
            suggestBidInfo: ImproveBidInfo;
            adoptionRate: number;
        };
    };
    [ExceedCostAdviceName.OVER_CHARGE_RELAX_TIME]: {
        relaxBidTimeMetric: {
            narrowTimePlanNum: number;
            topFluctuationDropPlanNames: string[];
            narrowingPlanList: DiagnosisNarrowingPlan[];
        };
    };
    [ExceedCostAdviceName.OVER_CHARGE_EXPAND_REGION]: {
        expandRegionMetric: {
            narrowRegionNum: number;
            topFluctuationDropPlanNames: string[];
            narrowingPlanList: DiagnosisNarrowingPlan[];
        };
    };
    [ExceedCostAdviceName.OVER_CHARGE_MOD_PLAN_DEVICE]: {
        adjustPlanPromotionDeviceMetric: {
            noWisePlanCnt: number; // 未投放移动设备方案数
            noPcPlanCnt: number; // 未投放pc设备方案数
            hasModCampaignEquipmentConversion: boolean; // 是否存在优化中心卡片
        };
    };
    [ExceedCostAdviceName.WIDEN_CROWD]: {
        expandCrowdMetric: {
            narrowingPlanList: DiagnosisNarrowingPlan[];
        };
    };
    [ExceedCostAdviceName.OVER_CHARGE_HIGH_CV_WORD]: {
        addHighConversionKeywordMetric: {
            keywordSummaryInfo: {
                keywordCount: number;
                hasPayKeywordCount: number;
                hasCvKeywordCount: number;
                hasPayKeywordCountCompareTrade: number;
                hasCvKeywordCountCompareTrade: number;
                hasPayKeywordRatio: number;
                hasCvKeywordRatio: number;
            };
            businessPointId: number;
        };
    };
    [ExceedCostAdviceName.OVER_CHARGE_HIGH_CV_HIGH_PAY_WORD]: {
        addHighConversionHighPayKeywordMetric: {
            keywordSummaryInfo: {
                keywordCount: number;
                hasPayKeywordCount: number;
                hasCvKeywordCount: number;
                hasPayKeywordCountCompareTrade: number;
                hasCvKeywordCountCompareTrade: number;
                hasPayKeywordRatio: number;
                hasCvKeywordRatio: number;
            };
            highConvHighPayKeywordInfo: {
                highConvHighPayKeywordRatio: number;
                highConvHighPayKeywordAvgRatio: number;
            };
            businessPointId: number;
        };
    };
    [ExceedCostAdviceName.OPTIMIZE_CREATIVE]: {
        optimizeCreativeMetric: {
            creativeCtrInfo: {
                titleCtrTrendInfo: RateTrendInfo;
                descCtrTrendInfo: RateTrendInfo;
                imageCtrTrendInfo: RateTrendInfo;
                videoCtrTrendInfo: RateTrendInfo;
                componentCtrTrendInfo: RateTrendInfo;
            };
            creativeSuggestInfo: {
                suggestComps: Array<{
                    name: string;
                    ctr: number;
                    url: string;
                }>;
            };
        addCreativeTextMetric: {
            topTexts: string[];
        };
        addCreativeImageMetric: {
            topImages: string[];
        };
        addCreativeVideoMetric: {
            topVideos: Array<{
                videoImgUrl: string;
                videoUrl: string;
            }>;
        };
        };
    };
    [ExceedCostAdviceName.OPTIMIZE_LANDING_PAGE]: {
        optimizeLandingPageMetric: {
            cvrInfo: {
                projectCvrTrendInfo: {
                    date: string[];
                    rate: number[];
                };
                tradeCvrTrendInfo: {
                    date: string[];
                    rate: number[];
                };
                secondTradeName: string;
            };
            suggestLandingPages: SuggestLandingPage[];
        };
    };
    [ExceedCostAdviceName.ADD_TRANS_TYPE]: {
        addTransTypeMetric: {
            projectTransTypes: Array<{
                transTypeName: string;
                conversions: number;
            }>;
        };
    };
}


export type IExceedCostAdviceDetailItem<T extends ExceedCostAdviceName[]> = {
    userId: number;
    projectId: number;
    processId: string;
} & UnionToIntersection<
    T[number] extends infer Item
    ? Item extends keyof ExceedCostMetricMap ? ExceedCostMetricMap[Item] : Record<string, never>
    : Record<string, never>
>;

export function getExceedCostDetailBatch<T extends ExceedCostAdviceName[]>(params: DiagnosisInfoRange & {
    adviceTypeList: T;
    projectId: number;
    processId: string;
}) {
    return request<IExceedCostAdviceDetailItem<T>>('ad-diagnosis-agent/GET/AdDiagnosisService/getAdviceDetailBatch', {
        ...getDiagnosisInfo(params),
        adviceTypeList: params.adviceTypeList,
        projectId: params.projectId,
        processId: params.processId,
    });
}


interface GetSetCpaTrendResponse {
    date: string[];
    setCpaTrend: number[];
    deepSetCpaTrend: number[];
    transPriceTrend: number[];
    deepTransPriceTrend: number[];
    ocpcConversionsTrend: number[];
    deepOcpcConversionsTrend: number[];
}
export function getSetCpaTrend(params: DiagnosisInfoRange) {
    return request<GetSetCpaTrendResponse>(
        'ad-diagnosis-agent/GET/AdDiagnosisService/getSetCpaTrend',
        {
            ...getDiagnosisInfo(params),
            projectId: params.projectId,
        },
    );
}


interface GetProjectOverchargeDeepThinkResultParams {
    projectId: number;
    processId: string;
}


export enum DecisionResultStatus {
    /** loading */
    LOADING = 'LOADING',
    /** 命中异常分支 */
    ABNORMAL = 'ABNORMAL',
    /** 未命中异常分支 */
    NORMAL = 'NORMAL',
}

interface GetProjectOverchargeDeepThinkResultResponse {
    text: string;
    decisionResultStatus: DecisionResultStatus;
}
function getProjectOverchargeDeepThinkResult(params: GetProjectOverchargeDeepThinkResultParams) {
    return request<GetProjectOverchargeDeepThinkResultResponse>(
        'ad-diagnosis-agent/GET/AdDiagnosisService/getProjectOverchargeDeepThinkResult',
        params,
    );
}

/**
 * 轮询获取深度思考结果
 * @param params 轮询参数
 * @param onUpdate 数据更新回调
 * @param onComplete 轮询完成回调
 * @returns 清理函数
 */
export function pollDeepThinkResult(
    params: {
        projectId: number;
        processId: string;
    },
    onUpdate: (text: string) => void,
    onComplete?: () => void
): () => void {
    const pollingIntervals = isVirOnline ? [30, 20, 10] : [2, 2, 1]; // 轮询间隔时间（秒），超出长度后取最后一个
    const maxPollCount = 20; // 最大轮询次数
    let currentPollCount = 0;
    let timeoutId: NodeJS.Timeout;

    const poll = () => {
        getProjectOverchargeDeepThinkResult(params).then((res: GetProjectOverchargeDeepThinkResultResponse) => {
            onUpdate(res.text);
            if (res.decisionResultStatus !== DecisionResultStatus.LOADING) {
                // 轮询真正完成，调用完成回调
                onComplete?.();
                return;
            }

            // 如果还在loading状态且未达到最大轮询次数，继续轮询
            if (currentPollCount < maxPollCount) {
                // 取对应索引的间隔时间，超出数组长度时取最后一个
                const nextInterval = pollingIntervals[Math.min(currentPollCount, pollingIntervals.length - 1)];
                currentPollCount++;
                timeoutId = setTimeout(poll, nextInterval * 1000);
            }
            else {
                onUpdate('请求超时，请稍后再试');
            }
        }).catch((error: any) => {
            console.error('轮询获取深度思考结果失败:', error);
        });
    };

    // 开始轮询
    poll();

    // 返回清理函数
    return () => {
        if (timeoutId) {
            clearTimeout(timeoutId);
        }
    };
}
