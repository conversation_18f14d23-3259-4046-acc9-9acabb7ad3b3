/**
 * @file 列表配置查询api
 * <AUTHOR>
 * @date 2021/08/12
 */
import {mapValues, flatten, isEmpty} from 'lodash-es';
import {request} from '@/utils/ajax';
import {materialToken as token, FIX_TYPE} from '@/config/tableList/config';
import {MaterialList} from '@/interface/tableList';
import getAlign from '@/utils/materialList/getAlign';
import {ReportConfigResponse} from '@/interface/report';
import {getColumnsFormatter, getDefaultColumnWidth} from '@/utils/tableList/columns';

export const enum BaseTemplateIdEnum {
    DEFAULT = -1,
    CUSTOM = 0
}
export function calcCustomColumns(data: ReportConfigResponse): string[] {
    const {
        defaultVisibleColumns: defaultColumns,
        customColumns: originColumns,
        templateId,
        templateColumns = [],
    } = data;
    let customColumns = templateColumns;
    if (templateId === BaseTemplateIdEnum.DEFAULT) {
        customColumns = defaultColumns;
    } else if (templateId === BaseTemplateIdEnum.CUSTOM || !templateId) {
        // 不走marspro接口的config data中(如关键词新建列表、计划设置列表)，templateId不存在，取customColumns
        customColumns = originColumns.length > 0 ? originColumns : defaultColumns;
    }

    return customColumns;
}

export function normalizeToConfiguration(data: ReportConfigResponse): MaterialList.ColumnConfiguration {
    // 根据report返回数据初始化column配置
    const {
        defaultVisibleColumns: defaultColumns,
        columnCategories: oldColumnCategories = [],
        newColumnCategories = [],
        columnConfigs: originColumnConfig = {},
        columnGroups = [],
        haveDataColumns = [],
        splitable = false,
        splitColumns = [],
        customColumns: originColumns = [],
        smartFilterColumns,
        smartFilterable,
        templateId,
    } = data;
    const columnCategories = isEmpty(newColumnCategories) ? oldColumnCategories : newColumnCategories;
    const customColumns = calcCustomColumns(data);

    // 映射成自定义列组件需要的字段
    const groups = columnCategories.map((category, index) => {
        const {name, columns = [], subCategories = []} = category;
        const partFields = haveDataColumns.filter(column => columns.includes(column));
        return {
            id: index,
            label: name, // 中文描述
            ...(isEmpty(subCategories) ? {
                fields: columns, // columns配置数组
                partFields, // 筛选有数据的column数组
            } : {
                children: subCategories.map(subCategory => {
                    const {name: subColumnName, columns: subColumns = []} = subCategory;
                    const subPartFields = haveDataColumns.filter(column => subColumns.includes(column));
                    return {
                        id: subColumnName,
                        label: subColumnName,
                        fields: subColumns,
                        partFields: subPartFields,
                    };
                }),
            }),
        };
    });
    const allColumns = flatten(groups.map(group => {
        if ('children' in group) {
            return flatten(group.children.map(child => child.fields));
        }
        return group.fields;
    }));

    const columnConfigs = mapValues(originColumnConfig, conf => {
        const {
            columnText,
            optional,
            columnType,
            sortable,
            feConfig = {},
            commentKey, // 新版小问号key，每一列有没有配置，都有可能存在无效key
        } = conf;
        const {
            fixType,
            requestTo,
            columnWidth, // 列宽
            columnMinWidth, // 最小列宽
            draggable = true, // 是否可拖拽
            composites, // 后端业务上分组，例如：设备出价设置包含设备出价基准和设备出价系数
            align,
            filter,
            tipKeyName,
        } = feConfig;
        const childrenColumnKeys = composites ? composites.split(',') : []; // 带有分组的情况

        const defaultColumnsWidth = getDefaultColumnWidth(columnText.length, columnType);
        return {
            ...conf,
            label: columnText, // 中文描述
            removable: optional, // 是否可选
            draggable: !fixType,
            columnDraggable: draggable,
            fixLeft: fixType === FIX_TYPE.left,
            fixType,
            sorter: !!sortable,
            width: columnWidth || defaultColumnsWidth,
            minWidth: columnMinWidth || 76,
            requestTo,
            tipKeyName: commentKey || tipKeyName,
            childrenColumnKeys,
            align: getAlign({columnType, compArray: childrenColumnKeys, align}),
            filter: filter ? JSON.parse(filter) : {},
        };
    });

    return {
        columnConfigs,
        customColumns,
        defaultColumns,
        columnCategories: groups,
        columnGroups,
        allColumns,
        splitable,
        splitColumns,
        templateId,
        smartFilterColumns,
        smartFilterable,
    };
}

interface FetchMaterialListConfigurationParams {
    reportType: number;
    [x: string]: any;
}
export async function fetchMaterialListConfiguration({reportType, ...extParams}: FetchMaterialListConfigurationParams) {
    const result = await request<ReportConfigResponse>(
        'marsPro/GET/ReportConfigService/getReportConfig',
        {
            token,
            reportType,
            dataAnalysis: true,
            ...extParams,
        }
    );
    return normalizeToConfiguration(result);
}

interface ModMaterialListConfigurationParams {
    reportType: number;
    columns: string[];
    withNewCategory?: boolean;
}
export async function modMaterialListConfiguration({
    reportType,
    columns,
    withNewCategory = false,
}: ModMaterialListConfigurationParams) {
    const result = await request<ReportConfigResponse>(
        'marsPro/GET/ReportConfigService/saveCustomColumns',
        {
            token,
            reportType,
            columns,
            withNewCategory,
        }
    );
    return normalizeToConfiguration(result);
}

type FormatColumnConfigurationParams = Pick<ReportConfigResponse, 'columnConfigs' | 'customColumns'>
    & Partial<Pick<ReportConfigResponse, 'defaultVisibleColumns'>>;
export function formatColumnConfiguration({
    customColumns,
    columnConfigs,
    defaultVisibleColumns,
}: FormatColumnConfigurationParams) {
    const initialConfiguration = normalizeToConfiguration({
        defaultVisibleColumns: defaultVisibleColumns || customColumns,
        customColumns: customColumns,
        columnConfigs,
    });
    const formatColumns = getColumnsFormatter(initialConfiguration.columnConfigs);
    const tableColumns = formatColumns(customColumns);
    return {...initialConfiguration, tableColumns, allColumns: Object.keys(columnConfigs)};
}
