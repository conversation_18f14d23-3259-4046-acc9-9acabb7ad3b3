import {FcProviderForQingge} from 'commonLibs/qinggeProvider';
import {initialQinggeHairuo} from 'commonLibs/utils/initialQinggeHairuo';
import ReactDOM from 'react-dom';
import {Router} from 'react-router-dom';
import 'regenerator-runtime/runtime';
import {PageTips} from '@baidu/one-ui-pro';
import {BoundaryConfigProvider} from 'react-suspense-boundary';
import App from '@/modules/App';
import {isDev, getToken, getOperatorId, getRedirectUrl} from '@/utils';
import {getTongjiScript} from '@/utils/logger';
import globalData from '@/utils/globalData';
import {prefetchRequestByWindow, getPrefetchParamsByName} from '@/utils/ajax/prefetch';
import {fetchAixAccountInfo} from '@/api/getAccountInfo';
import {FeedBasicData, formatBasicDataApiData} from '@/api/feedBasicData';
import {BasicInfo} from '@/hooks/contexts/basicInfo';
import {WeirwoodErrorBoundary} from '@/components/WeirwoodErrorBoundary';
import {addCustomResTimingLogger, ensureError} from '@/utils/logger/weirwood';
import '@/styles';
import {fetchFcAccountInfo} from '@/api/manage/fcAccountsetting';
import {isHasFeedAuth} from '@/dicts';
import fetchFeedUserInfo from '@/api/getFeedUserInfo';
import {LoggersContext, loggers} from '@/logger';
import {WithWeirwoodCaughtBoundaryConfigProvider} from '../hoc/WithWeirwoodCaughtBoundaryConfigProvider';
import history from './history';

const onErrorCaught = (error: any) => {
    // @ts-ignore
    const __Weirwood = window && window.__Weirwood;
    if (__Weirwood) {
        const safeError = ensureError(error);
        __Weirwood.error.setContext({errorPage: true}, {persist: false});
        __Weirwood.error.captureException(safeError);
    }
};

const parseCookies = () => {
    const token = getToken();
    const userId = getOperatorId();
    if (token != null && userId != null) {
        return {token, userId: +userId};
    }
    return null;
};

export async function fetchFcBasicInfo() {
    const cookies = initialQinggeHairuo();
    const userId = cookies?.userId;

    const apiPath = 'puppet/GET/BasicInfoFunction/getBasicInfo';
    const data = await prefetchRequestByWindow(
        '__PRELOAD_API_QINGGE___puppet/GET/BasicInfoFunction/getBasicInfo',
        {
            path: apiPath, params: {userId},
        }
    );
    const {accountAuth = {}, ...basicInfo} = data;
    const writable = accountAuth.writable;
    return {writable, basicInfo, product: 'fc', qnAppId: 1, cookies};
}

async function fetchFeedBasicData() {
    const name = '__PRELOAD_API_QINGGE___carinae/GET/BasicWebService/basicData';
    const result = await prefetchRequestByWindow(
        name,
        {
            path: 'carinae/GET/BasicWebService/basicData',
            params: getPrefetchParamsByName(name),
        }
    );
    return formatBasicDataApiData(result as FeedBasicData);
}

async function fetchFeedBasicInfo() {
    const cookies = initialQinggeHairuo();
    const userId = cookies?.userId;
    const result = await prefetchRequestByWindow(
        '__PRELOAD_API_QINGGE___carinae/GET/BasicWebService/basicInfo',
        {
            path: 'carinae/GET/BasicWebService/basicInfo',
            params: {
                userId,
            },
        }
    );
    return result;
}

const main = () => {
    const cookies = parseCookies();
    if (!isDev && cookies == null) {
        window.location.href = getRedirectUrl();
        return;
    }
    document.body.style.overscrollBehaviorX = 'none';

    getTongjiScript();

    const root = document.createElement('div');
    root.id = 'root';

    Promise.all([
        prefetchRequestByWindow(
            '__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBizAuth',
            {path: 'aurora/GET/BizAuthService/getBizAuth', params: {}}
        ),
        prefetchRequestByWindow(
            '__PRELOAD_API_QINGGE___aurora/GET/BizAuthService/getBasicInfo',
            {path: 'aurora/GET/BizAuthService/getBasicInfo', params: {}}
        ),
        fetchFcAccountInfo(),
        fetchAixAccountInfo(),
        fetchFcBasicInfo(),
        fetchFeedBasicData(),
        fetchFeedBasicInfo(),
    ]).then(async ([authRes, hagRes, fcAccountInfo, accountInfo_, fcBasicData, feedBasicData, feedBasicInfo]) => {
        const {accountInfo} = authRes;
        const {
            tradeInfo, bizHags, aixAccountInitInfo, aixUserAlreadyMove,
            aixExistingMaterialInfo, existingMaterialInfo, optHags,
            unitInfo,  canUseSmartAdBuild
        } = hagRes;
        const basicInfo = {
            ...authRes,
            ...hagRes,
            accountInfo: {...accountInfo, ...accountInfo_},
        } as BasicInfo;
        globalData.set({
            writable: basicInfo.accountAuth.writable,
            tradeInfo: tradeInfo || {},
            accountInfo: {...accountInfo, ...accountInfo_},
            aixAccountInitInfo,
            fcBasicData,
            bizHags,
            optHags,
            aixExistingMaterialInfo,
            unitInfo,
            canUseSmartAdBuild,
            existingMaterialInfo,
            feedBasicData,
            feedBasicInfo,
            aixUserAlreadyMove,
            optAccountRoles: fcBasicData.basicInfo.accountInfo?.optAccountRoles,
        });
        let feedUserInfo = null;
        if (isHasFeedAuth()) {
            feedUserInfo = await fetchFeedUserInfo();
            globalData.set({feedUserInfo});
        }
        addCustomResTimingLogger([{
            customPerfName: 'x_qingge_baseinfo_api_duration',
            isMultiple: true,
            // eslint-disable-next-line max-len
            isSendResourceTiming: ({name}) => /BizAuthService|getAccountInfo|getBasicInfo|BasicWebService|UserWebService/.test(name),
        }]);
        ReactDOM.render(
            <WeirwoodErrorBoundary>
                <Router history={history}>
                    <LoggersContext.Provider value={{loggers}}>
                        <FcProviderForQingge fcBasicData={fcBasicData}>
                            <BoundaryConfigProvider
                                onErrorCaught={onErrorCaught}
                                pendingFallback={null}
                                renderError={() => null}
                            >
                                <App basicInfo={basicInfo} fcAccountInfo={fcAccountInfo} feedUserInfo={feedUserInfo} />
                            </BoundaryConfigProvider>
                        </FcProviderForQingge>
                    </LoggersContext.Provider>
                </Router>
            </WeirwoodErrorBoundary>,
            document.body.appendChild(root)
        );
    }).catch((err: any) => {
        console.error(err);

        // 接口重定向 不渲染错误提示页面
        if (err?.status === 940) {
            return null;
        }

        return err.redirect
            ? null
            : ReactDOM.render(
                <WithWeirwoodCaughtBoundaryConfigProvider>
                    <PageTips.Error type="frontEnd" />
                </WithWeirwoodCaughtBoundaryConfigProvider>,
                document.body.appendChild(root)
            );
    });
};

export default main;

if (!isDev) {
    main();
}
