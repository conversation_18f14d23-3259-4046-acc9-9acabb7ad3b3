/**
 * @file 登录页
 * <AUTHOR>
 * @desc https://ku.baidu-int.com/d/SvqUM7ESrrEmxG
 */

import ReactDOM from 'react-dom';
import qs from 'query-string';
import {request} from '@/utils/ajax';
import Login from '@/modules/Login';
import '@/styles';
import {WithWeirwoodCaughtBoundaryConfigProvider} from './hoc/WithWeirwoodCaughtBoundaryConfigProvider';

declare const window: Window & typeof globalThis & {
    ucCommonLogin: {
        init: (params: object) => void;
    };
};

const main = () => {
    const root = document.createElement('div');
    root.id = 'home';

    // 背景：活动链接要和自然流量区分开，比如：https://qingge.baidu.com/act/2024，它配了 nginx 重定向：
    // https://qingge.baidu.com/login?act=2024。该链接的参数在登陆后（或者已经是登陆态）要传给 /ad
    const {fromu, ...rest} = qs.parse(location.search);
    const successJumpPath = Object.keys(rest).length ? `/ad/overview?${qs.stringify(rest)}` : '/hairuo/main.do';

    window.ucCommonLogin.init({
        container: 'uc-login-container',
        appid: 756,
        fromu: fromu || (window.location.origin + successJumpPath),
        staticPage: window.location.origin + '/v3Jump-stable.html',
        multiLogin: 0,
        isSmall: 0,
        isCanCodeLogin: true,
        faqLink: '//aq.baidu.com/new/#/school',
        defaultLogin: 'uc',
        defaultCssVersion: 3,
        defaultCss: true,
        smsFirst: true,
        // passUseOptions: {loginVersion: 'v4', defaultCss: true},
        // passLoginOptions: {sms: 5},
        ucTitle: '百度营销账号',
        // passportTitle: '尚未开户',
        proxyUc: true,
    });

    request(
        'aurora/GET/BizAuthService/getBizAuth',
        {},
        {
            onSuccess: res => {
                if ('redirect' in res) {
                    window.location.href = 'https://www2.baidu.com';
                    throw res;
                }
                window.location.href = successJumpPath;
                return res.data;
            },
            onFailure: error => {
                throw error;
            },
            errorRetryCount: 0,
            toastOnFailure: false,
        }
    ).catch(() => {
        ReactDOM.render(
            <WithWeirwoodCaughtBoundaryConfigProvider>
                <Login />
            </WithWeirwoodCaughtBoundaryConfigProvider>,
            document.body.appendChild(root)
        );
    });
};

main();
