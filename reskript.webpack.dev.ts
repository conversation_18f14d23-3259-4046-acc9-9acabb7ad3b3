/**
 * @file: webpack dev config
 * <AUTHOR>
 * @date 2024-07-15 15:08:30
 */
import fs from 'fs';
import {isObject} from 'lodash-es';
import webpack from 'webpack';
import {configure} from '@reskript/settings';
import chalk from 'chalk';
// // @ts-ignore
// import CircularDependencyPlugin from 'circular-dependency-plugin';
import sharedDeps from './src/config/sharedDeps';
import {proxyConfig, HAIRUO_PATH, DEV_PORT} from './debug/proxyConfig';
import serverMiddleWare, {httpProxyConfig} from './debug/middleware/server';
import {PROXY_PATH_REWRITE, remotesForDev} from './debug/remote';

/* eslint-disable no-console */
// 打印出所有的信息
console.log(chalk.bgGreen(proxyConfig?.target ? `当前代理到${proxyConfig.target}` : '本地开发环境'));
console.log(chalk.bgGreen(`搜索微前端配置：${process.env.FC_REMOTE_STAGE}`));
console.log(chalk.bgGreen(`信息流微前端配置：${process.env.FEED_REMOTE_STAGE}`));
console.log(chalk.green('微前端代理到'));
console.log(chalk.green(`${JSON.stringify(PROXY_PATH_REWRITE, null, 2)}`));
console.log('----------------------------------');
console.log('如需修改，请到 .env.local 中修改');
/* eslint-enable no-console */
const errPaths: any[] = [];
export default configure(
    'webpack',
    {
        build: {
            appTitle: '轻舸',
            finalize: buildConfig => {
                if (process.env.DEV_CACHE !== undefined) {
                    buildConfig.cache = Boolean(process.env.DEV_CACHE);
                }
                buildConfig.devtool = 'source-map';
                buildConfig.module.rules.forEach(
                    rule => {
                        if (isObject(rule) && rule.test?.toString() === '/\\.[jt]sx?$/') {
                            rule.resolve = {fullySpecified: false};
                        }
                    }
                );
                buildConfig.plugins = buildConfig.plugins.filter((plugin: any) => plugin?.name !== 'reskript:css-bind');
                buildConfig.plugins.push(
                    new webpack.container.ModuleFederationPlugin({
                        name: 'fecangjieapp',
                        filename: 'cangjieApp/remoteEntry.js',
                        remotes: remotesForDev,
                        shared: sharedDeps,
                    }),
                );
                buildConfig.plugins.push(
                    new webpack.DefinePlugin({
                        'process.env': {
                            STAGE: JSON.stringify(process.env.STAGE),
                        },
                    })
                );
                // buildConfig.plugins.push(
                //     new CircularDependencyPlugin({
                //         onStart() {
                //             errPaths = [];
                //         },
                //         // 排除检测符合正则的文件
                //         exclude: /node_modules/,
                //         // 向 webpack 输出错误而不是警告
                //         failOnError: false,
                //         // 允许包含异步导入的循环
                //         // 举例：via import(/* webpackMode: "weak" */ './file.js')
                //         allowAsyncCycles: false,
                //         // 设置当前的工作目录以显示模块路径
                //         cwd: process.cwd(),
                //         onDetected({paths}: any) {
                //             errPaths.push(paths);
                //         },
                //         onEnd() {
                //             // 输入到文件中
                //             const sortedByJson = errPaths.sort((a, b) =>
                //                 JSON.stringify(a).localeCompare(JSON.stringify(b))
                //             );
                //             fs.writeFileSync(
                //                 './circular.json',
                //                 JSON.stringify(sortedByJson, null, 2),
                //                 'utf8'
                //             );
                //         },
                //     })
                // );
                return buildConfig;
            },
        },
        devServer: {
            ...(
                proxyConfig
                    ? {
                        defaultProxyDomain: `${proxyConfig.target}`,
                        https: {
                            proxy: proxyConfig.https,
                        },
                    }
                    : {}
            ),
            proxyRewrite: PROXY_PATH_REWRITE,
            customizeMiddleware: serverMiddleWare,
            port: DEV_PORT,
            apiPrefixes: [HAIRUO_PATH],
            hot: true,
            finalize: devServerConfig => {
                devServerConfig.allowedHosts = 'all';
                if (devServerConfig.client) {
                    (devServerConfig.client as any).overlay = false;
                }
                if (proxyConfig) {
                    if (typeof (devServerConfig as any)?.proxy?.[HAIRUO_PATH] === 'object') {
                        (devServerConfig as any).proxy[HAIRUO_PATH] = {
                            ...((devServerConfig as any).proxy[HAIRUO_PATH]),
                            ...httpProxyConfig,
                        };
                    }
                }
                if (devServerConfig.client) {
                    (devServerConfig.client as any).overlay = false;
                }
                return devServerConfig;
            },
        },
    }
);
